#!/bin/bash

# Start MCP servers script
set -e

echo "🚀 Starting MCP servers..."

# Wait for Docker to be available
echo "⏳ Waiting for Dock<PERSON> to be ready..."
while ! docker info > /dev/null 2>&1; do
    sleep 1
done

# Check if MCP containers are running
echo "🔍 Checking MCP container status..."

containers=(
    "mcp-brave-search"
    "mcp-tavily"
    "mcp-firecrawl"
    "mcp-context7"
    "mcp-notion"
    "mcp-desktop-commander"
    "mcp-taskmaster"
    "mcp-supabase"
    "mcp-browser-tools"
    "mcp-magic"
    "mcp-neo4j"
)

for container in "${containers[@]}"; do
    if docker ps --format "table {{.Names}}" | grep -q "^${container}$"; then
        echo "✅ $container is running"
    else
        echo "❌ $container is not running"
        echo "🔄 Attempting to start $container..."
        docker start "$container" || echo "⚠️  Failed to start $container"
    fi
done

# Wait a bit for servers to initialize
echo "⏳ Waiting for MCP servers to initialize..."
sleep 10

# Run health check
echo "🏥 Running health check..."
if [ -f /workspace/mcp-config/health-check.js ]; then
    node /workspace/mcp-config/health-check.js
else
    echo "⚠️  Health check script not found"
fi

echo "🎉 MCP server startup complete!"
