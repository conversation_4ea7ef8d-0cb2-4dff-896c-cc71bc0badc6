import React, { useState, useEffect } from 'react'
import { useNavigate } from 'react-router-dom'
import { useBetterAuth } from '@/providers/BetterAuthProvider'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Loader2 } from 'lucide-react'

interface BetterAuthLoginProps {
  onSuccess?: () => void
  redirectTo?: string
}

export function BetterAuthLogin({ onSuccess, redirectTo = '/dashboard' }: BetterAuthLoginProps) {
  const [email, setEmail] = useState('')
  const [password, setPassword] = useState('')
  const [error, setError] = useState('')
  const [isLoading, setIsLoading] = useState(false)
  
  const { signIn, isAuthenticated } = useBetterAuth()
  const navigate = useNavigate()

  // Watch for authentication state changes after successful login
  useEffect(() => {
    if (isAuthenticated) {
      console.log('🔍 Authentication confirmed, navigating to:', redirectTo)
      if (onSuccess) {
        onSuccess()
      } else {
        navigate(redirectTo)
      }
    }
  }, [isAuthenticated, navigate, redirectTo, onSuccess])

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setError('')
    setIsLoading(true)

    try {
      console.log('🔍 Attempting login with:', email)
      const result = await signIn(email, password)
      console.log('🔍 Login result:', result)

      if (result.error) {
        console.error('❌ Login error:', result.error)
        setError(result.error.message || 'Login failed')
      } else {
        console.log('✅ Login successful, waiting for session update...')
        // Don't navigate immediately - let useEffect handle it when isAuthenticated becomes true
      }
    } catch (err) {
      console.error('❌ Unexpected error:', err)
      setError('An unexpected error occurred')
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <div className="flex items-center justify-center min-h-screen bg-gray-50">
      <Card className="w-full max-w-md">
        <CardHeader>
          <CardTitle>Sign In</CardTitle>
          <CardDescription>
            Enter your credentials to access your account
          </CardDescription>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleSubmit} className="space-y-4">
            {error && (
              <Alert variant="destructive">
                <AlertDescription>{error}</AlertDescription>
              </Alert>
            )}
            
            <div className="space-y-2">
              <Label htmlFor="email">Email</Label>
              <Input
                id="email"
                type="email"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                required
                disabled={isLoading}
              />
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="password">Password</Label>
              <Input
                id="password"
                type="password"
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                required
                disabled={isLoading}
              />
            </div>
            
            <Button type="submit" className="w-full" disabled={isLoading}>
              {isLoading ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Signing in...
                </>
              ) : (
                'Sign In'
              )}
            </Button>
          </form>
        </CardContent>
      </Card>
    </div>
  )
}