const fetch = require('node-fetch');

const BASE_URL = process.env.BETTER_AUTH_URL || 'https://nxtdotx.co.za/api/auth';

const ADMIN_EMAIL = "<EMAIL>";

async function verifyAdminEmail() {
  try {
    // There is no standard public API for direct verification, so we try PATCH or similar pattern
    // Try a PATCH; if the API does not exist, this will fail and you may need a manual DB update.
    const resp = await fetch(`${BASE_URL}/users/email-verify`, {
      method: "PATCH",
      headers: { "Content-Type": "application/json" },
      body: JSON.stringify({
        email: ADMIN_EMAIL,
        verified: true
      })
    });
    const data = await resp.json();
    if (!resp.ok) {
      throw new Error(`Could not verify admin email. API responded: ${data?.error || JSON.stringify(data)}`);
    }
    console.log(`Admin user ${ADMIN_EMAIL} set to verified.`, data);
  } catch (err) {
    console.error("Failed to verify admin user email. You may need to update it manually in the Better Auth database.", err.message || err);
    process.exit(1);
  }
}

verifyAdminEmail();