import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
import { Component } from 'react';
class ErrorBoundary extends Component {
    constructor(props) {
        super(props);
        this.state = {
            hasError: false,
            error: null,
            errorInfo: null
        };
    }
    static getDerivedStateFromError(error) {
        // Update state so the next render will show the fallback UI
        return {
            hasError: true,
            error,
            errorInfo: null
        };
    }
    componentDidCatch(error, errorInfo) {
        // You can also log the error to an error reporting service
        console.error('ErrorBoundary caught an error', error, errorInfo);
        this.setState({
            error,
            errorInfo
        });
    }
    render() {
        if (this.state.hasError) {
            // You can render any custom fallback UI
            return this.props.fallback || (_jsxs("div", { className: "error-boundary p-6 bg-red-50 border border-red-200 rounded-lg", children: [_jsx("h2", { className: "text-xl font-bold text-red-800 mb-2", children: "Something went wrong" }), _jsxs("details", { className: "whitespace-pre-wrap text-red-700", children: [_jsx("summary", { className: "cursor-pointer mb-2", children: "View error details" }), _jsx("p", { className: "text-sm mb-2", children: this.state.error?.toString() }), _jsx("pre", { className: "text-xs bg-red-100 p-2 rounded overflow-auto", children: this.state.errorInfo?.componentStack })] })] }));
        }
        return this.props.children;
    }
}
export default ErrorBoundary;
