// src/lib/auth-client.ts - UPDATED
import { createAuthClient } from "better-auth/react"
import type { Session, User } from "./auth-shared"

const baseURL = import.meta.env.VITE_BETTER_AUTH_URL ||
  (import.meta.env.DEV ? 'http://localhost:8000/api/auth' : 'https://nxtdotx.co.za/api/auth');

console.log('🔍 Auth client using baseURL:', baseURL);

export const authClient = createAuthClient({
  baseURL,
  fetchOptions: {
    credentials: 'include',
  },
  debug: true,
})

export type { Session, User }

// Re-export auth utilities
export {
  getUserPermissions,
  hasPermission,
  hasAnyPermission,
  isRole,
  isRoleOrHigher
} from "./auth-shared"