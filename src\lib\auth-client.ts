// src/lib/auth-client.ts - UPDATED
import { createAuthClient } from "better-auth/react"
import type { Session, User } from "./auth-shared"

const baseURL = 'http://localhost:8000/api/auth';

console.log('🔍 Auth client using baseURL:', baseURL);

export const authClient = createAuthClient({
  baseURL,
  fetchOptions: {
    credentials: 'include',
  },
  debug: true,
})

export type { Session, User }

// Re-export auth utilities
export {
  getUserPermissions,
  hasPermission,
  hasAnyPermission,
  isRole,
  isRoleOrHigher
} from "./auth-shared"