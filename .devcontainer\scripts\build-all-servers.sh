#!/bin/bash

# Build all MCP servers script
set -e

echo "🏗️  Building all MCP servers..."

# Change to the devcontainer directory
cd .devcontainer

# Create remaining server directories first
echo "📁 Creating remaining server directories..."
bash scripts/create-remaining-servers.sh

# Build each MCP server
servers=(
    "brave-search"
    "tavily"
    "firecrawl"
    "context7"
    "notion"
    "desktop-commander"
    "taskmaster"
    "supabase"
    "browser-tools"
    "magic"
    "neo4j"
)

echo "🔨 Building MCP server Docker images..."

for server in "${servers[@]}"; do
    echo "Building $server..."
    if [ -d "mcp-servers/$server" ]; then
        docker build -t "mcp-$server:latest" "mcp-servers/$server/"
        echo "✅ Built mcp-$server:latest"
    else
        echo "⚠️  Directory mcp-servers/$server not found, skipping..."
    fi
done

echo "🎉 All MCP servers built successfully!"
echo ""
echo "📋 Available MCP servers:"
for server in "${servers[@]}"; do
    echo "  - mcp-$server (port 808${servers[@]/$server})"
done

echo ""
echo "🚀 To start all servers, run:"
echo "  docker-compose up -d"
echo ""
echo "🏥 To check server health, run:"
echo "  node ../mcp-config/health-check.js"
