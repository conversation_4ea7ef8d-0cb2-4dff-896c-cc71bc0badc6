import { jsx as _jsx, jsxs as _jsxs, Fragment as _Fragment } from "react/jsx-runtime";
import { useState } from 'react';
import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Table, FileDown, FileUp, RefreshCw } from "lucide-react";
import { toast } from "sonner";
const TableItem = ({ table, refetchTables }) => {
    const [exportLoading, setExportLoading] = useState(false);
    const [importLoading, setImportLoading] = useState(false);
    const handleExportData = async () => {
        try {
            setExportLoading(true);
            console.log(`Exporting data for table: ${table.name}`);
            // In a real implementation, this would call a Supabase function to export data
            // For demo purposes, we'll simulate a delay
            await new Promise(resolve => setTimeout(resolve, 1500));
            // Create a mock CSV content
            const csvContent = `id,name,status\n1,Item 1,active\n2,Item 2,inactive`;
            // Create a blob and download it
            const blob = new Blob([csvContent], { type: 'text/csv' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `${table.name}_export.csv`;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);
            toast.success(`Export for ${table.name} completed`);
        }
        catch (error) {
            toast.error(`Error exporting data from ${table.name}`);
            console.error('Export error:', error);
        }
        finally {
            setExportLoading(false);
        }
    };
    const handleImportData = async () => {
        try {
            setImportLoading(true);
            console.log(`Preparing import for table: ${table.name}`);
            // In a real implementation, this would open a file picker and process the file
            // For demo purposes, we'll simulate a delay and show a success message
            await new Promise(resolve => setTimeout(resolve, 1500));
            // Simulate a successful import
            toast.success(`Data imported successfully to ${table.name}`);
            // Refresh the tables data
            refetchTables();
        }
        catch (error) {
            toast.error(`Error importing data to ${table.name}`);
            console.error('Import error:', error);
        }
        finally {
            setImportLoading(false);
        }
    };
    return (_jsx(Card, { className: "shadow-sm", children: _jsx(CardContent, { className: "p-4", children: _jsxs("div", { className: "flex justify-between items-center", children: [_jsxs("div", { children: [_jsxs("div", { className: "flex items-center gap-2 mb-2", children: [_jsx(Table, { className: "h-4 w-4 text-primary" }), _jsx("h3", { className: "font-semibold text-lg", children: table.name })] }), _jsxs("div", { className: "grid grid-cols-3 gap-4 text-sm text-muted-foreground", children: [_jsxs("div", { children: [_jsx("span", { className: "font-medium", children: "Rows: " }), table.row_count.toLocaleString()] }), _jsxs("div", { children: [_jsx("span", { className: "font-medium", children: "Size: " }), table.size] }), _jsxs("div", { children: [_jsx("span", { className: "font-medium", children: "Last Vacuum: " }), table.last_vacuum] })] })] }), _jsxs("div", { className: "flex gap-2", children: [_jsx(Button, { size: "sm", variant: "outline", className: "flex items-center gap-1", onClick: handleExportData, disabled: exportLoading, children: exportLoading ? (_jsxs(_Fragment, { children: [_jsx(RefreshCw, { className: "h-4 w-4 animate-spin" }), "Exporting..."] })) : (_jsxs(_Fragment, { children: [_jsx(FileDown, { className: "h-4 w-4" }), "Export"] })) }), _jsx(Button, { size: "sm", variant: "outline", className: "flex items-center gap-1", onClick: handleImportData, disabled: importLoading, children: importLoading ? (_jsxs(_Fragment, { children: [_jsx(RefreshCw, { className: "h-4 w-4 animate-spin" }), "Importing..."] })) : (_jsxs(_Fragment, { children: [_jsx(FileUp, { className: "h-4 w-4" }), "Import"] })) })] })] }) }) }, table.name));
};
export default TableItem;
