import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
import { useState, useEffect } from 'react';
import { supabase } from '@/integrations/supabase/client';
import { useToast } from '@/hooks/use-toast';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Switch } from '@/components/ui/switch';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
const ModuleTogglePanel = ({ userId }) => {
    const [accessList, setAccessList] = useState([]);
    const [loading, setLoading] = useState(true);
    const [users, setUsers] = useState([]);
    const [selectedUser, setSelectedUser] = useState(userId);
    const { toast } = useToast();
    const fetchModuleAccess = async (uid) => {
        setLoading(true);
        try {
            const { data, error } = await supabase
                .from('user_module_access')
                .select('*')
                .eq('user_id', uid);
            if (error)
                throw error;
            setAccessList(data?.map(item => ({
                ...item,
                submenu_slug: item.submenu_slug || undefined,
                category: item.category || undefined
            })) || []);
        }
        catch (err) {
            console.error('Error fetching module access:', err);
            toast({
                title: "Error",
                description: "Failed to load module access settings",
                variant: "destructive"
            });
        }
        finally {
            setLoading(false);
        }
    };
    const fetchUsers = async () => {
        try {
            // In a real implementation, you'd fetch from the profiles table
            // This is a placeholder since we don't have direct access to auth.users
            const { data, error } = await supabase
                .from('user_roles')
                .select('user_id')
                .eq('role', 'admin');
            if (error)
                throw error;
            // For demonstration, we'll just use the user IDs
            // In a real app, you'd join with a profiles table
            setUsers(data.map(u => ({
                id: u.user_id,
                username: u.user_id.substring(0, 8) + '...'
            })));
        }
        catch (err) {
            console.error('Error fetching users:', err);
        }
    };
    useEffect(() => {
        fetchUsers();
        if (userId) {
            fetchModuleAccess(userId);
            setSelectedUser(userId);
        }
    }, [userId]);
    const handleUserChange = (uid) => {
        setSelectedUser(uid);
        fetchModuleAccess(uid);
    };
    const toggleAccess = async (id, currentValue) => {
        try {
            const { error } = await supabase
                .from('user_module_access')
                .update({ is_enabled: !currentValue })
                .eq('id', id);
            if (error)
                throw error;
            // Update local state
            setAccessList(prev => prev.map(item => item.id === id ? { ...item, is_enabled: !currentValue } : item));
            toast({
                title: "Access Updated",
                description: `Module access has been ${!currentValue ? 'enabled' : 'disabled'}`,
            });
        }
        catch (err) {
            console.error('Error toggling access:', err);
            toast({
                title: "Error",
                description: "Failed to update module access",
                variant: "destructive"
            });
        }
    };
    const addModuleAccess = async (event) => {
        event.preventDefault();
        const formData = new FormData(event.currentTarget);
        const moduleSlug = formData.get('moduleSlug');
        const submenuSlug = formData.get('submenuSlug');
        const category = formData.get('category');
        if (!moduleSlug) {
            toast({
                title: "Validation Error",
                description: "Module slug is required",
                variant: "destructive"
            });
            return;
        }
        try {
            const { data, error } = await supabase
                .from('user_module_access')
                .insert([{
                    user_id: selectedUser,
                    module_slug: moduleSlug,
                    submenu_slug: submenuSlug || undefined,
                    category: category || undefined,
                    is_enabled: true
                }])
                .select();
            if (error)
                throw error;
            setAccessList(prev => [...prev, {
                    ...data[0],
                    submenu_slug: data[0].submenu_slug || undefined,
                    category: data[0].category || undefined
                }]);
            toast({
                title: "Success",
                description: "Module access added successfully",
            });
            // Reset the form
            event.target.reset();
        }
        catch (err) {
            console.error('Error adding module access:', err);
            toast({
                title: "Error",
                description: "Failed to add module access",
                variant: "destructive"
            });
        }
    };
    return (_jsxs("div", { className: "space-y-8", children: [_jsxs(Card, { children: [_jsxs(CardHeader, { children: [_jsx(CardTitle, { children: "Module Access Control" }), _jsx(CardDescription, { children: "Manage which modules and submenus users can access" })] }), _jsxs(CardContent, { children: [_jsxs("div", { className: "mb-6", children: [_jsx(Label, { htmlFor: "userId", children: "Select User" }), _jsxs(Select, { value: selectedUser, onValueChange: handleUserChange, children: [_jsx(SelectTrigger, { className: "w-full", children: _jsx(SelectValue, { placeholder: "Select a user" }) }), _jsx(SelectContent, { children: users.map(user => (_jsx(SelectItem, { value: user.id, children: user.username }, user.id))) })] })] }), loading ? (_jsx("div", { className: "flex justify-center py-8", children: _jsx("div", { className: "w-8 h-8 border-t-2 border-b-2 border-gray-500 rounded-full animate-spin" }) })) : accessList.length === 0 ? (_jsx("div", { className: "text-center py-8 text-gray-500", children: "No module access settings found for this user" })) : (_jsx("div", { className: "space-y-4", children: accessList.map(access => (_jsxs("div", { className: "flex justify-between items-center border p-3 rounded-md", children: [_jsxs("div", { children: [_jsx("div", { className: "font-medium", children: access.module_slug }), access.submenu_slug && (_jsxs("div", { className: "text-sm text-gray-500", children: ["Submenu: ", access.submenu_slug] })), access.category && (_jsx(Badge, { variant: "outline", className: "mt-1", children: access.category }))] }), _jsx(Switch, { checked: access.is_enabled, onCheckedChange: () => toggleAccess(access.id, access.is_enabled) })] }, access.id))) }))] })] }), _jsxs(Card, { children: [_jsxs(CardHeader, { children: [_jsx(CardTitle, { children: "Add Module Access" }), _jsx(CardDescription, { children: "Grant access to a module for this user" })] }), _jsx(CardContent, { children: _jsxs("form", { onSubmit: addModuleAccess, className: "space-y-4", children: [_jsxs("div", { className: "grid gap-4 sm:grid-cols-2", children: [_jsxs("div", { className: "space-y-2", children: [_jsx(Label, { htmlFor: "moduleSlug", children: "Module Slug *" }), _jsx(Input, { id: "moduleSlug", name: "moduleSlug", placeholder: "e.g., dashboard", required: true })] }), _jsxs("div", { className: "space-y-2", children: [_jsx(Label, { htmlFor: "submenuSlug", children: "Submenu Slug (Optional)" }), _jsx(Input, { id: "submenuSlug", name: "submenuSlug", placeholder: "e.g., analytics" })] })] }), _jsxs("div", { className: "space-y-2", children: [_jsx(Label, { htmlFor: "category", children: "Category (Optional)" }), _jsxs(Select, { name: "category", children: [_jsx(SelectTrigger, { children: _jsx(SelectValue, { placeholder: "Select a category" }) }), _jsxs(SelectContent, { children: [_jsx(SelectItem, { value: "project", children: "Project" }), _jsx(SelectItem, { value: "admin", children: "Admin" }), _jsx(SelectItem, { value: "system", children: "System" })] })] })] }), _jsx(Button, { type: "submit", className: "w-full", children: "Add Access" })] }) })] })] }));
};
export default ModuleTogglePanel;
