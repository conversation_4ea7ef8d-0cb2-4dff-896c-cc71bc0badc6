import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
  Table, 
  TableBody, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from '@/components/ui/table';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  FileText, 
  Download, 
  Calendar,
  TrendingUp,
  TrendingDown,
  DollarSign,
  BarChart3,
  Loader2
} from 'lucide-react';
import { ReportService } from '../../services/financial/reportService';
import { ReportType } from '../../types/financial';

interface TrialBalanceItem {
  accountCode: string;
  accountName: string;
  accountType: string;
  debitBalance: number;
  creditBalance: number;
  totalDebits: number;
  totalCredits: number;
}

interface BalanceSheetData {
  assets: {
    currentAssets: number;
    fixedAssets: number;
    totalAssets: number;
  };
  liabilities: {
    currentLiabilities: number;
    longTermLiabilities: number;
    totalLiabilities: number;
  };
  equity: {
    ownersEquity: number;
    retainedEarnings: number;
    totalEquity: number;
  };
  asOfDate: Date;
}

interface IncomeStatementData {
  revenue: {
    operatingRevenue: number;
    otherRevenue: number;
    totalRevenue: number;
  };
  expenses: {
    costOfGoodsSold: number;
    operatingExpenses: number;
    otherExpenses: number;
    totalExpenses: number;
  };
  netIncome: number;
  grossProfit: number;
  operatingIncome: number;
  periodStart: Date;
  periodEnd: Date;
}

interface CashFlowData {
  operatingActivities: {
    netIncome: number;
    depreciation: number;
    accountsReceivableChange: number;
    accountsPayableChange: number;
    inventoryChange: number;
    netOperatingCashFlow: number;
  };
  investingActivities: {
    assetPurchases: number;
    assetSales: number;
    netInvestingCashFlow: number;
  };
  financingActivities: {
    debtProceeds: number;
    debtPayments: number;
    equityProceeds: number;
    dividendPayments: number;
    netFinancingCashFlow: number;
  };
  netCashFlow: number;
  beginningCash: number;
  endingCash: number;
  periodStart: Date;
  periodEnd: Date;
}

const FinancialReports: React.FC = () => {
  const [activeReport, setActiveReport] = useState<string>('balance-sheet');
  const [loading, setLoading] = useState(false);
  const [balanceSheet, setBalanceSheet] = useState<BalanceSheetData | null>(null);
  const [incomeStatement, setIncomeStatement] = useState<IncomeStatementData | null>(null);
  const [cashFlow, setCashFlow] = useState<CashFlowData | null>(null);
  const [trialBalance, setTrialBalance] = useState<TrialBalanceItem[]>([]);

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(amount);
  };

  const formatDate = (date: Date) => {
    return new Intl.DateTimeFormat('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    }).format(date);
  };

  const generateBalanceSheet = async () => {
    try {
      setLoading(true);
      const data = await ReportService.generateBalanceSheet(new Date());
      setBalanceSheet(data);
    } catch (error) {
      console.error('Error generating balance sheet:', error);
    } finally {
      setLoading(false);
    }
  };

  const generateIncomeStatement = async () => {
    try {
      setLoading(true);
      const startDate = new Date(new Date().getFullYear(), 0, 1); // Start of year
      const endDate = new Date(); // Today
      const data = await ReportService.generateIncomeStatement(startDate, endDate);
      setIncomeStatement(data);
    } catch (error) {
      console.error('Error generating income statement:', error);
    } finally {
      setLoading(false);
    }
  };

  const generateCashFlowStatement = async () => {
    try {
      setLoading(true);
      const startDate = new Date(new Date().getFullYear(), 0, 1); // Start of year
      const endDate = new Date(); // Today
      const data = await ReportService.generateCashFlowStatement(startDate, endDate);
      setCashFlow(data);
    } catch (error) {
      console.error('Error generating cash flow statement:', error);
    } finally {
      setLoading(false);
    }
  };

  const generateTrialBalance = async () => {
    try {
      setLoading(true);
      const data = await ReportService.generateTrialBalance(new Date());
      setTrialBalance(data);
    } catch (error) {
      console.error('Error generating trial balance:', error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    switch (activeReport) {
      case 'balance-sheet':
        generateBalanceSheet();
        break;
      case 'income-statement':
        generateIncomeStatement();
        break;
      case 'cash-flow':
        generateCashFlowStatement();
        break;
      case 'trial-balance':
        generateTrialBalance();
        break;
    }
  }, [activeReport]);

  const renderBalanceSheet = () => (
    <div className="space-y-6">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* Assets */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <TrendingUp className="h-5 w-5 text-green-600" />
              <span>Assets</span>
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex justify-between">
              <span>Current Assets</span>
              <span className="font-medium">{formatCurrency(balanceSheet?.assets.currentAssets || 0)}</span>
            </div>
            <div className="flex justify-between">
              <span>Fixed Assets</span>
              <span className="font-medium">{formatCurrency(balanceSheet?.assets.fixedAssets || 0)}</span>
            </div>
            <hr />
            <div className="flex justify-between font-bold">
              <span>Total Assets</span>
              <span>{formatCurrency(balanceSheet?.assets.totalAssets || 0)}</span>
            </div>
          </CardContent>
        </Card>

        {/* Liabilities & Equity */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <TrendingDown className="h-5 w-5 text-red-600" />
              <span>Liabilities & Equity</span>
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <h4 className="font-medium">Liabilities</h4>
              <div className="flex justify-between text-sm">
                <span>Current Liabilities</span>
                <span>{formatCurrency(balanceSheet?.liabilities.currentLiabilities || 0)}</span>
              </div>
              <div className="flex justify-between text-sm">
                <span>Long-term Liabilities</span>
                <span>{formatCurrency(balanceSheet?.liabilities.longTermLiabilities || 0)}</span>
              </div>
            </div>
            <div className="space-y-2">
              <h4 className="font-medium">Equity</h4>
              <div className="flex justify-between text-sm">
                <span>Owner's Equity</span>
                <span>{formatCurrency(balanceSheet?.equity.ownersEquity || 0)}</span>
              </div>
              <div className="flex justify-between text-sm">
                <span>Retained Earnings</span>
                <span>{formatCurrency(balanceSheet?.equity.retainedEarnings || 0)}</span>
              </div>
            </div>
            <hr />
            <div className="flex justify-between font-bold">
              <span>Total Liabilities & Equity</span>
              <span>{formatCurrency((balanceSheet?.liabilities.totalLiabilities || 0) + (balanceSheet?.equity.totalEquity || 0))}</span>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );

  const renderIncomeStatement = () => (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>Income Statement</CardTitle>
          <CardDescription>
            {incomeStatement && `${formatDate(incomeStatement.periodStart)} - ${formatDate(incomeStatement.periodEnd)}`}
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-2">
            <h4 className="font-medium text-green-600">Revenue</h4>
            <div className="flex justify-between">
              <span>Operating Revenue</span>
              <span>{formatCurrency(incomeStatement?.revenue.operatingRevenue || 0)}</span>
            </div>
            <div className="flex justify-between">
              <span>Other Revenue</span>
              <span>{formatCurrency(incomeStatement?.revenue.otherRevenue || 0)}</span>
            </div>
            <div className="flex justify-between font-medium border-t pt-2">
              <span>Total Revenue</span>
              <span>{formatCurrency(incomeStatement?.revenue.totalRevenue || 0)}</span>
            </div>
          </div>

          <div className="space-y-2">
            <h4 className="font-medium text-red-600">Expenses</h4>
            <div className="flex justify-between">
              <span>Cost of Goods Sold</span>
              <span>{formatCurrency(incomeStatement?.expenses.costOfGoodsSold || 0)}</span>
            </div>
            <div className="flex justify-between">
              <span>Operating Expenses</span>
              <span>{formatCurrency(incomeStatement?.expenses.operatingExpenses || 0)}</span>
            </div>
            <div className="flex justify-between">
              <span>Other Expenses</span>
              <span>{formatCurrency(incomeStatement?.expenses.otherExpenses || 0)}</span>
            </div>
            <div className="flex justify-between font-medium border-t pt-2">
              <span>Total Expenses</span>
              <span>{formatCurrency(incomeStatement?.expenses.totalExpenses || 0)}</span>
            </div>
          </div>

          <div className="space-y-2 border-t pt-4">
            <div className="flex justify-between">
              <span>Gross Profit</span>
              <span className="font-medium">{formatCurrency(incomeStatement?.grossProfit || 0)}</span>
            </div>
            <div className="flex justify-between">
              <span>Operating Income</span>
              <span className="font-medium">{formatCurrency(incomeStatement?.operatingIncome || 0)}</span>
            </div>
            <div className="flex justify-between font-bold text-lg border-t pt-2">
              <span>Net Income</span>
              <span className={incomeStatement?.netIncome && incomeStatement.netIncome >= 0 ? 'text-green-600' : 'text-red-600'}>
                {formatCurrency(incomeStatement?.netIncome || 0)}
              </span>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );

  const renderCashFlowStatement = () => (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>Cash Flow Statement</CardTitle>
          <CardDescription>
            {cashFlow && `${formatDate(cashFlow.periodStart)} - ${formatDate(cashFlow.periodEnd)}`}
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Operating Activities */}
          <div className="space-y-2">
            <h4 className="font-medium text-blue-600">Operating Activities</h4>
            <div className="flex justify-between text-sm">
              <span>Net Income</span>
              <span>{formatCurrency(cashFlow?.operatingActivities.netIncome || 0)}</span>
            </div>
            <div className="flex justify-between text-sm">
              <span>Depreciation</span>
              <span>{formatCurrency(cashFlow?.operatingActivities.depreciation || 0)}</span>
            </div>
            <div className="flex justify-between text-sm">
              <span>Accounts Receivable Change</span>
              <span>{formatCurrency(cashFlow?.operatingActivities.accountsReceivableChange || 0)}</span>
            </div>
            <div className="flex justify-between text-sm">
              <span>Accounts Payable Change</span>
              <span>{formatCurrency(cashFlow?.operatingActivities.accountsPayableChange || 0)}</span>
            </div>
            <div className="flex justify-between text-sm">
              <span>Inventory Change</span>
              <span>{formatCurrency(cashFlow?.operatingActivities.inventoryChange || 0)}</span>
            </div>
            <div className="flex justify-between font-medium border-t pt-2">
              <span>Net Operating Cash Flow</span>
              <span>{formatCurrency(cashFlow?.operatingActivities.netOperatingCashFlow || 0)}</span>
            </div>
          </div>

          {/* Investing Activities */}
          <div className="space-y-2">
            <h4 className="font-medium text-purple-600">Investing Activities</h4>
            <div className="flex justify-between text-sm">
              <span>Asset Purchases</span>
              <span>{formatCurrency(cashFlow?.investingActivities.assetPurchases || 0)}</span>
            </div>
            <div className="flex justify-between text-sm">
              <span>Asset Sales</span>
              <span>{formatCurrency(cashFlow?.investingActivities.assetSales || 0)}</span>
            </div>
            <div className="flex justify-between font-medium border-t pt-2">
              <span>Net Investing Cash Flow</span>
              <span>{formatCurrency(cashFlow?.investingActivities.netInvestingCashFlow || 0)}</span>
            </div>
          </div>

          {/* Financing Activities */}
          <div className="space-y-2">
            <h4 className="font-medium text-orange-600">Financing Activities</h4>
            <div className="flex justify-between text-sm">
              <span>Debt Proceeds</span>
              <span>{formatCurrency(cashFlow?.financingActivities.debtProceeds || 0)}</span>
            </div>
            <div className="flex justify-between text-sm">
              <span>Debt Payments</span>
              <span>{formatCurrency(cashFlow?.financingActivities.debtPayments || 0)}</span>
            </div>
            <div className="flex justify-between text-sm">
              <span>Equity Proceeds</span>
              <span>{formatCurrency(cashFlow?.financingActivities.equityProceeds || 0)}</span>
            </div>
            <div className="flex justify-between text-sm">
              <span>Dividend Payments</span>
              <span>{formatCurrency(cashFlow?.financingActivities.dividendPayments || 0)}</span>
            </div>
            <div className="flex justify-between font-medium border-t pt-2">
              <span>Net Financing Cash Flow</span>
              <span>{formatCurrency(cashFlow?.financingActivities.netFinancingCashFlow || 0)}</span>
            </div>
          </div>

          {/* Net Cash Flow */}
          <div className="space-y-2 border-t pt-4">
            <div className="flex justify-between">
              <span>Beginning Cash</span>
              <span className="font-medium">{formatCurrency(cashFlow?.beginningCash || 0)}</span>
            </div>
            <div className="flex justify-between">
              <span>Net Cash Flow</span>
              <span className="font-medium">{formatCurrency(cashFlow?.netCashFlow || 0)}</span>
            </div>
            <div className="flex justify-between font-bold text-lg border-t pt-2">
              <span>Ending Cash</span>
              <span className={cashFlow?.endingCash && cashFlow.endingCash >= 0 ? 'text-green-600' : 'text-red-600'}>
                {formatCurrency(cashFlow?.endingCash || 0)}
              </span>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );

  const renderTrialBalance = () => (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>Trial Balance</CardTitle>
          <CardDescription>All accounts with their debit and credit balances</CardDescription>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Account Code</TableHead>
                <TableHead>Account Name</TableHead>
                <TableHead>Type</TableHead>
                <TableHead className="text-right">Debit Balance</TableHead>
                <TableHead className="text-right">Credit Balance</TableHead>
                <TableHead className="text-right">Total Debits</TableHead>
                <TableHead className="text-right">Total Credits</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {trialBalance.map((item, index) => (
                <TableRow key={index}>
                  <TableCell className="font-medium">{item.accountCode}</TableCell>
                  <TableCell>{item.accountName}</TableCell>
                  <TableCell>
                    <Badge variant="outline">{item.accountType}</Badge>
                  </TableCell>
                  <TableCell className="text-right">
                    {item.debitBalance > 0 ? formatCurrency(item.debitBalance) : '-'}
                  </TableCell>
                  <TableCell className="text-right">
                    {item.creditBalance > 0 ? formatCurrency(item.creditBalance) : '-'}
                  </TableCell>
                  <TableCell className="text-right">{formatCurrency(item.totalDebits)}</TableCell>
                  <TableCell className="text-right">{formatCurrency(item.totalCredits)}</TableCell>
                </TableRow>
              ))}
              {trialBalance.length === 0 && (
                <TableRow>
                  <TableCell colSpan={7} className="text-center py-8">
                    <div className="text-muted-foreground">
                      No trial balance data available. Create some journal entries to see data here.
                    </div>
                  </TableCell>
                </TableRow>
              )}
            </TableBody>
          </Table>
          
          {trialBalance.length > 0 && (
            <div className="mt-4 pt-4 border-t">
              <div className="grid grid-cols-2 gap-4">
                <div className="text-right">
                  <span className="font-medium">Total Debits: </span>
                  <span className="font-bold">
                    {formatCurrency(trialBalance.reduce((sum, item) => sum + item.totalDebits, 0))}
                  </span>
                </div>
                <div className="text-right">
                  <span className="font-medium">Total Credits: </span>
                  <span className="font-bold">
                    {formatCurrency(trialBalance.reduce((sum, item) => sum + item.totalCredits, 0))}
                  </span>
                </div>
              </div>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Financial Reports</h1>
          <p className="text-muted-foreground">
            Generate and view comprehensive financial statements
          </p>
        </div>
        <div className="flex space-x-2">
          <Button variant="outline">
            <Download className="h-4 w-4 mr-2" />
            Export
          </Button>
          <Button variant="outline">
            <Calendar className="h-4 w-4 mr-2" />
            Date Range
          </Button>
        </div>
      </div>

      {/* Report Tabs */}
      <Tabs value={activeReport} onValueChange={setActiveReport}>
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="balance-sheet">Balance Sheet</TabsTrigger>
          <TabsTrigger value="income-statement">Income Statement</TabsTrigger>
          <TabsTrigger value="cash-flow">Cash Flow</TabsTrigger>
          <TabsTrigger value="trial-balance">Trial Balance</TabsTrigger>
        </TabsList>

        <TabsContent value="balance-sheet" className="space-y-4">
          {loading ? (
            <div className="flex items-center justify-center h-64">
              <div className="flex items-center space-x-2">
                <Loader2 className="h-6 w-6 animate-spin" />
                <span>Generating balance sheet...</span>
              </div>
            </div>
          ) : (
            renderBalanceSheet()
          )}
        </TabsContent>

        <TabsContent value="income-statement" className="space-y-4">
          {loading ? (
            <div className="flex items-center justify-center h-64">
              <div className="flex items-center space-x-2">
                <Loader2 className="h-6 w-6 animate-spin" />
                <span>Generating income statement...</span>
              </div>
            </div>
          ) : (
            renderIncomeStatement()
          )}
        </TabsContent>

        <TabsContent value="cash-flow" className="space-y-4">
          {loading ? (
            <div className="flex items-center justify-center h-64">
              <div className="flex items-center space-x-2">
                <Loader2 className="h-6 w-6 animate-spin" />
                <span>Generating cash flow statement...</span>
              </div>
            </div>
          ) : (
            renderCashFlowStatement()
          )}
        </TabsContent>

        <TabsContent value="trial-balance" className="space-y-4">
          {loading ? (
            <div className="flex items-center justify-center h-64">
              <div className="flex items-center space-x-2">
                <Loader2 className="h-6 w-6 animate-spin" />
                <span>Generating trial balance...</span>
              </div>
            </div>
          ) : (
            renderTrialBalance()
          )}
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default FinancialReports;
