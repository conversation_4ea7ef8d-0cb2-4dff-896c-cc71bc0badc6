import React, { useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, BarChart, Bar, ComposedChart, Area, AreaChart } from 'recharts';
import { DollarSign, TrendingUp, Calculator, Target, Users, Calendar } from 'lucide-react';

interface CLVData {
  segment: string;
  currentCLV: number;
  projectedCLV: number;
  customerCount: number;
  avgOrderValue: number;
  purchaseFrequency: number;
  customerLifespan: number;
  totalValue: number;
}

interface CLVTrendData {
  month: string;
  clv: number;
  newCustomers: number;
  retainedCustomers: number;
}

interface CustomerLifetimeValueProps {
  clvData?: CLVData[];
  trendData?: CLVTrendData[];
}

const CustomerLifetimeValue: React.FC<CustomerLifetimeValueProps> = ({ 
  clvData = [],
  trendData = []
}) => {
  const [selectedSegment, setSelectedSegment] = useState<string>('all');

  // Default CLV data if none provided
  const defaultCLVData: CLVData[] = [
    {
      segment: 'VIP Champions',
      currentCLV: 4200,
      projectedCLV: 5800,
      customerCount: 78,
      avgOrderValue: 680,
      purchaseFrequency: 8.2,
      customerLifespan: 36,
      totalValue: 327600
    },
    {
      segment: 'Loyal Customers',
      currentCLV: 2850,
      projectedCLV: 3400,
      customerCount: 156,
      avgOrderValue: 425,
      purchaseFrequency: 6.7,
      customerLifespan: 24,
      totalValue: 444600
    },
    {
      segment: 'Potential Loyalists',
      currentCLV: 1650,
      projectedCLV: 2200,
      customerCount: 342,
      avgOrderValue: 285,
      purchaseFrequency: 4.2,
      customerLifespan: 18,
      totalValue: 564300
    },
    {
      segment: 'New Customers',
      currentCLV: 850,
      projectedCLV: 1400,
      customerCount: 189,
      avgOrderValue: 145,
      purchaseFrequency: 1.8,
      customerLifespan: 12,
      totalValue: 160650
    }
  ];

  // Default trend data if none provided
  const defaultTrendData: CLVTrendData[] = [
    { month: 'Jan', clv: 1250, newCustomers: 45, retainedCustomers: 420 },
    { month: 'Feb', clv: 1320, newCustomers: 52, retainedCustomers: 435 },
    { month: 'Mar', clv: 1180, newCustomers: 38, retainedCustomers: 410 },
    { month: 'Apr', clv: 1450, newCustomers: 67, retainedCustomers: 465 },
    { month: 'May', clv: 1380, newCustomers: 58, retainedCustomers: 445 },
    { month: 'Jun', clv: 1520, newCustomers: 72, retainedCustomers: 485 }
  ];

  const clvSegments = clvData.length > 0 ? clvData : defaultCLVData;
  const clvTrends = trendData.length > 0 ? trendData : defaultTrendData;

  // Calculate overall metrics
  const totalCustomers = clvSegments.reduce((sum, segment) => sum + segment.customerCount, 0);
  const totalCurrentValue = clvSegments.reduce((sum, segment) => sum + segment.totalValue, 0);
  const averageCLV = totalCurrentValue / totalCustomers;
  const totalProjectedValue = clvSegments.reduce((sum, segment) => sum + (segment.projectedCLV * segment.customerCount), 0);

  // CLV calculation formula display
  const calculateCLV = (aov: number, frequency: number, lifespan: number) => {
    return aov * frequency * lifespan;
  };

  const clvMetrics = [
    {
      title: 'Average CLV',
      value: `$${averageCLV.toFixed(0)}`,
      change: '+12.5%',
      icon: DollarSign,
      color: 'text-green-600',
      bgColor: 'bg-green-100'
    },
    {
      title: 'Total Customer Value',
      value: `$${(totalCurrentValue / 1000000).toFixed(1)}M`,
      change: '+8.3%',
      icon: Target,
      color: 'text-blue-600',
      bgColor: 'bg-blue-100'
    },
    {
      title: 'Projected Growth',
      value: `$${((totalProjectedValue - totalCurrentValue) / 1000).toFixed(0)}K`,
      change: '+15.2%',
      icon: TrendingUp,
      color: 'text-purple-600',
      bgColor: 'bg-purple-100'
    },
    {
      title: 'High-Value Customers',
      value: `${clvSegments.filter(s => s.currentCLV > 2000).reduce((sum, s) => sum + s.customerCount, 0)}`,
      change: '+6.7%',
      icon: Users,
      color: 'text-orange-600',
      bgColor: 'bg-orange-100'
    }
  ];

  return (
    <div className="space-y-6">
      {/* CLV Metrics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        {clvMetrics.map((metric, index) => (
          <Card key={index}>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-xs font-medium text-gray-600">{metric.title}</p>
                  <p className="text-xl font-bold text-gray-900">{metric.value}</p>
                  <div className="flex items-center mt-1">
                    <TrendingUp className="h-3 w-3 text-green-500 mr-1" />
                    <span className="text-xs text-green-600">{metric.change}</span>
                  </div>
                </div>
                <div className={`p-2 rounded-full ${metric.bgColor}`}>
                  <metric.icon className={`h-4 w-4 ${metric.color}`} />
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* CLV Charts */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle>CLV by Customer Segment</CardTitle>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={300}>
              <BarChart data={clvSegments}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="segment" angle={-45} textAnchor="end" height={80} />
                <YAxis />
                <Tooltip formatter={(value) => [`$${value}`, 'CLV']} />
                <Bar dataKey="currentCLV" fill="#3b82f6" name="Current CLV" />
                <Bar dataKey="projectedCLV" fill="#8b5cf6" name="Projected CLV" />
              </BarChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>CLV Trends Over Time</CardTitle>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={300}>
              <ComposedChart data={clvTrends}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="month" />
                <YAxis yAxisId="left" />
                <YAxis yAxisId="right" orientation="right" />
                <Tooltip />
                <Area yAxisId="left" type="monotone" dataKey="clv" fill="#3b82f6" fillOpacity={0.3} stroke="#3b82f6" />
                <Bar yAxisId="right" dataKey="newCustomers" fill="#10b981" />
              </ComposedChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>
      </div>

      {/* CLV Calculator */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Calculator className="h-5 w-5 mr-2" />
            CLV Calculation Formula
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="bg-blue-50 rounded-lg p-4 mb-4">
            <h3 className="font-semibold text-blue-900 mb-2">Customer Lifetime Value Formula:</h3>
            <div className="text-blue-800 font-mono text-lg">
              CLV = Average Order Value × Purchase Frequency × Customer Lifespan
            </div>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="text-center p-4 bg-gray-50 rounded-lg">
              <DollarSign className="h-8 w-8 mx-auto mb-2 text-green-600" />
              <h4 className="font-semibold">Average Order Value</h4>
              <p className="text-sm text-gray-600">Total revenue ÷ Number of orders</p>
            </div>
            <div className="text-center p-4 bg-gray-50 rounded-lg">
              <Calendar className="h-8 w-8 mx-auto mb-2 text-blue-600" />
              <h4 className="font-semibold">Purchase Frequency</h4>
              <p className="text-sm text-gray-600">Number of orders ÷ Number of customers</p>
            </div>
            <div className="text-center p-4 bg-gray-50 rounded-lg">
              <Users className="h-8 w-8 mx-auto mb-2 text-purple-600" />
              <h4 className="font-semibold">Customer Lifespan</h4>
              <p className="text-sm text-gray-600">Average time customer remains active</p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Detailed CLV Analysis */}
      <Card>
        <CardHeader>
          <CardTitle>Detailed CLV Analysis by Segment</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="overflow-x-auto">
            <table className="w-full text-sm">
              <thead>
                <tr className="border-b">
                  <th className="text-left py-3 px-4 font-semibold">Segment</th>
                  <th className="text-center py-3 px-4 font-semibold">Customers</th>
                  <th className="text-center py-3 px-4 font-semibold">Current CLV</th>
                  <th className="text-center py-3 px-4 font-semibold">Projected CLV</th>
                  <th className="text-center py-3 px-4 font-semibold">AOV</th>
                  <th className="text-center py-3 px-4 font-semibold">Frequency</th>
                  <th className="text-center py-3 px-4 font-semibold">Lifespan</th>
                  <th className="text-center py-3 px-4 font-semibold">Total Value</th>
                </tr>
              </thead>
              <tbody>
                {clvSegments.map((segment, index) => (
                  <tr key={index} className="border-b hover:bg-gray-50">
                    <td className="py-3 px-4">
                      <div className="font-medium text-gray-900">{segment.segment}</div>
                    </td>
                    <td className="py-3 px-4 text-center">
                      <span className="text-gray-600">{segment.customerCount}</span>
                    </td>
                    <td className="py-3 px-4 text-center">
                      <span className="font-semibold text-blue-600">${segment.currentCLV}</span>
                    </td>
                    <td className="py-3 px-4 text-center">
                      <span className="font-semibold text-purple-600">${segment.projectedCLV}</span>
                      <div className="text-xs text-green-600">
                        +{(((segment.projectedCLV - segment.currentCLV) / segment.currentCLV) * 100).toFixed(1)}%
                      </div>
                    </td>
                    <td className="py-3 px-4 text-center">
                      <span className="text-gray-600">${segment.avgOrderValue}</span>
                    </td>
                    <td className="py-3 px-4 text-center">
                      <span className="text-gray-600">{segment.purchaseFrequency}x</span>
                    </td>
                    <td className="py-3 px-4 text-center">
                      <span className="text-gray-600">{segment.customerLifespan}mo</span>
                    </td>
                    <td className="py-3 px-4 text-center">
                      <span className="font-semibold text-green-600">
                        ${(segment.totalValue / 1000).toFixed(0)}K
                      </span>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </CardContent>
      </Card>

      {/* CLV Optimization Recommendations */}
      <Card>
        <CardHeader>
          <CardTitle>CLV Optimization Recommendations</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            <div className="p-4 bg-green-50 rounded-lg">
              <h4 className="font-semibold text-green-900 mb-2">Increase AOV</h4>
              <ul className="text-sm text-green-700 space-y-1">
                <li>• Cross-sell complementary products</li>
                <li>• Implement bundle offers</li>
                <li>• Offer volume discounts</li>
                <li>• Upsell premium versions</li>
              </ul>
              <Badge className="mt-2 bg-green-100 text-green-800">+15% potential increase</Badge>
            </div>
            
            <div className="p-4 bg-blue-50 rounded-lg">
              <h4 className="font-semibold text-blue-900 mb-2">Boost Frequency</h4>
              <ul className="text-sm text-blue-700 space-y-1">
                <li>• Loyalty program rewards</li>
                <li>• Subscription models</li>
                <li>• Personalized recommendations</li>
                <li>• Regular engagement campaigns</li>
              </ul>
              <Badge className="mt-2 bg-blue-100 text-blue-800">+20% potential increase</Badge>
            </div>
            
            <div className="p-4 bg-purple-50 rounded-lg">
              <h4 className="font-semibold text-purple-900 mb-2">Extend Lifespan</h4>
              <ul className="text-sm text-purple-700 space-y-1">
                <li>• Improve customer service</li>
                <li>• Regular satisfaction surveys</li>
                <li>• Proactive support</li>
                <li>• Community building</li>
              </ul>
              <Badge className="mt-2 bg-purple-100 text-purple-800">+25% potential increase</Badge>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default CustomerLifetimeValue;
