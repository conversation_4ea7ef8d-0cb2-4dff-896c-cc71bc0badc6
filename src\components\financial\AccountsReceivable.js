import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue, } from '@/components/ui/select';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Plus, Search, FileText, Clock, AlertTriangle, DollarSign, TrendingUp, Mail, Phone, Loader2 } from 'lucide-react';
import { InvoiceService, ReportService } from '../../services/financial';
import { InvoiceStatus } from '../../types/financial';
const AccountsReceivable = () => {
    const [invoices, setInvoices] = useState([]);
    const [overdueInvoices, setOverdueInvoices] = useState([]);
    const [agedReceivables, setAgedReceivables] = useState([]);
    const [loading, setLoading] = useState(true);
    const [searchTerm, setSearchTerm] = useState('');
    const [statusFilter, setStatusFilter] = useState('ALL');
    useEffect(() => {
        loadData();
    }, []);
    const loadData = async () => {
        try {
            setLoading(true);
            const [invoicesData, overdueData, agedData] = await Promise.all([
                InvoiceService.getInvoices('CUSTOMER'),
                InvoiceService.getOverdueInvoices(),
                ReportService.generateAgedReceivables()
            ]);
            setInvoices(invoicesData);
            setOverdueInvoices(overdueData);
            setAgedReceivables(agedData);
        }
        catch (error) {
            console.error('Error loading accounts receivable data:', error);
        }
        finally {
            setLoading(false);
        }
    };
    const formatCurrency = (amount) => {
        return new Intl.NumberFormat('en-US', {
            style: 'currency',
            currency: 'USD'
        }).format(amount);
    };
    const formatDate = (date) => {
        const dateObj = typeof date === 'string' ? new Date(date) : date;
        return new Intl.DateTimeFormat('en-US', {
            year: 'numeric',
            month: 'short',
            day: 'numeric'
        }).format(dateObj);
    };
    const getStatusColor = (status) => {
        switch (status) {
            case InvoiceStatus.PAID:
                return 'bg-green-100 text-green-800';
            case InvoiceStatus.PARTIALLY_PAID:
                return 'bg-yellow-100 text-yellow-800';
            case InvoiceStatus.OVERDUE:
                return 'bg-red-100 text-red-800';
            case InvoiceStatus.SENT:
                return 'bg-blue-100 text-blue-800';
            default:
                return 'bg-gray-100 text-gray-800';
        }
    };
    const getDaysOverdue = (dueDate) => {
        const due = typeof dueDate === 'string' ? new Date(dueDate) : dueDate;
        const today = new Date();
        const diffTime = today.getTime() - due.getTime();
        const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
        return diffDays > 0 ? diffDays : 0;
    };
    const filteredInvoices = invoices.filter(invoice => {
        const matchesSearch = invoice.invoiceNumber.toLowerCase().includes(searchTerm.toLowerCase()) ||
            (invoice.entityId && invoice.entityId.toLowerCase().includes(searchTerm.toLowerCase()));
        const matchesStatus = statusFilter === 'ALL' || invoice.status === statusFilter;
        return matchesSearch && matchesStatus;
    });
    const totalReceivables = invoices.reduce((sum, invoice) => sum + invoice.balance, 0);
    const totalOverdue = overdueInvoices.reduce((sum, invoice) => sum + invoice.balance, 0);
    const averageDaysToPayment = 30; // This would be calculated from historical data
    if (loading) {
        return (_jsx("div", { className: "flex items-center justify-center h-64", children: _jsxs("div", { className: "flex items-center space-x-2", children: [_jsx(Loader2, { className: "h-6 w-6 animate-spin" }), _jsx("span", { children: "Loading accounts receivable..." })] }) }));
    }
    return (_jsxs("div", { className: "space-y-6", children: [_jsxs("div", { className: "flex justify-between items-center", children: [_jsxs("div", { children: [_jsx("h1", { className: "text-3xl font-bold tracking-tight", children: "Accounts Receivable" }), _jsx("p", { className: "text-muted-foreground", children: "Manage customer invoices and track outstanding payments" })] }), _jsxs(Button, { children: [_jsx(Plus, { className: "h-4 w-4 mr-2" }), "Create Invoice"] })] }), _jsxs("div", { className: "grid grid-cols-1 md:grid-cols-4 gap-4", children: [_jsxs(Card, { children: [_jsxs(CardHeader, { className: "flex flex-row items-center justify-between space-y-0 pb-2", children: [_jsx(CardTitle, { className: "text-sm font-medium", children: "Total Receivables" }), _jsx(DollarSign, { className: "h-4 w-4 text-muted-foreground" })] }), _jsxs(CardContent, { children: [_jsx("div", { className: "text-2xl font-bold", children: formatCurrency(totalReceivables) }), _jsxs("p", { className: "text-xs text-muted-foreground", children: [invoices.length, " outstanding invoices"] })] })] }), _jsxs(Card, { children: [_jsxs(CardHeader, { className: "flex flex-row items-center justify-between space-y-0 pb-2", children: [_jsx(CardTitle, { className: "text-sm font-medium", children: "Overdue Amount" }), _jsx(AlertTriangle, { className: "h-4 w-4 text-red-500" })] }), _jsxs(CardContent, { children: [_jsx("div", { className: "text-2xl font-bold text-red-600", children: formatCurrency(totalOverdue) }), _jsxs("p", { className: "text-xs text-muted-foreground", children: [overdueInvoices.length, " overdue invoices"] })] })] }), _jsxs(Card, { children: [_jsxs(CardHeader, { className: "flex flex-row items-center justify-between space-y-0 pb-2", children: [_jsx(CardTitle, { className: "text-sm font-medium", children: "Avg. Days to Payment" }), _jsx(Clock, { className: "h-4 w-4 text-muted-foreground" })] }), _jsxs(CardContent, { children: [_jsx("div", { className: "text-2xl font-bold", children: averageDaysToPayment }), _jsx("p", { className: "text-xs text-muted-foreground", children: "Based on last 90 days" })] })] }), _jsxs(Card, { children: [_jsxs(CardHeader, { className: "flex flex-row items-center justify-between space-y-0 pb-2", children: [_jsx(CardTitle, { className: "text-sm font-medium", children: "Collection Rate" }), _jsx(TrendingUp, { className: "h-4 w-4 text-green-500" })] }), _jsxs(CardContent, { children: [_jsx("div", { className: "text-2xl font-bold text-green-600", children: "94.2%" }), _jsx("p", { className: "text-xs text-muted-foreground", children: "Last 12 months" })] })] })] }), _jsxs(Tabs, { defaultValue: "invoices", className: "space-y-4", children: [_jsxs(TabsList, { children: [_jsx(TabsTrigger, { value: "invoices", children: "Outstanding Invoices" }), _jsx(TabsTrigger, { value: "overdue", children: "Overdue" }), _jsx(TabsTrigger, { value: "aged", children: "Aged Receivables" })] }), _jsxs(TabsContent, { value: "invoices", className: "space-y-4", children: [_jsxs(Card, { children: [_jsx(CardHeader, { children: _jsx(CardTitle, { children: "Filters" }) }), _jsx(CardContent, { children: _jsxs("div", { className: "flex space-x-4", children: [_jsx("div", { className: "flex-1", children: _jsxs("div", { className: "relative", children: [_jsx(Search, { className: "absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" }), _jsx(Input, { placeholder: "Search invoices...", value: searchTerm, onChange: (e) => setSearchTerm(e.target.value), className: "pl-8" })] }) }), _jsxs(Select, { value: statusFilter, onValueChange: setStatusFilter, children: [_jsx(SelectTrigger, { className: "w-[180px]", children: _jsx(SelectValue, { placeholder: "Status" }) }), _jsxs(SelectContent, { children: [_jsx(SelectItem, { value: "ALL", children: "All Status" }), _jsx(SelectItem, { value: InvoiceStatus.SENT, children: "Sent" }), _jsx(SelectItem, { value: InvoiceStatus.PARTIALLY_PAID, children: "Partially Paid" }), _jsx(SelectItem, { value: InvoiceStatus.OVERDUE, children: "Overdue" })] })] })] }) })] }), _jsxs(Card, { children: [_jsxs(CardHeader, { children: [_jsx(CardTitle, { children: "Outstanding Invoices" }), _jsxs(CardDescription, { children: [filteredInvoices.length, " of ", invoices.length, " invoices"] })] }), _jsx(CardContent, { children: _jsxs(Table, { children: [_jsx(TableHeader, { children: _jsxs(TableRow, { children: [_jsx(TableHead, { children: "Invoice #" }), _jsx(TableHead, { children: "Customer" }), _jsx(TableHead, { children: "Date" }), _jsx(TableHead, { children: "Due Date" }), _jsx(TableHead, { className: "text-right", children: "Amount" }), _jsx(TableHead, { className: "text-right", children: "Balance" }), _jsx(TableHead, { children: "Status" }), _jsx(TableHead, { children: "Actions" })] }) }), _jsx(TableBody, { children: filteredInvoices.map((invoice) => (_jsxs(TableRow, { children: [_jsx(TableCell, { className: "font-medium", children: invoice.invoiceNumber }), _jsx(TableCell, { children: invoice.entityId }), _jsx(TableCell, { children: formatDate(invoice.date) }), _jsx(TableCell, { children: formatDate(invoice.dueDate) }), _jsx(TableCell, { className: "text-right", children: formatCurrency(invoice.total) }), _jsx(TableCell, { className: "text-right font-medium", children: formatCurrency(invoice.balance) }), _jsx(TableCell, { children: _jsx(Badge, { className: getStatusColor(invoice.status), children: invoice.status.replace('_', ' ') }) }), _jsx(TableCell, { children: _jsxs("div", { className: "flex space-x-2", children: [_jsx(Button, { variant: "ghost", size: "sm", children: _jsx(FileText, { className: "h-4 w-4" }) }), _jsx(Button, { variant: "ghost", size: "sm", children: _jsx(Mail, { className: "h-4 w-4" }) })] }) })] }, invoice.id))) })] }) })] })] }), _jsx(TabsContent, { value: "overdue", className: "space-y-4", children: _jsxs(Card, { children: [_jsxs(CardHeader, { children: [_jsxs(CardTitle, { className: "flex items-center space-x-2", children: [_jsx(AlertTriangle, { className: "h-5 w-5 text-red-500" }), _jsx("span", { children: "Overdue Invoices" })] }), _jsx(CardDescription, { children: "Invoices past their due date requiring immediate attention" })] }), _jsx(CardContent, { children: _jsxs(Table, { children: [_jsx(TableHeader, { children: _jsxs(TableRow, { children: [_jsx(TableHead, { children: "Invoice #" }), _jsx(TableHead, { children: "Customer" }), _jsx(TableHead, { children: "Due Date" }), _jsx(TableHead, { children: "Days Overdue" }), _jsx(TableHead, { className: "text-right", children: "Balance" }), _jsx(TableHead, { children: "Actions" })] }) }), _jsx(TableBody, { children: overdueInvoices.map((invoice) => (_jsxs(TableRow, { children: [_jsx(TableCell, { className: "font-medium", children: invoice.invoiceNumber }), _jsx(TableCell, { children: invoice.entityId }), _jsx(TableCell, { children: formatDate(invoice.dueDate) }), _jsx(TableCell, { children: _jsxs(Badge, { variant: "destructive", children: [getDaysOverdue(invoice.dueDate), " days"] }) }), _jsx(TableCell, { className: "text-right font-medium", children: formatCurrency(invoice.balance) }), _jsx(TableCell, { children: _jsxs("div", { className: "flex space-x-2", children: [_jsxs(Button, { variant: "outline", size: "sm", children: [_jsx(Mail, { className: "h-4 w-4 mr-1" }), "Send Reminder"] }), _jsxs(Button, { variant: "outline", size: "sm", children: [_jsx(Phone, { className: "h-4 w-4 mr-1" }), "Call"] })] }) })] }, invoice.id))) })] }) })] }) }), _jsx(TabsContent, { value: "aged", className: "space-y-4", children: _jsxs(Card, { children: [_jsxs(CardHeader, { children: [_jsx(CardTitle, { children: "Aged Receivables Report" }), _jsx(CardDescription, { children: "Outstanding receivables grouped by age" })] }), _jsx(CardContent, { children: _jsx("div", { className: "text-center py-8 text-muted-foreground", children: "Aged receivables report will be displayed here" }) })] }) })] })] }));
};
export default AccountsReceivable;
