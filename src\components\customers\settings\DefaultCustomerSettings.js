import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { toast } from 'sonner';
import { Save } from 'lucide-react';
export const DefaultCustomerSettings = () => {
    const [settings, setSettings] = useState({
        defaultStatus: 'active',
        defaultAccountType: 'standard',
        defaultCurrency: 'USD',
        defaultPaymentTerms: 'Net 30'
    });
    const handleChange = (field, value) => {
        setSettings(prev => ({
            ...prev,
            [field]: value
        }));
    };
    const handleSave = () => {
        console.log('Saving default customer settings:', settings);
        toast.success('Default customer settings saved successfully');
    };
    return (_jsxs("div", { className: "space-y-6", children: [_jsxs("div", { className: "grid grid-cols-1 md:grid-cols-2 gap-6", children: [_jsxs("div", { className: "space-y-2", children: [_jsx(Label, { htmlFor: "defaultStatus", children: "Default Status" }), _jsxs(Select, { value: settings.defaultStatus, onValueChange: (value) => handleChange('defaultStatus', value), children: [_jsx(SelectTrigger, { id: "defaultStatus", children: _jsx(SelectValue, { placeholder: "Select default status" }) }), _jsxs(SelectContent, { children: [_jsx(SelectItem, { value: "active", children: "Active" }), _jsx(SelectItem, { value: "inactive", children: "Inactive" }), _jsx(SelectItem, { value: "pending", children: "Pending" })] })] })] }), _jsxs("div", { className: "space-y-2", children: [_jsx(Label, { htmlFor: "defaultAccountType", children: "Default Account Type" }), _jsxs(Select, { value: settings.defaultAccountType, onValueChange: (value) => handleChange('defaultAccountType', value), children: [_jsx(SelectTrigger, { id: "defaultAccountType", children: _jsx(SelectValue, { placeholder: "Select default account type" }) }), _jsxs(SelectContent, { children: [_jsx(SelectItem, { value: "standard", children: "Standard" }), _jsx(SelectItem, { value: "premium", children: "Premium" }), _jsx(SelectItem, { value: "enterprise", children: "Enterprise" })] })] })] }), _jsxs("div", { className: "space-y-2", children: [_jsx(Label, { htmlFor: "defaultCurrency", children: "Default Currency" }), _jsxs(Select, { value: settings.defaultCurrency, onValueChange: (value) => handleChange('defaultCurrency', value), children: [_jsx(SelectTrigger, { id: "defaultCurrency", children: _jsx(SelectValue, { placeholder: "Select default currency" }) }), _jsxs(SelectContent, { children: [_jsx(SelectItem, { value: "USD", children: "US Dollar (USD)" }), _jsx(SelectItem, { value: "EUR", children: "Euro (EUR)" }), _jsx(SelectItem, { value: "GBP", children: "British Pound (GBP)" }), _jsx(SelectItem, { value: "JPY", children: "Japanese Yen (JPY)" }), _jsx(SelectItem, { value: "CAD", children: "Canadian Dollar (CAD)" })] })] })] }), _jsxs("div", { className: "space-y-2", children: [_jsx(Label, { htmlFor: "defaultPaymentTerms", children: "Default Payment Terms" }), _jsx(Input, { id: "defaultPaymentTerms", value: settings.defaultPaymentTerms, onChange: (e) => handleChange('defaultPaymentTerms', e.target.value), placeholder: "e.g., Net 30" })] })] }), _jsx("div", { className: "flex justify-end", children: _jsxs(Button, { onClick: handleSave, children: [_jsx(Save, { className: "h-4 w-4 mr-2" }), "Save Settings"] }) })] }));
};
