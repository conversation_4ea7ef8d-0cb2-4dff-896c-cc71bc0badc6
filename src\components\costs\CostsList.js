import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
import { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { Card, CardContent, CardDescription, CardHeader, CardTitle, } from "@/components/ui/card";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow, } from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue, } from "@/components/ui/select";
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuLabel, DropdownMenuSeparator, DropdownMenuTrigger, } from "@/components/ui/dropdown-menu";
import { format } from 'date-fns';
import { PlusCircle, MoreHorizontal, Edit, History, Trash2, Search, Calendar, Filter } from "lucide-react";
import { useDeleteSupplierCost, useSupplierCosts } from '@/hooks/use-supplier-costs';
export function CostsList({ supplier }) {
    const navigate = useNavigate();
    const [filters, setFilters] = useState({
        supplierId: supplier?.id,
    });
    const [searchTerm, setSearchTerm] = useState('');
    const { data: costs = [], isLoading } = useSupplierCosts(filters);
    const { mutate: deleteCost } = useDeleteSupplierCost();
    // Filter by search term (product name or SKU)
    const filteredCosts = costs.filter(cost => cost.product_name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        cost.product_sku?.toLowerCase().includes(searchTerm.toLowerCase()));
    const handleDelete = (id) => {
        if (globalThis.confirm('Are you sure you want to delete this cost record?')) {
            deleteCost(id);
        }
    };
    const handleFilterChange = (key, value) => {
        setFilters(prev => ({
            ...prev,
            [key]: value
        }));
    };
    const formatCurrency = (amount, currency) => {
        return new Intl.NumberFormat('en-US', {
            style: 'currency',
            currency: currency || 'USD',
            minimumFractionDigits: 2
        }).format(amount);
    };
    return (_jsxs(Card, { children: [_jsxs(CardHeader, { className: "flex flex-row items-center justify-between", children: [_jsxs("div", { children: [_jsx(CardTitle, { children: supplier ? `${supplier.name} - Costs` : 'All Supplier Costs' }), _jsx(CardDescription, { children: supplier
                                    ? `Manage cost data for ${supplier.name}`
                                    : 'View and manage all supplier costs' })] }), _jsxs(Button, { onClick: () => navigate(supplier
                            ? `/beta1/suppliers/${supplier.id}/costs/new`
                            : '/beta1/costs/new'), children: [_jsx(PlusCircle, { className: "h-4 w-4 mr-2" }), "Add Cost"] })] }), _jsxs(CardContent, { children: [_jsx("div", { className: "mb-6 space-y-4", children: _jsxs("div", { className: "flex flex-col sm:flex-row sm:items-center gap-4", children: [_jsxs("div", { className: "relative flex-1", children: [_jsx(Search, { className: "absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" }), _jsx(Input, { placeholder: "Search by product name or SKU...", className: "pl-8", value: searchTerm, onChange: e => setSearchTerm(e.target.value) })] }), _jsxs("div", { className: "flex items-center gap-2", children: [_jsx(Filter, { className: "h-4 w-4 text-muted-foreground" }), _jsx("span", { className: "text-sm text-muted-foreground", children: "Status:" }), _jsxs(Select, { value: filters.status || '', onValueChange: value => handleFilterChange('status', value || undefined), children: [_jsx(SelectTrigger, { className: "w-[120px]", children: _jsx(SelectValue, { placeholder: "All" }) }), _jsxs(SelectContent, { children: [_jsx(SelectItem, { value: "", children: "All" }), _jsx(SelectItem, { value: "active", children: "Active" }), _jsx(SelectItem, { value: "pending_approval", children: "Pending" }), _jsx(SelectItem, { value: "expired", children: "Expired" })] })] })] }), !supplier && (_jsxs("div", { className: "flex items-center gap-2", children: [_jsx(Calendar, { className: "h-4 w-4 text-muted-foreground" }), _jsx("span", { className: "text-sm text-muted-foreground", children: "Date:" }), _jsxs(Select, { value: filters.effectiveDateStart ? 'custom' : '', onValueChange: value => {
                                                if (value === '') {
                                                    handleFilterChange('effectiveDateStart', undefined);
                                                    handleFilterChange('effectiveDateEnd', undefined);
                                                }
                                                else if (value === 'current') {
                                                    handleFilterChange('effectiveDateStart', new Date().toISOString());
                                                }
                                            }, children: [_jsx(SelectTrigger, { className: "w-[120px]", children: _jsx(SelectValue, { placeholder: "All Dates" }) }), _jsxs(SelectContent, { children: [_jsx(SelectItem, { value: "", children: "All Dates" }), _jsx(SelectItem, { value: "current", children: "Current" })] })] })] }))] }) }), _jsx("div", { className: "rounded-md border", children: _jsxs(Table, { children: [_jsx(TableHeader, { children: _jsxs(TableRow, { children: [_jsx(TableHead, { children: "Product" }), _jsx(TableHead, { children: "SKU" }), !supplier && _jsx(TableHead, { children: "Supplier" }), _jsx(TableHead, { children: "Cost" }), _jsx(TableHead, { children: "Currency" }), _jsx(TableHead, { children: "Effective Date" }), _jsx(TableHead, { children: "Status" }), _jsx(TableHead, { children: "Type" }), _jsx(TableHead, { className: "w-[100px]", children: "Actions" })] }) }), _jsx(TableBody, { children: isLoading ? (_jsx(TableRow, { children: _jsx(TableCell, { colSpan: supplier ? 8 : 9, className: "h-24 text-center", children: "Loading costs..." }) })) : filteredCosts.length === 0 ? (_jsx(TableRow, { children: _jsx(TableCell, { colSpan: supplier ? 8 : 9, className: "h-24 text-center text-muted-foreground", children: "No cost data found" }) })) : (filteredCosts.map((cost) => (_jsxs(TableRow, { children: [_jsx(TableCell, { children: cost.product_name }), _jsx(TableCell, { className: "font-mono", children: cost.product_sku }), !supplier && _jsx(TableCell, { children: cost.supplier_name }), _jsx(TableCell, { className: "font-mono", children: formatCurrency(cost.cost, cost.currency_code) }), _jsx(TableCell, { children: cost.currency_code }), _jsx(TableCell, { children: cost.effective_date ? format(new Date(cost.effective_date), 'PP') : '—' }), _jsx(TableCell, { children: _jsx(Badge, { variant: cost.status === 'active' ? 'default' :
                                                        cost.status === 'pending_approval' ? 'outline' :
                                                            cost.status === 'expired' ? 'secondary' : 'destructive', children: cost.status }) }), _jsxs(TableCell, { children: [cost.is_promotional && _jsx(Badge, { variant: "outline", className: "bg-amber-50", children: "Promotional" }), cost.is_contract && _jsx(Badge, { variant: "outline", className: "bg-blue-50", children: "Contract" }), !cost.is_promotional && !cost.is_contract && "Standard"] }), _jsx(TableCell, { children: _jsxs(DropdownMenu, { children: [_jsx(DropdownMenuTrigger, { asChild: true, children: _jsx(Button, { variant: "ghost", className: "h-8 w-8 p-0", children: _jsx(MoreHorizontal, { className: "h-4 w-4" }) }) }), _jsxs(DropdownMenuContent, { align: "end", children: [_jsx(DropdownMenuLabel, { children: "Actions" }), _jsxs(DropdownMenuItem, { onClick: () => navigate(`/beta1/costs/${cost.id}`), children: [_jsx(Edit, { className: "h-4 w-4 mr-2" }), "Edit"] }), _jsxs(DropdownMenuItem, { onClick: () => navigate(`/beta1/costs/${cost.id}/history`), children: [_jsx(History, { className: "h-4 w-4 mr-2" }), "View History"] }), _jsx(DropdownMenuSeparator, {}), _jsxs(DropdownMenuItem, { className: "text-red-600", onClick: () => handleDelete(cost.id), children: [_jsx(Trash2, { className: "h-4 w-4 mr-2" }), "Delete"] })] })] }) })] }, cost.id)))) })] }) })] })] }));
}
