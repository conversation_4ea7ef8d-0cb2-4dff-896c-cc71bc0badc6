import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Download, Calendar, TrendingUp, TrendingDown, Loader2 } from 'lucide-react';
import { ReportService } from '../../services/financial/reportService';
const FinancialReports = () => {
    const [activeReport, setActiveReport] = useState('balance-sheet');
    const [loading, setLoading] = useState(false);
    const [balanceSheet, setBalanceSheet] = useState(null);
    const [incomeStatement, setIncomeStatement] = useState(null);
    const [cashFlow, setCashFlow] = useState(null);
    const [trialBalance, setTrialBalance] = useState([]);
    const formatCurrency = (amount) => {
        return new Intl.NumberFormat('en-US', {
            style: 'currency',
            currency: 'USD'
        }).format(amount);
    };
    const formatDate = (date) => {
        return new Intl.DateTimeFormat('en-US', {
            year: 'numeric',
            month: 'long',
            day: 'numeric'
        }).format(date);
    };
    const generateBalanceSheet = async () => {
        try {
            setLoading(true);
            const data = await ReportService.generateBalanceSheet(new Date());
            setBalanceSheet(data);
        }
        catch (error) {
            console.error('Error generating balance sheet:', error);
        }
        finally {
            setLoading(false);
        }
    };
    const generateIncomeStatement = async () => {
        try {
            setLoading(true);
            const startDate = new Date(new Date().getFullYear(), 0, 1); // Start of year
            const endDate = new Date(); // Today
            const data = await ReportService.generateIncomeStatement(startDate, endDate);
            setIncomeStatement(data);
        }
        catch (error) {
            console.error('Error generating income statement:', error);
        }
        finally {
            setLoading(false);
        }
    };
    const generateCashFlowStatement = async () => {
        try {
            setLoading(true);
            const startDate = new Date(new Date().getFullYear(), 0, 1); // Start of year
            const endDate = new Date(); // Today
            const data = await ReportService.generateCashFlowStatement(startDate, endDate);
            setCashFlow(data);
        }
        catch (error) {
            console.error('Error generating cash flow statement:', error);
        }
        finally {
            setLoading(false);
        }
    };
    const generateTrialBalance = async () => {
        try {
            setLoading(true);
            const data = await ReportService.generateTrialBalance(new Date());
            setTrialBalance(data);
        }
        catch (error) {
            console.error('Error generating trial balance:', error);
        }
        finally {
            setLoading(false);
        }
    };
    useEffect(() => {
        switch (activeReport) {
            case 'balance-sheet':
                generateBalanceSheet();
                break;
            case 'income-statement':
                generateIncomeStatement();
                break;
            case 'cash-flow':
                generateCashFlowStatement();
                break;
            case 'trial-balance':
                generateTrialBalance();
                break;
        }
    }, [activeReport]);
    const renderBalanceSheet = () => (_jsx("div", { className: "space-y-6", children: _jsxs("div", { className: "grid grid-cols-1 md:grid-cols-2 gap-6", children: [_jsxs(Card, { children: [_jsx(CardHeader, { children: _jsxs(CardTitle, { className: "flex items-center space-x-2", children: [_jsx(TrendingUp, { className: "h-5 w-5 text-green-600" }), _jsx("span", { children: "Assets" })] }) }), _jsxs(CardContent, { className: "space-y-4", children: [_jsxs("div", { className: "flex justify-between", children: [_jsx("span", { children: "Current Assets" }), _jsx("span", { className: "font-medium", children: formatCurrency(balanceSheet?.assets.currentAssets || 0) })] }), _jsxs("div", { className: "flex justify-between", children: [_jsx("span", { children: "Fixed Assets" }), _jsx("span", { className: "font-medium", children: formatCurrency(balanceSheet?.assets.fixedAssets || 0) })] }), _jsx("hr", {}), _jsxs("div", { className: "flex justify-between font-bold", children: [_jsx("span", { children: "Total Assets" }), _jsx("span", { children: formatCurrency(balanceSheet?.assets.totalAssets || 0) })] })] })] }), _jsxs(Card, { children: [_jsx(CardHeader, { children: _jsxs(CardTitle, { className: "flex items-center space-x-2", children: [_jsx(TrendingDown, { className: "h-5 w-5 text-red-600" }), _jsx("span", { children: "Liabilities & Equity" })] }) }), _jsxs(CardContent, { className: "space-y-4", children: [_jsxs("div", { className: "space-y-2", children: [_jsx("h4", { className: "font-medium", children: "Liabilities" }), _jsxs("div", { className: "flex justify-between text-sm", children: [_jsx("span", { children: "Current Liabilities" }), _jsx("span", { children: formatCurrency(balanceSheet?.liabilities.currentLiabilities || 0) })] }), _jsxs("div", { className: "flex justify-between text-sm", children: [_jsx("span", { children: "Long-term Liabilities" }), _jsx("span", { children: formatCurrency(balanceSheet?.liabilities.longTermLiabilities || 0) })] })] }), _jsxs("div", { className: "space-y-2", children: [_jsx("h4", { className: "font-medium", children: "Equity" }), _jsxs("div", { className: "flex justify-between text-sm", children: [_jsx("span", { children: "Owner's Equity" }), _jsx("span", { children: formatCurrency(balanceSheet?.equity.ownersEquity || 0) })] }), _jsxs("div", { className: "flex justify-between text-sm", children: [_jsx("span", { children: "Retained Earnings" }), _jsx("span", { children: formatCurrency(balanceSheet?.equity.retainedEarnings || 0) })] })] }), _jsx("hr", {}), _jsxs("div", { className: "flex justify-between font-bold", children: [_jsx("span", { children: "Total Liabilities & Equity" }), _jsx("span", { children: formatCurrency((balanceSheet?.liabilities.totalLiabilities || 0) + (balanceSheet?.equity.totalEquity || 0)) })] })] })] })] }) }));
    const renderIncomeStatement = () => (_jsx("div", { className: "space-y-6", children: _jsxs(Card, { children: [_jsxs(CardHeader, { children: [_jsx(CardTitle, { children: "Income Statement" }), _jsx(CardDescription, { children: incomeStatement && `${formatDate(incomeStatement.periodStart)} - ${formatDate(incomeStatement.periodEnd)}` })] }), _jsxs(CardContent, { className: "space-y-4", children: [_jsxs("div", { className: "space-y-2", children: [_jsx("h4", { className: "font-medium text-green-600", children: "Revenue" }), _jsxs("div", { className: "flex justify-between", children: [_jsx("span", { children: "Operating Revenue" }), _jsx("span", { children: formatCurrency(incomeStatement?.revenue.operatingRevenue || 0) })] }), _jsxs("div", { className: "flex justify-between", children: [_jsx("span", { children: "Other Revenue" }), _jsx("span", { children: formatCurrency(incomeStatement?.revenue.otherRevenue || 0) })] }), _jsxs("div", { className: "flex justify-between font-medium border-t pt-2", children: [_jsx("span", { children: "Total Revenue" }), _jsx("span", { children: formatCurrency(incomeStatement?.revenue.totalRevenue || 0) })] })] }), _jsxs("div", { className: "space-y-2", children: [_jsx("h4", { className: "font-medium text-red-600", children: "Expenses" }), _jsxs("div", { className: "flex justify-between", children: [_jsx("span", { children: "Cost of Goods Sold" }), _jsx("span", { children: formatCurrency(incomeStatement?.expenses.costOfGoodsSold || 0) })] }), _jsxs("div", { className: "flex justify-between", children: [_jsx("span", { children: "Operating Expenses" }), _jsx("span", { children: formatCurrency(incomeStatement?.expenses.operatingExpenses || 0) })] }), _jsxs("div", { className: "flex justify-between", children: [_jsx("span", { children: "Other Expenses" }), _jsx("span", { children: formatCurrency(incomeStatement?.expenses.otherExpenses || 0) })] }), _jsxs("div", { className: "flex justify-between font-medium border-t pt-2", children: [_jsx("span", { children: "Total Expenses" }), _jsx("span", { children: formatCurrency(incomeStatement?.expenses.totalExpenses || 0) })] })] }), _jsxs("div", { className: "space-y-2 border-t pt-4", children: [_jsxs("div", { className: "flex justify-between", children: [_jsx("span", { children: "Gross Profit" }), _jsx("span", { className: "font-medium", children: formatCurrency(incomeStatement?.grossProfit || 0) })] }), _jsxs("div", { className: "flex justify-between", children: [_jsx("span", { children: "Operating Income" }), _jsx("span", { className: "font-medium", children: formatCurrency(incomeStatement?.operatingIncome || 0) })] }), _jsxs("div", { className: "flex justify-between font-bold text-lg border-t pt-2", children: [_jsx("span", { children: "Net Income" }), _jsx("span", { className: incomeStatement?.netIncome && incomeStatement.netIncome >= 0 ? 'text-green-600' : 'text-red-600', children: formatCurrency(incomeStatement?.netIncome || 0) })] })] })] })] }) }));
    const renderCashFlowStatement = () => (_jsx("div", { className: "space-y-6", children: _jsxs(Card, { children: [_jsxs(CardHeader, { children: [_jsx(CardTitle, { children: "Cash Flow Statement" }), _jsx(CardDescription, { children: cashFlow && `${formatDate(cashFlow.periodStart)} - ${formatDate(cashFlow.periodEnd)}` })] }), _jsxs(CardContent, { className: "space-y-6", children: [_jsxs("div", { className: "space-y-2", children: [_jsx("h4", { className: "font-medium text-blue-600", children: "Operating Activities" }), _jsxs("div", { className: "flex justify-between text-sm", children: [_jsx("span", { children: "Net Income" }), _jsx("span", { children: formatCurrency(cashFlow?.operatingActivities.netIncome || 0) })] }), _jsxs("div", { className: "flex justify-between text-sm", children: [_jsx("span", { children: "Depreciation" }), _jsx("span", { children: formatCurrency(cashFlow?.operatingActivities.depreciation || 0) })] }), _jsxs("div", { className: "flex justify-between text-sm", children: [_jsx("span", { children: "Accounts Receivable Change" }), _jsx("span", { children: formatCurrency(cashFlow?.operatingActivities.accountsReceivableChange || 0) })] }), _jsxs("div", { className: "flex justify-between text-sm", children: [_jsx("span", { children: "Accounts Payable Change" }), _jsx("span", { children: formatCurrency(cashFlow?.operatingActivities.accountsPayableChange || 0) })] }), _jsxs("div", { className: "flex justify-between text-sm", children: [_jsx("span", { children: "Inventory Change" }), _jsx("span", { children: formatCurrency(cashFlow?.operatingActivities.inventoryChange || 0) })] }), _jsxs("div", { className: "flex justify-between font-medium border-t pt-2", children: [_jsx("span", { children: "Net Operating Cash Flow" }), _jsx("span", { children: formatCurrency(cashFlow?.operatingActivities.netOperatingCashFlow || 0) })] })] }), _jsxs("div", { className: "space-y-2", children: [_jsx("h4", { className: "font-medium text-purple-600", children: "Investing Activities" }), _jsxs("div", { className: "flex justify-between text-sm", children: [_jsx("span", { children: "Asset Purchases" }), _jsx("span", { children: formatCurrency(cashFlow?.investingActivities.assetPurchases || 0) })] }), _jsxs("div", { className: "flex justify-between text-sm", children: [_jsx("span", { children: "Asset Sales" }), _jsx("span", { children: formatCurrency(cashFlow?.investingActivities.assetSales || 0) })] }), _jsxs("div", { className: "flex justify-between font-medium border-t pt-2", children: [_jsx("span", { children: "Net Investing Cash Flow" }), _jsx("span", { children: formatCurrency(cashFlow?.investingActivities.netInvestingCashFlow || 0) })] })] }), _jsxs("div", { className: "space-y-2", children: [_jsx("h4", { className: "font-medium text-orange-600", children: "Financing Activities" }), _jsxs("div", { className: "flex justify-between text-sm", children: [_jsx("span", { children: "Debt Proceeds" }), _jsx("span", { children: formatCurrency(cashFlow?.financingActivities.debtProceeds || 0) })] }), _jsxs("div", { className: "flex justify-between text-sm", children: [_jsx("span", { children: "Debt Payments" }), _jsx("span", { children: formatCurrency(cashFlow?.financingActivities.debtPayments || 0) })] }), _jsxs("div", { className: "flex justify-between text-sm", children: [_jsx("span", { children: "Equity Proceeds" }), _jsx("span", { children: formatCurrency(cashFlow?.financingActivities.equityProceeds || 0) })] }), _jsxs("div", { className: "flex justify-between text-sm", children: [_jsx("span", { children: "Dividend Payments" }), _jsx("span", { children: formatCurrency(cashFlow?.financingActivities.dividendPayments || 0) })] }), _jsxs("div", { className: "flex justify-between font-medium border-t pt-2", children: [_jsx("span", { children: "Net Financing Cash Flow" }), _jsx("span", { children: formatCurrency(cashFlow?.financingActivities.netFinancingCashFlow || 0) })] })] }), _jsxs("div", { className: "space-y-2 border-t pt-4", children: [_jsxs("div", { className: "flex justify-between", children: [_jsx("span", { children: "Beginning Cash" }), _jsx("span", { className: "font-medium", children: formatCurrency(cashFlow?.beginningCash || 0) })] }), _jsxs("div", { className: "flex justify-between", children: [_jsx("span", { children: "Net Cash Flow" }), _jsx("span", { className: "font-medium", children: formatCurrency(cashFlow?.netCashFlow || 0) })] }), _jsxs("div", { className: "flex justify-between font-bold text-lg border-t pt-2", children: [_jsx("span", { children: "Ending Cash" }), _jsx("span", { className: cashFlow?.endingCash && cashFlow.endingCash >= 0 ? 'text-green-600' : 'text-red-600', children: formatCurrency(cashFlow?.endingCash || 0) })] })] })] })] }) }));
    const renderTrialBalance = () => (_jsx("div", { className: "space-y-6", children: _jsxs(Card, { children: [_jsxs(CardHeader, { children: [_jsx(CardTitle, { children: "Trial Balance" }), _jsx(CardDescription, { children: "All accounts with their debit and credit balances" })] }), _jsxs(CardContent, { children: [_jsxs(Table, { children: [_jsx(TableHeader, { children: _jsxs(TableRow, { children: [_jsx(TableHead, { children: "Account Code" }), _jsx(TableHead, { children: "Account Name" }), _jsx(TableHead, { children: "Type" }), _jsx(TableHead, { className: "text-right", children: "Debit Balance" }), _jsx(TableHead, { className: "text-right", children: "Credit Balance" }), _jsx(TableHead, { className: "text-right", children: "Total Debits" }), _jsx(TableHead, { className: "text-right", children: "Total Credits" })] }) }), _jsxs(TableBody, { children: [trialBalance.map((item, index) => (_jsxs(TableRow, { children: [_jsx(TableCell, { className: "font-medium", children: item.accountCode }), _jsx(TableCell, { children: item.accountName }), _jsx(TableCell, { children: _jsx(Badge, { variant: "outline", children: item.accountType }) }), _jsx(TableCell, { className: "text-right", children: item.debitBalance > 0 ? formatCurrency(item.debitBalance) : '-' }), _jsx(TableCell, { className: "text-right", children: item.creditBalance > 0 ? formatCurrency(item.creditBalance) : '-' }), _jsx(TableCell, { className: "text-right", children: formatCurrency(item.totalDebits) }), _jsx(TableCell, { className: "text-right", children: formatCurrency(item.totalCredits) })] }, index))), trialBalance.length === 0 && (_jsx(TableRow, { children: _jsx(TableCell, { colSpan: 7, className: "text-center py-8", children: _jsx("div", { className: "text-muted-foreground", children: "No trial balance data available. Create some journal entries to see data here." }) }) }))] })] }), trialBalance.length > 0 && (_jsx("div", { className: "mt-4 pt-4 border-t", children: _jsxs("div", { className: "grid grid-cols-2 gap-4", children: [_jsxs("div", { className: "text-right", children: [_jsx("span", { className: "font-medium", children: "Total Debits: " }), _jsx("span", { className: "font-bold", children: formatCurrency(trialBalance.reduce((sum, item) => sum + item.totalDebits, 0)) })] }), _jsxs("div", { className: "text-right", children: [_jsx("span", { className: "font-medium", children: "Total Credits: " }), _jsx("span", { className: "font-bold", children: formatCurrency(trialBalance.reduce((sum, item) => sum + item.totalCredits, 0)) })] })] }) }))] })] }) }));
    return (_jsxs("div", { className: "space-y-6", children: [_jsxs("div", { className: "flex justify-between items-center", children: [_jsxs("div", { children: [_jsx("h1", { className: "text-3xl font-bold tracking-tight", children: "Financial Reports" }), _jsx("p", { className: "text-muted-foreground", children: "Generate and view comprehensive financial statements" })] }), _jsxs("div", { className: "flex space-x-2", children: [_jsxs(Button, { variant: "outline", children: [_jsx(Download, { className: "h-4 w-4 mr-2" }), "Export"] }), _jsxs(Button, { variant: "outline", children: [_jsx(Calendar, { className: "h-4 w-4 mr-2" }), "Date Range"] })] })] }), _jsxs(Tabs, { value: activeReport, onValueChange: setActiveReport, children: [_jsxs(TabsList, { className: "grid w-full grid-cols-4", children: [_jsx(TabsTrigger, { value: "balance-sheet", children: "Balance Sheet" }), _jsx(TabsTrigger, { value: "income-statement", children: "Income Statement" }), _jsx(TabsTrigger, { value: "cash-flow", children: "Cash Flow" }), _jsx(TabsTrigger, { value: "trial-balance", children: "Trial Balance" })] }), _jsx(TabsContent, { value: "balance-sheet", className: "space-y-4", children: loading ? (_jsx("div", { className: "flex items-center justify-center h-64", children: _jsxs("div", { className: "flex items-center space-x-2", children: [_jsx(Loader2, { className: "h-6 w-6 animate-spin" }), _jsx("span", { children: "Generating balance sheet..." })] }) })) : (renderBalanceSheet()) }), _jsx(TabsContent, { value: "income-statement", className: "space-y-4", children: loading ? (_jsx("div", { className: "flex items-center justify-center h-64", children: _jsxs("div", { className: "flex items-center space-x-2", children: [_jsx(Loader2, { className: "h-6 w-6 animate-spin" }), _jsx("span", { children: "Generating income statement..." })] }) })) : (renderIncomeStatement()) }), _jsx(TabsContent, { value: "cash-flow", className: "space-y-4", children: loading ? (_jsx("div", { className: "flex items-center justify-center h-64", children: _jsxs("div", { className: "flex items-center space-x-2", children: [_jsx(Loader2, { className: "h-6 w-6 animate-spin" }), _jsx("span", { children: "Generating cash flow statement..." })] }) })) : (renderCashFlowStatement()) }), _jsx(TabsContent, { value: "trial-balance", className: "space-y-4", children: loading ? (_jsx("div", { className: "flex items-center justify-center h-64", children: _jsxs("div", { className: "flex items-center space-x-2", children: [_jsx(Loader2, { className: "h-6 w-6 animate-spin" }), _jsx("span", { children: "Generating trial balance..." })] }) })) : (renderTrialBalance()) })] })] }));
};
export default FinancialReports;
