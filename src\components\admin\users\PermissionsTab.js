import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { useUserManagement } from '@/context/UserManagementContext';
const PermissionsTab = () => {
    const { permissions } = useUserManagement();
    // Group permissions by category
    const permissionsByCategory = permissions.reduce((acc, permission) => {
        const { category } = permission;
        if (!acc[category])
            acc[category] = [];
        acc[category].push(permission);
        return acc;
    }, {});
    return (_jsxs(Card, { children: [_jsxs(CardHeader, { children: [_jsx(CardTitle, { children: "Permission Management" }), _jsx(CardDescription, { children: "View all available permissions in the system." })] }), _jsx(CardContent, { children: Object.entries(permissionsByCategory).map(([category, perms]) => (_jsxs("div", { className: "mb-8", children: [_jsxs("h3", { className: "text-lg font-medium mb-4", children: [category, " Permissions"] }), _jsx("div", { className: "grid grid-cols-1 md:grid-cols-2 gap-4", children: perms.map((permission) => (_jsx("div", { className: "p-4 border rounded-md flex justify-between items-center", children: _jsxs("div", { children: [_jsx("p", { className: "font-medium", children: permission.name }), _jsx("p", { className: "text-sm text-muted-foreground", children: permission.id })] }) }, permission.id))) })] }, category))) })] }));
};
export default PermissionsTab;
