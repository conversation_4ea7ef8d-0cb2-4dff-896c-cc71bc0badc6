/// <reference types="vite/client" />

import { createAuthClient } from 'better-auth/react';

// Determine auth URL based on environment
const authURL = import.meta.env.VITE_BETTER_AUTH_URL || 'https://nxtdotx.co.za/api/auth';

console.log('🔍 Auth configuration:', {
  authURL,
  windowLocation: window.location.href
});

export const authClient = createAuthClient({
  baseURL: authURL,
  fetchOptions: {
    credentials: 'include',
  },
  debug: true,
  onRequest: (request) => {
    console.log('🔍 [AUTH CLIENT] Request:', request);
  },
  onResponse: (response) => {
    console.log('🔍 [AUTH CLIENT] Response:', response);
  },
  onResponseError: (error) => {
    console.error('❌ [AUTH CLIENT] Response error:', error);
  },
});