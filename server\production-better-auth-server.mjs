import { betterAuth } from "better-auth";
import { drizzleAdapter } from "better-auth/adapters/drizzle";
import cors from "cors";
import dotenv from "dotenv";
import { drizzle } from "drizzle-orm/postgres-js";
import express from "express";
import path from "path";
import postgres from "postgres";
import { fromNodeHeaders } from "better-auth/node";
import { verifyPassword as customVerifyPassword, hashPassword as customHashPassword } from "../src/lib/custom-password.js";
import { eq, and } from "drizzle-orm";
import { user as userTable, account as accountTable } from "../src/lib/better-auth-schema.js";

// Load environment variables from root directory
dotenv.config();

// Simple rate limiting implementation (RELAXED FOR PRODUCTION)
const rateLimitMap = new Map();
const RATE_LIMIT_WINDOW = 15 * 60 * 1000; // 15 minutes
const RATE_LIMIT_MAX_REQUESTS = 1000; // requests per window (increased from 100)

function rateLimit(req, res, next) {
  const clientIP = req.ip || req.connection.remoteAddress;
  const now = Date.now();

  if (!rateLimitMap.has(clientIP)) {
    rateLimitMap.set(clientIP, { count: 1, resetTime: now + RATE_LIMIT_WINDOW });
    return next();
  }

  const clientData = rateLimitMap.get(clientIP);

  if (now > clientData.resetTime) {
    clientData.count = 1;
    clientData.resetTime = now + RATE_LIMIT_WINDOW;
    return next();
  }

  if (clientData.count >= RATE_LIMIT_MAX_REQUESTS) {
    return res.status(429).json({
      error: 'Too many requests',
      message: 'Rate limit exceeded. Please try again later.',
      retryAfter: Math.ceil((clientData.resetTime - now) / 1000)
    });
  }

  clientData.count++;
  next();
}
import { schema } from "../src/lib/better-auth-schema.js";
import fs from 'fs';

/**
 * Load environment variables for both development and production.
 * - If NODE_ENV=development and .env.local exists, load from .env.local
 * - Else if NODE_ENV=production and .env.production exists, load from .env.production
 * - Otherwise, fallback to default .env
 */
if (process.env.NODE_ENV === 'development' && fs.existsSync('.env.local')) {
  dotenv.config({ path: '.env.local' });
} else if (process.env.NODE_ENV === 'production' && fs.existsSync('.env.production')) {
  dotenv.config({ path: '.env.production' });
} else {
  dotenv.config();
}

console.log('🚀 Starting FIXED Production Better-Auth Server...');
console.log('🌐 Environment:', process.env.NODE_ENV || 'production');

const PORT = parseInt(process.env.PORT || '8000', 10);
console.log('🚪 Starting server on port:', PORT);

// Validate required environment variables
const requiredEnvVars = [
  'DATABASE_URL',
  'BETTER_AUTH_SECRET'
];

const missingVars = requiredEnvVars.filter(varName => !process.env[varName]);
if (missingVars.length > 0) {
  console.error('❌ Missing required environment variables:', missingVars.join(', '));
  process.exit(1);
}

console.log('✅ All required environment variables are set');
console.log('🔗 Connecting to production database...');

// Create database connection using postgres.js (more reliable than pg)
const client = postgres(process.env.DATABASE_URL, {
  ssl: process.env.NODE_ENV === 'production' ? 'require' : false,
  max: 20,
  idle_timeout: 30,
  connect_timeout: 10,
});

const db = drizzle(client);

// Test database connection
async function testConnection() {
  try {
    const result = await client`SELECT NOW() as current_time, version() as pg_version`;
    console.log('✅ Database connected successfully');
    console.log('📅 Server time:', result[0].current_time);
    console.log('🗄️  PostgreSQL version:', result[0].pg_version.split(' ')[0]);
    return true;
  } catch (error) {
    console.error('❌ Database connection failed:', error.message);
    return false;
  }
}

// Initialize Better Auth with Drizzle adapter (more stable)
let auth;
async function initializeBetterAuth() {
  try {
    console.log('🔧 Initializing Better Auth with Drizzle adapter...');

    // Test database connection first
    await client`SELECT 1`;
    console.log('✅ Database connection verified for Better Auth');

    auth = betterAuth({
      database: drizzleAdapter(db, {
        provider: "pg",
        schema: schema,
      }),
      emailAndPassword: {
        enabled: true,
        requireEmailVerification: false,
        password: {
          verify: customVerifyPassword,
          hash: customHashPassword,
        }
      },
      session: {
        expiresIn: 60 * 60 * 24 * 7, // 7 days
        updateAge: 60 * 60 * 24, // 24 hours
      },
      secret: process.env.BETTER_AUTH_SECRET,
      baseURL: process.env.BETTER_AUTH_URL || 'https://localhost:9000',
      trustedOrigins: [
        'https://localhost:9000',
        'http://localhost:9000',
        'http://localhost:3000',
        'http://localhost:5173',
        'http://localhost:3001',
        'http://localhost:3002',
        'https://nxtdotx.co.za',
        'https://www.nxtdotx.co.za'
      ],
      advanced: {
        // Let Better Auth generate UUIDs for primary keys
        crossSubDomainCookies: {
          enabled: true,
          domain: '.nxtdotx.co.za'
        }
      }
    });

    console.log('✅ Better Auth initialized successfully');
    return true;
  } catch (error) {
    console.error('❌ Better Auth initialization failed:', error);
    console.error('🔍 Error details:', error.message);
    return false;
  }
}

// Create Express app
const app = express();

// Apply rate limiting (DISABLED FOR PRODUCTION)
// app.use(rateLimit);

// CORS configuration
app.use(cors({
  origin: [
    'https://nxtdotx.co.za',
    'https://www.nxtdotx.co.za',
    'http://localhost:3000',
    'http://localhost:5173'
  ],
  credentials: true
}));

// Security middleware
app.use((req, res, next) => {
  // Security headers
  res.setHeader('X-Content-Type-Options', 'nosniff');
  res.setHeader('X-Frame-Options', 'DENY');
  res.setHeader('X-XSS-Protection', '1; mode=block');
  res.setHeader('Referrer-Policy', 'strict-origin-when-cross-origin');
  res.setHeader('Permissions-Policy', 'geolocation=(), microphone=(), camera=()');

  // HTTPS redirect in production
  if (process.env.NODE_ENV === 'production') {
    return res.redirect(`https://${req.header('host')}${req.url}`);
  }

  next();
});

app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));
app.use(express.static('dist'));

// Health check endpoint
app.get("/health", (req, res) => {
  res.status(200).json({
    status: "healthy",
    timestamp: new Date().toISOString(),
    database: "connected"
  });
});

// Version check endpoint to verify deployment
app.get("/api/version", (req, res) => {
  res.status(200).json({
    version: "ES_MODULE_FIX_v0973c23",
    timestamp: new Date().toISOString(),
    commit: "0973c23",
    fix: "es_module_syntax_and_auth_flow",
    status: "PRODUCTION_READY",
    auth_handler: "toNodeHandler(auth)"
  });
});

// Production logging middleware
app.use((req, res, next) => {
  const start = Date.now();
  const originalSend = res.send;

  res.send = function(data) {
    const duration = Date.now() - start;
    console.log(`${new Date().toISOString()} ${req.method} ${req.url} ${res.statusCode} ${duration}ms`);
    originalSend.call(this, data);
  };

  next();
});

// Global error handlers
process.on('uncaughtException', (error) => {
  console.error(`${new Date().toISOString()} ❌ Uncaught Exception:`, error.message);
  console.error('🔍 Stack:', error.stack);
  // In production, you might want to restart the process
  process.exit(1);
});

process.on('unhandledRejection', (reason, promise) => {
  console.error(`${new Date().toISOString()} ❌ Unhandled Rejection at:`, promise, 'reason:', reason);
  // In production, you might want to restart the process
  process.exit(1);
});

// Start server after successful database connection and auth initialization
testConnection().then(async (dbSuccess) => {
  if (dbSuccess) {
    const authSuccess = await initializeBetterAuth();
    if (authSuccess) {
      // Mount Better Auth endpoints BEFORE catch-all route
      // Use Better Auth's native Node handler with proper request handling
      app.use("/api/auth*", async (req, res) => {
        try {
          // Create proper request object with full URL
          const protocol = req.secure || req.headers['x-forwarded-proto'] === 'https' ? 'https' : 'http';
          const host = req.headers.host || 'nxtdotx.co.za';
          const fullUrl = `${protocol}://${host}${req.originalUrl}`;

          // Log full request details
console.log('🔍 Full request details:');
console.log('  - Method:', req.method);
console.log('  - Path:', req.path);
console.log('  - Headers:', req.headers);
console.log('  - Body:', req.body);

// Debug request body
          console.log('🔍 Request method:', req.method);
          console.log('🔍 Request body:', req.body);
          console.log('🔍 Request body type:', typeof req.body);
          console.log('🔍 Request URL:', req.originalUrl);

          // Add database debugging for login attempts
          if (req.method === 'POST' && req.body && req.body.email) {
            try {
              console.log('🔍 DEBUG: Checking database for user:', req.body.email);

              // Check user table
              const userResult = await db.select().from(userTable).where(eq(userTable.email, req.body.email));
              console.log('🔍 User query result:', userResult);

              if (userResult.length > 0) {
                // Check account table
                const accountResult = await db.select().from(accountTable)
                  .where(and(
                    eq(accountTable.accountId, req.body.email),
                    eq(accountTable.providerId, 'credential')
                  ));
                console.log('🔍 Account query result:', accountResult);

                if (accountResult.length > 0) {
                  console.log('🔍 Password field type:', typeof accountResult[0].password);
                  console.log('🔍 Password field length:', accountResult[0].password ? accountResult[0].password.length : 'NULL');
                  console.log('🔍 Password starts with:', accountResult[0].password ? accountResult[0].password.substring(0, 10) : 'NULL');
                }
              }
            } catch (dbError) {
              console.error('🔍 Database debug error:', dbError);
            }
          }

          // Prepare request body properly
          let requestBody = undefined;
          if (req.method !== 'GET' && req.method !== 'HEAD') {
            if (req.body) {
              // If body is already parsed (object), stringify it
              requestBody = typeof req.body === 'string' ? req.body : JSON.stringify(req.body);
              console.log('🔍 Prepared request body:', requestBody);
            } else {
              console.log('❌ No request body found!');
            }
          }

          // Create a new request object with the full URL
          const nodeRequest = new Request(fullUrl, {
            method: req.method,
            headers: fromNodeHeaders(req.headers),
            body: requestBody,
          });

          // Handle with Better Auth
          const response = await auth.handler(nodeRequest);

          // Log auth response
console.log('🔍 Auth response:', response);

// Send response
          res.status(response.status);
          response.headers.forEach((value, key) => {
            res.setHeader(key, value);
          });

          const responseBody = await response.text();
          res.send(responseBody);
        } catch (error) {
          console.error('❌ Better Auth handler error:', error);
          res.status(500).json({ error: 'Authentication error' });
        }
      });

      // Serve frontend for all other routes (MUST be last)
      app.get('*', (req, res) => {
        res.sendFile(path.join(process.cwd(), 'dist/index.html'));
      });
      
      const server = app.listen(PORT, () => {
        const baseUrl = process.env.NODE_ENV === 'development' ? `https://localhost:${PORT}` : 'https://nxtdotx.co.za';
        console.log(`✅ FIXED Better Auth Server running on port ${PORT}`);
        console.log(`🌐 Frontend: ${baseUrl}`);
        console.log(`🔐 Auth API: ${baseUrl}/api/auth`);
        console.log(`🔍 Health: ${baseUrl}/health`);
      });
      
      server.on('error', (error) => {
        console.error('❌ Server error:', error);
      });
    } else {
      console.error('❌ Server failed to start due to Better Auth initialization issues');
      process.exit(1);
    }
  } else {
    console.error('❌ Server failed to start due to database connection issues');
    process.exit(1);
  }
}).catch(error => {
  console.error('❌ Startup error:', error);
  process.exit(1);
});
