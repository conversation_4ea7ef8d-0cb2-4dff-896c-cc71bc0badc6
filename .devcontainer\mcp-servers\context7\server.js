#!/usr/bin/env node

const { Server } = require('@modelcontextprotocol/sdk/server/index.js');
const { StdioServerTransport } = require('@modelcontextprotocol/sdk/server/stdio.js');
const { CallToolRequestSchema, ListToolsRequestSchema } = require('@modelcontextprotocol/sdk/types.js');
const { Redis } = require('@upstash/redis');
const express = require('express');

class Context7Server {
  constructor() {
    this.server = new Server(
      {
        name: 'context7-server',
        version: '1.0.0',
      },
      {
        capabilities: {
          tools: {},
        },
      }
    );

    this.redisUrl = process.env.UPSTASH_REDIS_REST_URL;
    this.redisToken = process.env.UPSTASH_REDIS_REST_TOKEN;
    this.port = process.env.MCP_PORT || 8083;
    
    if (this.redisUrl && this.redisToken) {
      this.redis = new Redis({
        url: this.redisUrl,
        token: this.redisToken,
      });
    }
    
    this.setupToolHandlers();
    this.setupHealthCheck();
  }

  setupToolHandlers() {
    this.server.setRequestHandler(ListToolsRequestSchema, async () => {
      return {
        tools: [
          {
            name: 'context7_store',
            description: 'Store context data in Redis with semantic search capabilities',
            inputSchema: {
              type: 'object',
              properties: {
                key: {
                  type: 'string',
                  description: 'Unique key for the context data',
                },
                content: {
                  type: 'string',
                  description: 'Content to store',
                },
                metadata: {
                  type: 'object',
                  description: 'Additional metadata',
                  properties: {
                    title: { type: 'string' },
                    tags: { type: 'array', items: { type: 'string' } },
                    timestamp: { type: 'string' },
                    source: { type: 'string' },
                  },
                },
                ttl: {
                  type: 'number',
                  description: 'Time to live in seconds',
                },
              },
              required: ['key', 'content'],
            },
          },
          {
            name: 'context7_retrieve',
            description: 'Retrieve context data by key',
            inputSchema: {
              type: 'object',
              properties: {
                key: {
                  type: 'string',
                  description: 'Key to retrieve',
                },
              },
              required: ['key'],
            },
          },
          {
            name: 'context7_search',
            description: 'Search context data by pattern or content',
            inputSchema: {
              type: 'object',
              properties: {
                pattern: {
                  type: 'string',
                  description: 'Search pattern (supports wildcards)',
                },
                query: {
                  type: 'string',
                  description: 'Content search query',
                },
                limit: {
                  type: 'number',
                  description: 'Maximum number of results',
                  default: 10,
                },
                tags: {
                  type: 'array',
                  description: 'Filter by tags',
                  items: { type: 'string' },
                },
              },
            },
          },
          {
            name: 'context7_delete',
            description: 'Delete context data by key',
            inputSchema: {
              type: 'object',
              properties: {
                key: {
                  type: 'string',
                  description: 'Key to delete',
                },
              },
              required: ['key'],
            },
          },
          {
            name: 'context7_list_keys',
            description: 'List all available keys',
            inputSchema: {
              type: 'object',
              properties: {
                pattern: {
                  type: 'string',
                  description: 'Pattern to filter keys (supports wildcards)',
                  default: '*',
                },
                limit: {
                  type: 'number',
                  description: 'Maximum number of keys to return',
                  default: 100,
                },
              },
            },
          },
        ],
      };
    });

    this.server.setRequestHandler(CallToolRequestSchema, async (request) => {
      const { name, arguments: args } = request.params;

      switch (name) {
        case 'context7_store':
          return await this.handleStore(args);
        case 'context7_retrieve':
          return await this.handleRetrieve(args);
        case 'context7_search':
          return await this.handleSearch(args);
        case 'context7_delete':
          return await this.handleDelete(args);
        case 'context7_list_keys':
          return await this.handleListKeys(args);
        default:
          throw new Error(`Unknown tool: ${name}`);
      }
    });
  }

  async handleStore(args) {
    if (!this.redis) {
      throw new Error('Redis connection not configured. Please set UPSTASH_REDIS_REST_URL and UPSTASH_REDIS_REST_TOKEN');
    }

    try {
      const data = {
        content: args.content,
        metadata: args.metadata || {},
        timestamp: new Date().toISOString(),
      };

      if (args.ttl) {
        await this.redis.setex(args.key, args.ttl, JSON.stringify(data));
      } else {
        await this.redis.set(args.key, JSON.stringify(data));
      }

      return {
        content: [
          {
            type: 'text',
            text: JSON.stringify({
              success: true,
              key: args.key,
              message: 'Context data stored successfully',
              ttl: args.ttl || null,
            }, null, 2),
          },
        ],
      };
    } catch (error) {
      throw new Error(`Context7 Store error: ${error.message}`);
    }
  }

  async handleRetrieve(args) {
    if (!this.redis) {
      throw new Error('Redis connection not configured');
    }

    try {
      const data = await this.redis.get(args.key);
      
      if (!data) {
        return {
          content: [
            {
              type: 'text',
              text: JSON.stringify({
                success: false,
                key: args.key,
                message: 'Key not found',
              }, null, 2),
            },
          ],
        };
      }

      const parsedData = typeof data === 'string' ? JSON.parse(data) : data;

      return {
        content: [
          {
            type: 'text',
            text: JSON.stringify({
              success: true,
              key: args.key,
              data: parsedData,
            }, null, 2),
          },
        ],
      };
    } catch (error) {
      throw new Error(`Context7 Retrieve error: ${error.message}`);
    }
  }

  async handleSearch(args) {
    if (!this.redis) {
      throw new Error('Redis connection not configured');
    }

    try {
      const pattern = args.pattern || '*';
      const keys = await this.redis.keys(pattern);
      const limit = args.limit || 10;
      const results = [];

      for (let i = 0; i < Math.min(keys.length, limit); i++) {
        const key = keys[i];
        const data = await this.redis.get(key);
        
        if (data) {
          const parsedData = typeof data === 'string' ? JSON.parse(data) : data;
          
          // Simple content search
          if (args.query) {
            const content = parsedData.content || '';
            if (!content.toLowerCase().includes(args.query.toLowerCase())) {
              continue;
            }
          }

          // Tag filtering
          if (args.tags && args.tags.length > 0) {
            const dataTags = parsedData.metadata?.tags || [];
            const hasMatchingTag = args.tags.some(tag => dataTags.includes(tag));
            if (!hasMatchingTag) {
              continue;
            }
          }

          results.push({
            key,
            data: parsedData,
          });
        }
      }

      return {
        content: [
          {
            type: 'text',
            text: JSON.stringify({
              success: true,
              query: args.query || null,
              pattern: pattern,
              results,
              total_found: results.length,
            }, null, 2),
          },
        ],
      };
    } catch (error) {
      throw new Error(`Context7 Search error: ${error.message}`);
    }
  }

  async handleDelete(args) {
    if (!this.redis) {
      throw new Error('Redis connection not configured');
    }

    try {
      const result = await this.redis.del(args.key);
      
      return {
        content: [
          {
            type: 'text',
            text: JSON.stringify({
              success: result > 0,
              key: args.key,
              message: result > 0 ? 'Key deleted successfully' : 'Key not found',
            }, null, 2),
          },
        ],
      };
    } catch (error) {
      throw new Error(`Context7 Delete error: ${error.message}`);
    }
  }

  async handleListKeys(args) {
    if (!this.redis) {
      throw new Error('Redis connection not configured');
    }

    try {
      const pattern = args.pattern || '*';
      const keys = await this.redis.keys(pattern);
      const limit = args.limit || 100;
      const limitedKeys = keys.slice(0, limit);

      return {
        content: [
          {
            type: 'text',
            text: JSON.stringify({
              success: true,
              pattern,
              keys: limitedKeys,
              total_keys: keys.length,
              returned_keys: limitedKeys.length,
            }, null, 2),
          },
        ],
      };
    } catch (error) {
      throw new Error(`Context7 List Keys error: ${error.message}`);
    }
  }

  setupHealthCheck() {
    const app = express();
    
    app.get('/health', async (req, res) => {
      let redisStatus = 'not_configured';
      
      if (this.redis) {
        try {
          await this.redis.ping();
          redisStatus = 'connected';
        } catch (error) {
          redisStatus = 'error';
        }
      }

      res.json({ 
        status: 'healthy', 
        service: 'context7-mcp',
        timestamp: new Date().toISOString(),
        redis_status: redisStatus,
        redis_configured: !!(this.redisUrl && this.redisToken)
      });
    });

    app.listen(this.port, () => {
      console.log(`Context7 MCP Server health check running on port ${this.port}`);
    });
  }

  async run() {
    const transport = new StdioServerTransport();
    await this.server.connect(transport);
    console.error('Context7 MCP server running on stdio');
  }
}

const server = new Context7Server();
server.run().catch(console.error);
