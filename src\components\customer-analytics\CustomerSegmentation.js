import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { PieChart, Pie, Cell, BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, ScatterChart, Scatter } from 'recharts';
import { Users, TrendingUp, Star, Target, Crown } from 'lucide-react';
const CustomerSegmentation = ({ segments = [] }) => {
    // Default segment data if none provided
    const defaultSegments = [
        {
            name: 'VIP Champions',
            customers: 78,
            revenue: 327600,
            avgOrderValue: 680,
            frequency: 8.2,
            satisfaction: 4.9,
            color: '#8b5cf6'
        },
        {
            name: 'Loyal Customers',
            customers: 156,
            revenue: 444600,
            avgOrderValue: 425,
            frequency: 6.7,
            satisfaction: 4.6,
            color: '#3b82f6'
        },
        {
            name: 'Potential Loyalists',
            customers: 342,
            revenue: 974700,
            avgOrderValue: 285,
            frequency: 4.2,
            satisfaction: 4.2,
            color: '#10b981'
        },
        {
            name: 'New Customers',
            customers: 189,
            revenue: 274050,
            avgOrderValue: 145,
            frequency: 1.8,
            satisfaction: 4.0,
            color: '#f59e0b'
        },
        {
            name: 'At Risk',
            customers: 98,
            revenue: 127400,
            avgOrderValue: 130,
            frequency: 1.2,
            satisfaction: 3.5,
            color: '#ef4444'
        },
        {
            name: 'Cannot Lose Them',
            customers: 45,
            revenue: 180000,
            avgOrderValue: 500,
            frequency: 2.1,
            satisfaction: 3.8,
            color: '#f97316'
        }
    ];
    const segmentData = segments.length > 0 ? segments : defaultSegments;
    // Calculate totals
    const totalCustomers = segmentData.reduce((sum, segment) => sum + segment.customers, 0);
    const totalRevenue = segmentData.reduce((sum, segment) => sum + segment.revenue, 0);
    // Prepare data for charts
    const pieData = segmentData.map(segment => ({
        name: segment.name,
        value: segment.customers,
        percentage: ((segment.customers / totalCustomers) * 100).toFixed(1),
        color: segment.color
    }));
    const revenueData = segmentData.map(segment => ({
        name: segment.name,
        revenue: segment.revenue,
        customers: segment.customers,
        color: segment.color
    }));
    const rfmData = segmentData.map(segment => ({
        name: segment.name,
        frequency: segment.frequency,
        avgOrderValue: segment.avgOrderValue,
        customers: segment.customers,
        color: segment.color
    }));
    const getSegmentIcon = (segmentName) => {
        switch (segmentName) {
            case 'VIP Champions':
                return Crown;
            case 'Loyal Customers':
                return Star;
            case 'Potential Loyalists':
                return Target;
            case 'At Risk':
                return TrendingUp;
            default:
                return Users;
        }
    };
    const getSegmentDescription = (segmentName) => {
        switch (segmentName) {
            case 'VIP Champions':
                return 'High-value customers with frequent purchases and excellent satisfaction';
            case 'Loyal Customers':
                return 'Regular customers with consistent purchase behavior';
            case 'Potential Loyalists':
                return 'Recent customers with potential for increased loyalty';
            case 'New Customers':
                return 'Recently acquired customers requiring nurturing';
            case 'At Risk':
                return 'Customers showing declining engagement patterns';
            case 'Cannot Lose Them':
                return 'High-value customers with decreasing activity';
            default:
                return 'Customer segment requiring analysis';
        }
    };
    const getSegmentStrategy = (segmentName) => {
        switch (segmentName) {
            case 'VIP Champions':
                return 'Reward loyalty, exclusive offers, premium support';
            case 'Loyal Customers':
                return 'Upsell opportunities, referral programs';
            case 'Potential Loyalists':
                return 'Engagement campaigns, loyalty program enrollment';
            case 'New Customers':
                return 'Onboarding optimization, education content';
            case 'At Risk':
                return 'Win-back campaigns, satisfaction surveys';
            case 'Cannot Lose Them':
                return 'Immediate intervention, personalized offers';
            default:
                return 'Develop targeted strategy';
        }
    };
    return (_jsxs("div", { className: "space-y-6", children: [_jsx("div", { className: "grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4", children: segmentData.slice(0, 3).map((segment, index) => {
                    const IconComponent = getSegmentIcon(segment.name);
                    return (_jsx(Card, { children: _jsxs(CardContent, { className: "p-4", children: [_jsxs("div", { className: "flex items-center justify-between mb-3", children: [_jsxs("div", { className: "flex items-center", children: [_jsx("div", { className: "p-2 rounded-full mr-3", style: { backgroundColor: `${segment.color}20` }, children: _jsx(IconComponent, { className: "h-4 w-4", style: { color: segment.color } }) }), _jsxs("div", { children: [_jsx("h3", { className: "font-semibold text-sm", children: segment.name }), _jsxs("p", { className: "text-xs text-gray-600", children: [segment.customers, " customers"] })] })] }), _jsxs(Badge, { style: { backgroundColor: segment.color, color: 'white' }, children: [((segment.customers / totalCustomers) * 100).toFixed(1), "%"] })] }), _jsxs("div", { className: "space-y-2", children: [_jsxs("div", { className: "flex justify-between text-xs", children: [_jsx("span", { className: "text-gray-600", children: "Revenue:" }), _jsxs("span", { className: "font-semibold", children: ["$", (segment.revenue / 1000).toFixed(0), "K"] })] }), _jsxs("div", { className: "flex justify-between text-xs", children: [_jsx("span", { className: "text-gray-600", children: "AOV:" }), _jsxs("span", { className: "font-semibold", children: ["$", segment.avgOrderValue] })] }), _jsxs("div", { className: "flex justify-between text-xs", children: [_jsx("span", { className: "text-gray-600", children: "Satisfaction:" }), _jsxs("span", { className: "font-semibold", children: [segment.satisfaction, "/5"] })] })] })] }) }, index));
                }) }), _jsxs("div", { className: "grid grid-cols-1 lg:grid-cols-2 gap-6", children: [_jsxs(Card, { children: [_jsx(CardHeader, { children: _jsx(CardTitle, { children: "Customer Distribution by Segment" }) }), _jsx(CardContent, { children: _jsx(ResponsiveContainer, { width: "100%", height: 300, children: _jsxs(PieChart, { children: [_jsx(Pie, { data: pieData, cx: "50%", cy: "50%", outerRadius: 80, dataKey: "value", label: ({ name, percentage }) => `${name}: ${percentage}%`, children: pieData.map((entry, index) => (_jsx(Cell, { fill: entry.color }, `cell-${index}`))) }), _jsx(Tooltip, {})] }) }) })] }), _jsxs(Card, { children: [_jsx(CardHeader, { children: _jsx(CardTitle, { children: "Revenue by Segment" }) }), _jsx(CardContent, { children: _jsx(ResponsiveContainer, { width: "100%", height: 300, children: _jsxs(BarChart, { data: revenueData, children: [_jsx(CartesianGrid, { strokeDasharray: "3 3" }), _jsx(XAxis, { dataKey: "name", angle: -45, textAnchor: "end", height: 80 }), _jsx(YAxis, {}), _jsx(Tooltip, { formatter: (value) => [`$${(value / 1000).toFixed(0)}K`, 'Revenue'] }), _jsx(Bar, { dataKey: "revenue", fill: "#3b82f6" })] }) }) })] })] }), _jsxs(Card, { children: [_jsxs(CardHeader, { children: [_jsx(CardTitle, { children: "RFM Analysis: Frequency vs Average Order Value" }), _jsx("p", { className: "text-sm text-gray-600", children: "Bubble size represents customer count" })] }), _jsx(CardContent, { children: _jsx(ResponsiveContainer, { width: "100%", height: 400, children: _jsxs(ScatterChart, { data: rfmData, children: [_jsx(CartesianGrid, { strokeDasharray: "3 3" }), _jsx(XAxis, { dataKey: "frequency", name: "Purchase Frequency" }), _jsx(YAxis, { dataKey: "avgOrderValue", name: "Average Order Value" }), _jsx(Tooltip, { cursor: { strokeDasharray: '3 3' }, formatter: (value, name) => [
                                            name === 'avgOrderValue' ? `$${value}` : value,
                                            name === 'avgOrderValue' ? 'AOV' : 'Frequency'
                                        ] }), rfmData.map((segment, index) => (_jsx(Scatter, { dataKey: "customers", fill: segment.color, name: segment.name }, index)))] }) }) })] }), _jsxs(Card, { children: [_jsx(CardHeader, { children: _jsx(CardTitle, { children: "Segment Analysis & Strategies" }) }), _jsx(CardContent, { children: _jsx("div", { className: "space-y-4", children: segmentData.map((segment, index) => {
                                const IconComponent = getSegmentIcon(segment.name);
                                return (_jsxs("div", { className: "border rounded-lg p-4", children: [_jsxs("div", { className: "flex items-start justify-between mb-3", children: [_jsxs("div", { className: "flex items-center", children: [_jsx("div", { className: "p-2 rounded-full mr-3", style: { backgroundColor: `${segment.color}20` }, children: _jsx(IconComponent, { className: "h-5 w-5", style: { color: segment.color } }) }), _jsxs("div", { children: [_jsx("h3", { className: "font-semibold text-lg", children: segment.name }), _jsx("p", { className: "text-sm text-gray-600", children: getSegmentDescription(segment.name) })] })] }), _jsxs(Badge, { style: { backgroundColor: segment.color, color: 'white' }, children: [segment.customers, " customers"] })] }), _jsxs("div", { className: "grid grid-cols-1 md:grid-cols-4 gap-4 mb-3", children: [_jsxs("div", { className: "text-center", children: [_jsxs("div", { className: "text-lg font-bold", style: { color: segment.color }, children: ["$", (segment.revenue / 1000).toFixed(0), "K"] }), _jsx("div", { className: "text-xs text-gray-600", children: "Total Revenue" })] }), _jsxs("div", { className: "text-center", children: [_jsxs("div", { className: "text-lg font-bold", style: { color: segment.color }, children: ["$", segment.avgOrderValue] }), _jsx("div", { className: "text-xs text-gray-600", children: "Avg Order Value" })] }), _jsxs("div", { className: "text-center", children: [_jsxs("div", { className: "text-lg font-bold", style: { color: segment.color }, children: [segment.frequency, "x"] }), _jsx("div", { className: "text-xs text-gray-600", children: "Purchase Frequency" })] }), _jsxs("div", { className: "text-center", children: [_jsxs("div", { className: "text-lg font-bold", style: { color: segment.color }, children: [segment.satisfaction, "/5"] }), _jsx("div", { className: "text-xs text-gray-600", children: "Satisfaction" })] })] }), _jsxs("div", { className: "bg-gray-50 rounded p-3", children: [_jsx("h4", { className: "font-medium text-sm mb-1", children: "Recommended Strategy:" }), _jsx("p", { className: "text-sm text-gray-700", children: getSegmentStrategy(segment.name) })] })] }, index));
                            }) }) })] })] }));
};
export default CustomerSegmentation;
