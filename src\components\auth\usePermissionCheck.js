import { useBetterAuth } from '@/providers/BetterAuthProvider';
import { UserRole } from '@/auth';
// Hook for checking permissions in components
export const usePermissionCheck = () => {
    const { user, hasPermission: contextHasPermission } = useBetterAuth(); // Renamed to avoid conflict
    const hasAnyPermission = (permissions) => {
        if (!user)
            return false;
        return permissions.some(permission => contextHasPermission(permission));
    };
    const hasAllPermissions = (permissions) => {
        if (!user)
            return false;
        return permissions.every(permission => contextHasPermission(permission));
    };
    const isRole = (role) => {
        return user?.role === role;
    };
    // Define role hierarchy for isRoleOrHigher
    const roleHierarchy = {
        [UserRole.GUEST]: 1,
        [UserRole.USER]: 2,
        [UserRole.MANAGER]: 3,
        [UserRole.ADMIN]: 4
    };
    const isRoleOrHigher = (role) => {
        if (!user?.role)
            return false;
        const userRoleLevel = roleHierarchy[user.role] || 0;
        const requiredRoleLevel = roleHierarchy[role] || 0;
        return userRoleLevel >= requiredRoleLevel;
    };
    return {
        user, // Expose user if needed by consumers of this hook
        hasPermission: contextHasPermission, // Expose the original hasPermission from context
        hasAnyPermission,
        hasAllPermissions,
        isRole,
        isRoleOrHigher,
        checkAccess: (permissions, requireAll = false) => {
            return requireAll ? hasAllPermissions(permissions) : hasAnyPermission(permissions);
        }
    };
};
