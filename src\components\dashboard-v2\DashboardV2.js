import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
import KpiCard from './KpiCard';
const DashboardV2 = () => {
    return (_jsxs("div", { className: "grid grid-cols-4 gap-4 p-4", children: [_jsx(KpiCard, { title: "Active Projects", value: "12", change: 5 }), _jsx(KpiCard, { title: "Overdue Tasks", value: "3", change: -2 }), _jsx(KpiCard, { title: "Team Capacity", value: "87%", change: 1 }), _jsx(KpiCard, { title: "Budget Variance", value: "-8%", change: -1 })] }));
};
export default DashboardV2;
