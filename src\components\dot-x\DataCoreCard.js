import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
export const DataCoreCard = ({ core = { activity: 75, status: "Operational" } }) => {
    return (_jsxs("div", { className: "frosted-card h-full", children: [_jsx("div", { className: "glossy-overlay" }), _jsxs("div", { className: "flex flex-col h-full", children: [_jsx("h3", { className: "text-silver-300/80 mb-4 font-medium text-sm", children: "Core Activity" }), _jsxs("div", { className: "flex flex-col items-center justify-center py-3 flex-1", children: [_jsxs("div", { className: "relative h-36 w-36 flex items-center justify-center", children: [_jsx("div", { className: "absolute h-full w-full rounded-full border-[16px] border-black-800/70" }), _jsxs("svg", { className: "absolute h-full w-full -rotate-90", viewBox: "0 0 100 100", children: [_jsx("defs", { children: _jsxs("linearGradient", { id: "progressGradient", x1: "0%", y1: "0%", x2: "100%", y2: "0%", children: [_jsx("stop", { offset: "0%", stopColor: "#8E0D0D" }), _jsx("stop", { offset: "100%", stopColor: "#BFBFBF" })] }) }), _jsx("circle", { cx: "50", cy: "50", r: "42", fill: "none", strokeWidth: "16", stroke: "url(#progressGradient)", strokeDasharray: `${core.activity * 2.64} 264` })] }), _jsxs("div", { className: "text-center", children: [_jsx("span", { className: "text-4xl font-bold tabular-nums text-silver-100", children: core.activity }), _jsx("span", { className: "text-2xl font-semibold text-silver-300/80", children: "%" })] })] }), _jsx("p", { className: "mt-2 text-xl font-medium text-center text-silver-300", children: "System Status" }), _jsx("p", { className: "text-silver-300/70 text-sm mt-1", children: core.status })] })] })] }));
};
