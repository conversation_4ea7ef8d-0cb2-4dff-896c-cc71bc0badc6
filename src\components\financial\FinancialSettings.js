import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { Separator } from '@/components/ui/separator';
import { Settings, Save, RefreshCw } from 'lucide-react';
/**
 * Financial Settings Component
 * Provides configuration options for the financial module
 */
const FinancialSettings = () => {
    return (_jsxs("div", { className: "container mx-auto p-6 space-y-6", children: [_jsxs("div", { className: "flex items-center gap-2 mb-6", children: [_jsx(Settings, { className: "h-6 w-6" }), _jsx("h1", { className: "text-2xl font-bold", children: "Financial Settings" })] }), _jsxs("div", { className: "grid grid-cols-1 lg:grid-cols-2 gap-6", children: [_jsxs(Card, { children: [_jsx(CardHeader, { children: _jsx(CardTitle, { children: "General Settings" }) }), _jsxs(CardContent, { className: "space-y-4", children: [_jsxs("div", { className: "space-y-2", children: [_jsx(Label, { htmlFor: "company-name", children: "Company Name" }), _jsx(Input, { id: "company-name", placeholder: "Enter company name" })] }), _jsxs("div", { className: "space-y-2", children: [_jsx(Label, { htmlFor: "fiscal-year", children: "Fiscal Year Start" }), _jsx(Input, { id: "fiscal-year", type: "date" })] }), _jsxs("div", { className: "space-y-2", children: [_jsx(Label, { htmlFor: "currency", children: "Default Currency" }), _jsxs("div", { className: "flex gap-2", children: [_jsx(Input, { id: "currency", placeholder: "ZAR", defaultValue: "ZAR", className: "flex-1" }), _jsx(Button, { variant: "outline", size: "sm", onClick: () => window.location.href = '/dashboard/settings?tab=currency', children: "Configure" })] }), _jsxs("p", { className: "text-sm text-muted-foreground", children: ["Set platform-wide currency in ", _jsx("a", { href: "/dashboard/settings?tab=currency", className: "text-blue-600 hover:underline", children: "Platform Settings" })] })] }), _jsxs("div", { className: "flex items-center space-x-2", children: [_jsx(Switch, { id: "multi-currency" }), _jsx(Label, { htmlFor: "multi-currency", children: "Enable Multi-Currency" })] })] })] }), _jsxs(Card, { children: [_jsx(CardHeader, { children: _jsx(CardTitle, { children: "Accounting Settings" }) }), _jsxs(CardContent, { className: "space-y-4", children: [_jsxs("div", { className: "space-y-2", children: [_jsx(Label, { htmlFor: "chart-template", children: "Chart of Accounts Template" }), _jsx(Input, { id: "chart-template", placeholder: "Standard Business" })] }), _jsxs("div", { className: "flex items-center space-x-2", children: [_jsx(Switch, { id: "auto-reconcile" }), _jsx(Label, { htmlFor: "auto-reconcile", children: "Auto-Reconciliation" })] }), _jsxs("div", { className: "flex items-center space-x-2", children: [_jsx(Switch, { id: "require-approval" }), _jsx(Label, { htmlFor: "require-approval", children: "Require Journal Entry Approval" })] }), _jsxs("div", { className: "space-y-2", children: [_jsx(Label, { htmlFor: "retention-period", children: "Data Retention Period (Years)" }), _jsx(Input, { id: "retention-period", type: "number", placeholder: "7" })] })] })] }), _jsxs(Card, { children: [_jsx(CardHeader, { children: _jsx(CardTitle, { children: "Tax Settings" }) }), _jsxs(CardContent, { className: "space-y-4", children: [_jsxs("div", { className: "space-y-2", children: [_jsx(Label, { htmlFor: "tax-jurisdiction", children: "Tax Jurisdiction" }), _jsx(Input, { id: "tax-jurisdiction", placeholder: "Enter jurisdiction" })] }), _jsxs("div", { className: "space-y-2", children: [_jsx(Label, { htmlFor: "default-tax-rate", children: "Default Tax Rate (%)" }), _jsx(Input, { id: "default-tax-rate", type: "number", placeholder: "15" })] }), _jsxs("div", { className: "flex items-center space-x-2", children: [_jsx(Switch, { id: "auto-tax-calc" }), _jsx(Label, { htmlFor: "auto-tax-calc", children: "Automatic Tax Calculation" })] })] })] }), _jsxs(Card, { children: [_jsx(CardHeader, { children: _jsx(CardTitle, { children: "Reporting Settings" }) }), _jsxs(CardContent, { className: "space-y-4", children: [_jsxs("div", { className: "space-y-2", children: [_jsx(Label, { htmlFor: "report-format", children: "Default Report Format" }), _jsx(Input, { id: "report-format", placeholder: "PDF" })] }), _jsxs("div", { className: "flex items-center space-x-2", children: [_jsx(Switch, { id: "auto-backup" }), _jsx(Label, { htmlFor: "auto-backup", children: "Automatic Report Backup" })] }), _jsxs("div", { className: "space-y-2", children: [_jsx(Label, { htmlFor: "backup-frequency", children: "Backup Frequency" }), _jsx(Input, { id: "backup-frequency", placeholder: "Daily" })] })] })] })] }), _jsx(Separator, {}), _jsxs("div", { className: "flex justify-end space-x-4", children: [_jsxs(Button, { variant: "outline", children: [_jsx(RefreshCw, { className: "h-4 w-4 mr-2" }), "Reset to Defaults"] }), _jsxs(Button, { children: [_jsx(Save, { className: "h-4 w-4 mr-2" }), "Save Settings"] })] })] }));
};
export default FinancialSettings;
