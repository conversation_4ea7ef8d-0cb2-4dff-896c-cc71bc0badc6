# Better Auth Troubleshooting Scripts

## 1. Server Connectivity Test Script

Create this file as `test-server-connectivity.js`:

```javascript
#!/usr/bin/env node

const https = require('https');
const http = require('http');

async function testEndpoint(url, method = 'GET', data = null) {
  return new Promise((resolve, reject) => {
    const urlObj = new URL(url);
    const options = {
      hostname: urlObj.hostname,
      port: urlObj.port || (urlObj.protocol === 'https:' ? 443 : 80),
      path: urlObj.pathname + urlObj.search,
      method: method,
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
        'User-Agent': 'Better-Auth-Test/1.0'
      }
    };

    if (data) {
      const postData = JSON.stringify(data);
      options.headers['Content-Length'] = Buffer.byteLength(postData);
    }

    const client = urlObj.protocol === 'https:' ? https : http;
    const req = client.request(options, (res) => {
      let body = '';
      res.on('data', (chunk) => body += chunk);
      res.on('end', () => {
        resolve({
          status: res.statusCode,
          headers: res.headers,
          body: body,
          url: url
        });
      });
    });

    req.on('error', (error) => {
      reject({ error: error.message, url: url });
    });

    if (data) {
      req.write(JSON.stringify(data));
    }
    req.end();
  });
}

async function runConnectivityTests() {
  console.log('🔍 Testing Better Auth Server Connectivity...\n');

  const baseUrl = 'https://nxtdotx.co.za';
  const tests = [
    { name: 'Health Check', url: `${baseUrl}/health` },
    { name: 'Version Check', url: `${baseUrl}/api/version` },
    { name: 'Auth Session', url: `${baseUrl}/api/auth/session` },
    { 
      name: 'Auth Sign-In', 
      url: `${baseUrl}/api/auth/sign-in`,
      method: 'POST',
      data: {
        email: '<EMAIL>',
        password: 'Admin123!@#'
      }
    }
  ];

  for (const test of tests) {
    try {
      console.log(`Testing: ${test.name}`);
      const result = await testEndpoint(
        test.url, 
        test.method || 'GET', 
        test.data || null
      );
      
      console.log(`✅ ${test.name}: ${result.status}`);
      if (result.status >= 400) {
        console.log(`   Response: ${result.body.substring(0, 200)}...`);
      }
      console.log('');
    } catch (error) {
      console.log(`❌ ${test.name}: ${error.error}`);
      console.log('');
    }
  }
}

runConnectivityTests().catch(console.error);
```

Run with: `node test-server-connectivity.js`

## 2. Database User Verification Script

Create this file as `verify-database-users.js`:

```javascript
#!/usr/bin/env node

const { Client } = require('pg');
require('dotenv').config();

async function verifyDatabaseUsers() {
  const client = new Client({
    connectionString: process.env.DATABASE_URL,
    ssl: process.env.NODE_ENV === 'production' ? { rejectUnauthorized: false } : false
  });

  try {
    await client.connect();
    console.log('✅ Connected to database');

    // Check if Better Auth tables exist
    const tablesQuery = `
      SELECT table_name 
      FROM information_schema.tables 
      WHERE table_schema = 'public' 
      AND table_name IN ('user', 'account', 'session', 'verification')
      ORDER BY table_name;
    `;
    
    const tablesResult = await client.query(tablesQuery);
    console.log('\n🔍 Better Auth Tables:');
    tablesResult.rows.forEach(row => {
      console.log(`   ✅ ${row.table_name}`);
    });

    if (tablesResult.rows.length < 4) {
      console.log('❌ Missing Better Auth tables!');
      return;
    }

    // Check test users
    const usersQuery = `
      SELECT u.id, u.email, u."emailVerified", u."createdAt",
             COUNT(a.id) as account_count,
             BOOL_OR(a.password IS NOT NULL) as has_password
      FROM "user" u
      LEFT JOIN "account" a ON u.id = a."userId"
      WHERE u.email IN ($1, $2, $3)
      GROUP BY u.id, u.email, u."emailVerified", u."createdAt"
      ORDER BY u.email;
    `;

    const testEmails = [
      '<EMAIL>',
      '<EMAIL>', 
      '<EMAIL>'
    ];

    const usersResult = await client.query(usersQuery, testEmails);
    
    console.log('\n🔍 Test Users Status:');
    if (usersResult.rows.length === 0) {
      console.log('❌ No test users found in database!');
      console.log('   Expected users:', testEmails.join(', '));
    } else {
      usersResult.rows.forEach(user => {
        console.log(`   📧 ${user.email}`);
        console.log(`      ID: ${user.id}`);
        console.log(`      Verified: ${user.emailVerified}`);
        console.log(`      Has Password: ${user.has_password}`);
        console.log(`      Accounts: ${user.account_count}`);
        console.log(`      Created: ${user.createdAt}`);
        console.log('');
      });
    }

    // Check account details for credential provider
    const accountsQuery = `
      SELECT u.email, a."providerId", a.password IS NOT NULL as has_password,
             LENGTH(a.password) as password_length, a."createdAt"
      FROM "account" a
      JOIN "user" u ON a."userId" = u.id
      WHERE u.email IN ($1, $2, $3)
      AND a."providerId" = 'credential'
      ORDER BY u.email;
    `;

    const accountsResult = await client.query(accountsQuery, testEmails);
    
    console.log('🔍 Password Accounts:');
    if (accountsResult.rows.length === 0) {
      console.log('❌ No password accounts found!');
    } else {
      accountsResult.rows.forEach(account => {
        console.log(`   📧 ${account.email}`);
        console.log(`      Provider: ${account.providerId}`);
        console.log(`      Has Password: ${account.has_password}`);
        console.log(`      Password Length: ${account.password_length || 'N/A'}`);
        console.log(`      Created: ${account.createdAt}`);
        console.log('');
      });
    }

  } catch (error) {
    console.error('❌ Database error:', error.message);
  } finally {
    await client.end();
  }
}

verifyDatabaseUsers().catch(console.error);
```

Run with: `node verify-database-users.js`

## 3. Environment Variables Check Script

Create this file as `check-env-vars.js`:

```javascript
#!/usr/bin/env node

require('dotenv').config();

function checkEnvVars() {
  console.log('🔍 Environment Variables Check\n');

  const requiredVars = [
    'DATABASE_URL',
    'BETTER_AUTH_SECRET',
    'BETTER_AUTH_URL',
    'VITE_BETTER_AUTH_URL'
  ];

  const optionalVars = [
    'NODE_ENV',
    'PORT',
    'VITE_SUPABASE_URL',
    'VITE_SUPABASE_ANON_KEY'
  ];

  console.log('Required Variables:');
  let missingRequired = [];
  
  requiredVars.forEach(varName => {
    const value = process.env[varName];
    if (value) {
      console.log(`✅ ${varName}: ${value.substring(0, 20)}...`);
    } else {
      console.log(`❌ ${varName}: NOT SET`);
      missingRequired.push(varName);
    }
  });

  console.log('\nOptional Variables:');
  optionalVars.forEach(varName => {
    const value = process.env[varName];
    if (value) {
      console.log(`✅ ${varName}: ${value}`);
    } else {
      console.log(`⚠️  ${varName}: NOT SET`);
    }
  });

  console.log('\nValidation:');
  
  // Check DATABASE_URL format
  const dbUrl = process.env.DATABASE_URL;
  if (dbUrl) {
    if (dbUrl.startsWith('postgresql://')) {
      console.log('✅ DATABASE_URL format looks correct');
    } else {
      console.log('❌ DATABASE_URL should start with postgresql://');
    }
  }

  // Check BETTER_AUTH_SECRET length
  const authSecret = process.env.BETTER_AUTH_SECRET;
  if (authSecret) {
    if (authSecret.length >= 32) {
      console.log('✅ BETTER_AUTH_SECRET length is sufficient');
    } else {
      console.log(`❌ BETTER_AUTH_SECRET too short (${authSecret.length} chars, need 32+)`);
    }
  }

  // Check URL consistency
  const authUrl = process.env.BETTER_AUTH_URL;
  const viteAuthUrl = process.env.VITE_BETTER_AUTH_URL;
  if (authUrl && viteAuthUrl) {
    if (viteAuthUrl.startsWith(authUrl)) {
      console.log('✅ Auth URLs are consistent');
    } else {
      console.log('❌ Auth URL mismatch:');
      console.log(`   BETTER_AUTH_URL: ${authUrl}`);
      console.log(`   VITE_BETTER_AUTH_URL: ${viteAuthUrl}`);
    }
  }

  if (missingRequired.length > 0) {
    console.log(`\n❌ Missing required variables: ${missingRequired.join(', ')}`);
    process.exit(1);
  } else {
    console.log('\n✅ All required environment variables are set');
  }
}

checkEnvVars();
```

Run with: `node check-env-vars.js`

## 4. Complete System Test Script

Create this file as `test-auth-system.js`:

```javascript
#!/usr/bin/env node

const https = require('https');
const { Client } = require('pg');
require('dotenv').config();

class AuthSystemTester {
  constructor() {
    this.baseUrl = process.env.VITE_BETTER_AUTH_URL?.replace('/api/auth', '') || 'https://nxtdotx.co.za';
    this.testCredentials = {
      email: '<EMAIL>',
      password: 'Admin123!@#'
    };
  }

  async httpRequest(url, method = 'GET', data = null, headers = {}) {
    return new Promise((resolve, reject) => {
      const urlObj = new URL(url);
      const options = {
        hostname: urlObj.hostname,
        port: urlObj.port || 443,
        path: urlObj.pathname + urlObj.search,
        method: method,
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
          ...headers
        }
      };

      if (data) {
        const postData = JSON.stringify(data);
        options.headers['Content-Length'] = Buffer.byteLength(postData);
      }

      const req = https.request(options, (res) => {
        let body = '';
        res.on('data', (chunk) => body += chunk);
        res.on('end', () => {
          resolve({
            status: res.statusCode,
            headers: res.headers,
            body: body
          });
        });
      });

      req.on('error', reject);
      if (data) req.write(JSON.stringify(data));
      req.end();
    });
  }

  async testServerConnectivity() {
    console.log('🔍 Testing Server Connectivity...');
    
    const tests = [
      { name: 'Health Check', path: '/health' },
      { name: 'API Version', path: '/api/version' },
      { name: 'Auth Session', path: '/api/auth/session' }
    ];

    for (const test of tests) {
      try {
        const response = await this.httpRequest(`${this.baseUrl}${test.path}`);
        console.log(`✅ ${test.name}: ${response.status}`);
      } catch (error) {
        console.log(`❌ ${test.name}: ${error.message}`);
      }
    }
  }

  async testDatabaseConnection() {
    console.log('\n🔍 Testing Database Connection...');
    
    const client = new Client({
      connectionString: process.env.DATABASE_URL,
      ssl: process.env.NODE_ENV === 'production' ? { rejectUnauthorized: false } : false
    });

    try {
      await client.connect();
      console.log('✅ Database connection successful');

      const result = await client.query('SELECT NOW() as current_time');
      console.log(`✅ Database query successful: ${result.rows[0].current_time}`);

      // Check for test user
      const userQuery = 'SELECT email FROM "user" WHERE email = $1';
      const userResult = await client.query(userQuery, [this.testCredentials.email]);
      
      if (userResult.rows.length > 0) {
        console.log(`✅ Test user exists: ${this.testCredentials.email}`);
      } else {
        console.log(`❌ Test user not found: ${this.testCredentials.email}`);
      }

    } catch (error) {
      console.log(`❌ Database error: ${error.message}`);
    } finally {
      await client.end();
    }
  }

  async testAuthentication() {
    console.log('\n🔍 Testing Authentication Flow...');

    try {
      const response = await this.httpRequest(
        `${this.baseUrl}/api/auth/sign-in`,
        'POST',
        this.testCredentials
      );

      console.log(`Response Status: ${response.status}`);
      console.log(`Response Headers:`, Object.keys(response.headers));
      
      if (response.status === 200) {
        console.log('✅ Authentication request successful');
        try {
          const data = JSON.parse(response.body);
          console.log('✅ Valid JSON response received');
          if (data.user) {
            console.log(`✅ User authenticated: ${data.user.email}`);
          }
        } catch (e) {
          console.log('⚠️  Non-JSON response:', response.body.substring(0, 100));
        }
      } else {
        console.log(`❌ Authentication failed: ${response.status}`);
        console.log(`Response: ${response.body.substring(0, 200)}`);
      }

    } catch (error) {
      console.log(`❌ Authentication error: ${error.message}`);
    }
  }

  async runAllTests() {
    console.log('🚀 Starting Complete Auth System Test\n');
    
    await this.testServerConnectivity();
    await this.testDatabaseConnection();
    await this.testAuthentication();
    
    console.log('\n✅ Test completed');
  }
}

const tester = new AuthSystemTester();
tester.runAllTests().catch(console.error);
```

Run with: `node test-auth-system.js`

## Usage Instructions

1. **Install dependencies** (if not already installed):
   ```bash
   npm install pg dotenv
   ```

2. **Make sure your `.env` file is properly configured** with all required variables

3. **Run the scripts in order**:
   ```bash
   # Check environment variables first
   node check-env-vars.js
   
   # Test server connectivity
   node test-server-connectivity.js
   
   # Verify database users
   node verify-database-users.js
   
   # Run complete system test
   node test-auth-system.js
   ```

4. **Analyze the output** to identify which specific component is failing

5. **Use the results** to focus your debugging efforts on the specific failing component

These scripts will help you quickly identify whether the issue is:
- Server connectivity/deployment
- Environment configuration
- Database connectivity/user data
- Authentication endpoint functionality
- Network/CORS issues

