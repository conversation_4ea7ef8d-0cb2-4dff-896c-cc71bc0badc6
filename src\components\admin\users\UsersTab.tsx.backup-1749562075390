
import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { 
  Table, 
  TableBody, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from '@/components/ui/table';
import { Badge } from '@/components/ui/badge';
import { Search, UserPlus } from 'lucide-react';
import { toast } from 'sonner';
import { useAuth } from '@/auth';
import { Permission } from '@/utils/rbac/permissions'; // Import Permission enum
import AddUserDialog from './AddUserDialog';
import { useUserManagement } from '@/context/UserManagementContext';

const UsersTab: React.FC = () => {
  const { hasPermission } = useAuth();
  const { users, roles: rolesData, addUser } = useUserManagement();
  const [searchTerm, setSearchTerm] = useState('');
  const [dialogOpen, setDialogOpen] = useState(false);

  // Filter users based on search term
  const filteredUsers = users.filter(user => 
    user.username.toLowerCase().includes(searchTerm.toLowerCase()) ||
    user.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
    (user.roles && user.roles.length > 0 ? user.roles.map(r => r.name).join(' ') : '').toLowerCase().includes(searchTerm.toLowerCase())
  );

  const handleAddUser = (newUser: any) => {
    addUser(newUser);
    toast.success(`User ${newUser.username} created successfully`);
    setDialogOpen(false);
  };

  return (
    <Card>
      <CardHeader className="pb-2">
        <div className="flex justify-between items-center">
          <CardTitle>All Users</CardTitle>
          <div className="relative w-64">
            <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="Search users..."
              className="pl-8"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
          </div>
        </div>
        <CardDescription>
          Manage user accounts and access permissions.
        </CardDescription>
      </CardHeader>
      <CardContent>
        {hasPermission(Permission.USER_MANAGEMENT_CREATE) && (
          <div className="mb-4 flex justify-end">
            <AddUserDialog 
              open={dialogOpen} 
              onOpenChange={setDialogOpen} 
              onAddUser={handleAddUser} 
              rolesData={rolesData}
            />
          </div>
        )}
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Username</TableHead>
              <TableHead>Email</TableHead>
              <TableHead>Role</TableHead>
              <TableHead>Status</TableHead>
              <TableHead>Created</TableHead>
              <TableHead className="text-right">Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {filteredUsers.map((user) => (
              <TableRow key={user.id}>
                <TableCell className="font-medium">{user.username}</TableCell>
                <TableCell>{user.email}</TableCell>
                <TableCell>
                  <Badge variant={user.roles && user.roles[0]?.name === 'admin' ? 'destructive' : user.roles && user.roles[0]?.name === 'manager' ? 'default' : 'secondary'}>
                    {user.roles && user.roles.length > 0 ? user.roles[0].name : 'N/A'}
                  </Badge>
                </TableCell>
                <TableCell>
                  <Badge variant={user.is_active ? 'default' : 'outline'}>
                    {user.is_active ? 'active' : 'inactive'}
                  </Badge>
                </TableCell>
                <TableCell>{new Date(user.created_at).toLocaleDateString()}</TableCell>
                <TableCell className="text-right">
                  <Button size="sm" variant="outline" onClick={() => toast.info("Edit feature coming soon")}>
                    Edit
                  </Button>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </CardContent>
    </Card>
  );
};

export default UsersTab;
