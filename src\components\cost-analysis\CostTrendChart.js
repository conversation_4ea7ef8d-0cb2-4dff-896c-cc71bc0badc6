import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON>hart, Line, XAxis, YAxis, CartesianGrid, <PERSON>lt<PERSON>, Legend, ResponsiveContainer } from 'recharts';
export const CostTrendChart = ({ data, title, description, className }) => {
    return (_jsxs(Card, { className: `backdrop-blur-md bg-white/30 border border-white/10 ${className}`, children: [_jsxs(CardHeader, { children: [_jsx(CardTitle, { children: title }), _jsx(CardDescription, { children: description })] }), _jsx(CardContent, { className: "h-80", children: _jsx(ResponsiveContainer, { width: "100%", height: "100%", children: _jsxs(LineChart, { data: data, children: [_jsx(<PERSON><PERSON><PERSON><PERSON><PERSON>, { strokeDasharray: "3 3" }), _jsx(XAxis, { dataKey: "month" }), _jsx(YAxis, { tickFormatter: (value) => `R${(value / 1000).toFixed(0)}k` }), _jsx(Tooltip, { formatter: (value) => [`R${value.toLocaleString()}`, 'Amount'], labelFormatter: (label) => `Month: ${label}` }), _jsx(Legend, {}), _jsx(Line, { type: "monotone", dataKey: "cost", stroke: "#0EA5E9", strokeWidth: 2, name: "Current Period", activeDot: { r: 8 } }), _jsx(Line, { type: "monotone", dataKey: "previous", stroke: "#94A3B8", strokeDasharray: "5 5", name: "Previous Period" })] }) }) })] }));
};
