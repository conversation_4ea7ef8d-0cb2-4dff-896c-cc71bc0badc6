import React from 'react';
import { <PERSON>, Card<PERSON>ontent, Card<PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, AreaChart, Area } from 'recharts';
import { Eye, MousePointer, Clock, TrendingUp, TrendingDown } from 'lucide-react';

interface BehaviorData {
  timestamp: string;
  pageViews: number;
  uniqueVisitors: number;
  bounceRate: number;
  avgSessionDuration: number;
  conversionRate: number;
}

interface CustomerBehaviorTrackerProps {
  data?: BehaviorData[];
  timeRange?: string;
}

const CustomerBehaviorTracker: React.FC<CustomerBehaviorTrackerProps> = ({ 
  data = [],
  timeRange = 'last-7-days'
}) => {
  // Default sample data if none provided
  const defaultData: BehaviorData[] = [
    { timestamp: '00:00', pageViews: 120, uniqueVisitors: 85, bounceRate: 45, avgSessionDuration: 180, conversionRate: 2.1 },
    { timestamp: '04:00', pageViews: 80, uniqueVisitors: 60, bounceRate: 52, avgSessionDuration: 165, conversionRate: 1.8 },
    { timestamp: '08:00', pageViews: 250, uniqueVisitors: 180, bounceRate: 38, avgSessionDuration: 220, conversionRate: 3.2 },
    { timestamp: '12:00', pageViews: 320, uniqueVisitors: 240, bounceRate: 35, avgSessionDuration: 245, conversionRate: 3.8 },
    { timestamp: '16:00', pageViews: 380, uniqueVisitors: 280, bounceRate: 32, avgSessionDuration: 260, conversionRate: 4.1 },
    { timestamp: '20:00', pageViews: 290, uniqueVisitors: 210, bounceRate: 40, avgSessionDuration: 200, conversionRate: 3.5 },
  ];

  const behaviorData = data.length > 0 ? data : defaultData;

  const behaviorMetrics = [
    {
      title: 'Page Views',
      value: '2,450',
      change: '+12.5%',
      trend: 'up',
      icon: Eye,
      color: 'text-blue-600',
      bgColor: 'bg-blue-100'
    },
    {
      title: 'Unique Visitors',
      value: '1,820',
      change: '+8.3%',
      trend: 'up',
      icon: MousePointer,
      color: 'text-green-600',
      bgColor: 'bg-green-100'
    },
    {
      title: 'Avg Session Duration',
      value: '3m 42s',
      change: '+15s',
      trend: 'up',
      icon: Clock,
      color: 'text-purple-600',
      bgColor: 'bg-purple-100'
    },
    {
      title: 'Bounce Rate',
      value: '38.2%',
      change: '-2.1%',
      trend: 'down',
      icon: TrendingDown,
      color: 'text-orange-600',
      bgColor: 'bg-orange-100'
    }
  ];

  return (
    <div className="space-y-6">
      {/* Behavior Metrics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        {behaviorMetrics.map((metric, index) => (
          <Card key={index}>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-xs font-medium text-gray-600">{metric.title}</p>
                  <p className="text-lg font-bold text-gray-900">{metric.value}</p>
                  <div className="flex items-center mt-1">
                    {metric.trend === 'up' ? (
                      <TrendingUp className="h-3 w-3 text-green-500 mr-1" />
                    ) : (
                      <TrendingDown className="h-3 w-3 text-red-500 mr-1" />
                    )}
                    <span className={`text-xs ${metric.trend === 'up' ? 'text-green-600' : 'text-red-600'}`}>
                      {metric.change}
                    </span>
                  </div>
                </div>
                <div className={`p-2 rounded-full ${metric.bgColor}`}>
                  <metric.icon className={`h-4 w-4 ${metric.color}`} />
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Behavior Charts */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle>Page Views & Visitors</CardTitle>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={300}>
              <AreaChart data={behaviorData}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="timestamp" />
                <YAxis />
                <Tooltip />
                <Area type="monotone" dataKey="pageViews" stackId="1" stroke="#3b82f6" fill="#3b82f6" fillOpacity={0.6} />
                <Area type="monotone" dataKey="uniqueVisitors" stackId="2" stroke="#10b981" fill="#10b981" fillOpacity={0.6} />
              </AreaChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Conversion Rate Trends</CardTitle>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={300}>
              <LineChart data={behaviorData}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="timestamp" />
                <YAxis />
                <Tooltip formatter={(value) => [`${value}%`, 'Conversion Rate']} />
                <Line type="monotone" dataKey="conversionRate" stroke="#8b5cf6" strokeWidth={3} />
              </LineChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>
      </div>

      {/* Behavior Insights */}
      <Card>
        <CardHeader>
          <CardTitle>Behavior Insights</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="p-4 bg-blue-50 rounded-lg">
              <h4 className="font-semibold text-blue-900 mb-2">Peak Activity</h4>
              <p className="text-sm text-blue-700">
                Highest engagement occurs between 4-6 PM with 380 page views and 4.1% conversion rate.
              </p>
              <Badge className="mt-2 bg-blue-100 text-blue-800">Optimize for peak hours</Badge>
            </div>
            
            <div className="p-4 bg-green-50 rounded-lg">
              <h4 className="font-semibold text-green-900 mb-2">Low Bounce Rate</h4>
              <p className="text-sm text-green-700">
                Bounce rate decreased by 2.1% indicating improved content relevance and user experience.
              </p>
              <Badge className="mt-2 bg-green-100 text-green-800">Positive trend</Badge>
            </div>
            
            <div className="p-4 bg-purple-50 rounded-lg">
              <h4 className="font-semibold text-purple-900 mb-2">Session Quality</h4>
              <p className="text-sm text-purple-700">
                Average session duration increased by 15 seconds, showing better user engagement.
              </p>
              <Badge className="mt-2 bg-purple-100 text-purple-800">Quality improvement</Badge>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default CustomerBehaviorTracker;
