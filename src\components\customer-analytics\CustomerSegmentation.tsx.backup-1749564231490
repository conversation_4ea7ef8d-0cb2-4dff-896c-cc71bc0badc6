import React from 'react';
import { <PERSON>, Card<PERSON>ontent, Card<PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { <PERSON><PERSON>hart, Pie, Cell, BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> } from 'recharts';
import { Users, DollarSign, TrendingUp, Star, Target, Crown } from 'lucide-react';

interface SegmentData {
  name: string;
  customers: number;
  revenue: number;
  avgOrderValue: number;
  frequency: number;
  satisfaction: number;
  color: string;
}

interface CustomerSegmentationProps {
  segments?: SegmentData[];
}

const CustomerSegmentation: React.FC<CustomerSegmentationProps> = ({ segments = [] }) => {
  // Default segment data if none provided
  const defaultSegments: SegmentData[] = [
    {
      name: 'VIP Champions',
      customers: 78,
      revenue: 327600,
      avgOrderValue: 680,
      frequency: 8.2,
      satisfaction: 4.9,
      color: '#8b5cf6'
    },
    {
      name: 'Loyal Customers',
      customers: 156,
      revenue: 444600,
      avgOrderValue: 425,
      frequency: 6.7,
      satisfaction: 4.6,
      color: '#3b82f6'
    },
    {
      name: 'Potential Loyalists',
      customers: 342,
      revenue: 974700,
      avgOrderValue: 285,
      frequency: 4.2,
      satisfaction: 4.2,
      color: '#10b981'
    },
    {
      name: 'New Customers',
      customers: 189,
      revenue: 274050,
      avgOrderValue: 145,
      frequency: 1.8,
      satisfaction: 4.0,
      color: '#f59e0b'
    },
    {
      name: 'At Risk',
      customers: 98,
      revenue: 127400,
      avgOrderValue: 130,
      frequency: 1.2,
      satisfaction: 3.5,
      color: '#ef4444'
    },
    {
      name: 'Cannot Lose Them',
      customers: 45,
      revenue: 180000,
      avgOrderValue: 500,
      frequency: 2.1,
      satisfaction: 3.8,
      color: '#f97316'
    }
  ];

  const segmentData = segments.length > 0 ? segments : defaultSegments;

  // Calculate totals
  const totalCustomers = segmentData.reduce((sum, segment) => sum + segment.customers, 0);
  const totalRevenue = segmentData.reduce((sum, segment) => sum + segment.revenue, 0);

  // Prepare data for charts
  const pieData = segmentData.map(segment => ({
    name: segment.name,
    value: segment.customers,
    percentage: ((segment.customers / totalCustomers) * 100).toFixed(1),
    color: segment.color
  }));

  const revenueData = segmentData.map(segment => ({
    name: segment.name,
    revenue: segment.revenue,
    customers: segment.customers,
    color: segment.color
  }));

  const rfmData = segmentData.map(segment => ({
    name: segment.name,
    frequency: segment.frequency,
    avgOrderValue: segment.avgOrderValue,
    customers: segment.customers,
    color: segment.color
  }));

  const getSegmentIcon = (segmentName: string) => {
    switch (segmentName) {
      case 'VIP Champions':
        return Crown;
      case 'Loyal Customers':
        return Star;
      case 'Potential Loyalists':
        return Target;
      case 'At Risk':
        return TrendingUp;
      default:
        return Users;
    }
  };

  const getSegmentDescription = (segmentName: string) => {
    switch (segmentName) {
      case 'VIP Champions':
        return 'High-value customers with frequent purchases and excellent satisfaction';
      case 'Loyal Customers':
        return 'Regular customers with consistent purchase behavior';
      case 'Potential Loyalists':
        return 'Recent customers with potential for increased loyalty';
      case 'New Customers':
        return 'Recently acquired customers requiring nurturing';
      case 'At Risk':
        return 'Customers showing declining engagement patterns';
      case 'Cannot Lose Them':
        return 'High-value customers with decreasing activity';
      default:
        return 'Customer segment requiring analysis';
    }
  };

  const getSegmentStrategy = (segmentName: string) => {
    switch (segmentName) {
      case 'VIP Champions':
        return 'Reward loyalty, exclusive offers, premium support';
      case 'Loyal Customers':
        return 'Upsell opportunities, referral programs';
      case 'Potential Loyalists':
        return 'Engagement campaigns, loyalty program enrollment';
      case 'New Customers':
        return 'Onboarding optimization, education content';
      case 'At Risk':
        return 'Win-back campaigns, satisfaction surveys';
      case 'Cannot Lose Them':
        return 'Immediate intervention, personalized offers';
      default:
        return 'Develop targeted strategy';
    }
  };

  return (
    <div className="space-y-6">
      {/* Segment Overview Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {segmentData.slice(0, 3).map((segment, index) => {
          const IconComponent = getSegmentIcon(segment.name);
          return (
            <Card key={index}>
              <CardContent className="p-4">
                <div className="flex items-center justify-between mb-3">
                  <div className="flex items-center">
                    <div 
                      className="p-2 rounded-full mr-3"
                      style={{ backgroundColor: `${segment.color}20` }}
                    >
                      <IconComponent className="h-4 w-4" style={{ color: segment.color }} />
                    </div>
                    <div>
                      <h3 className="font-semibold text-sm">{segment.name}</h3>
                      <p className="text-xs text-gray-600">{segment.customers} customers</p>
                    </div>
                  </div>
                  <Badge style={{ backgroundColor: segment.color, color: 'white' }}>
                    {((segment.customers / totalCustomers) * 100).toFixed(1)}%
                  </Badge>
                </div>
                <div className="space-y-2">
                  <div className="flex justify-between text-xs">
                    <span className="text-gray-600">Revenue:</span>
                    <span className="font-semibold">${(segment.revenue / 1000).toFixed(0)}K</span>
                  </div>
                  <div className="flex justify-between text-xs">
                    <span className="text-gray-600">AOV:</span>
                    <span className="font-semibold">${segment.avgOrderValue}</span>
                  </div>
                  <div className="flex justify-between text-xs">
                    <span className="text-gray-600">Satisfaction:</span>
                    <span className="font-semibold">{segment.satisfaction}/5</span>
                  </div>
                </div>
              </CardContent>
            </Card>
          );
        })}
      </div>

      {/* Charts */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle>Customer Distribution by Segment</CardTitle>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={300}>
              <PieChart>
                <Pie
                  data={pieData}
                  cx="50%"
                  cy="50%"
                  outerRadius={80}
                  dataKey="value"
                  label={({ name, percentage }) => `${name}: ${percentage}%`}
                >
                  {pieData.map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={entry.color} />
                  ))}
                </Pie>
                <Tooltip />
              </PieChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Revenue by Segment</CardTitle>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={300}>
              <BarChart data={revenueData}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="name" angle={-45} textAnchor="end" height={80} />
                <YAxis />
                <Tooltip formatter={(value) => [`$${(value as number / 1000).toFixed(0)}K`, 'Revenue']} />
                <Bar dataKey="revenue" fill="#3b82f6" />
              </BarChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>
      </div>

      {/* RFM Analysis */}
      <Card>
        <CardHeader>
          <CardTitle>RFM Analysis: Frequency vs Average Order Value</CardTitle>
          <p className="text-sm text-gray-600">Bubble size represents customer count</p>
        </CardHeader>
        <CardContent>
          <ResponsiveContainer width="100%" height={400}>
            <ScatterChart data={rfmData}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="frequency" name="Purchase Frequency" />
              <YAxis dataKey="avgOrderValue" name="Average Order Value" />
              <Tooltip 
                cursor={{ strokeDasharray: '3 3' }}
                formatter={(value, name) => [
                  name === 'avgOrderValue' ? `$${value}` : value,
                  name === 'avgOrderValue' ? 'AOV' : 'Frequency'
                ]}
              />
              {rfmData.map((segment, index) => (
                <Scatter
                  key={index}
                  dataKey="customers"
                  fill={segment.color}
                  name={segment.name}
                />
              ))}
            </ScatterChart>
          </ResponsiveContainer>
        </CardContent>
      </Card>

      {/* Detailed Segment Analysis */}
      <Card>
        <CardHeader>
          <CardTitle>Segment Analysis & Strategies</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {segmentData.map((segment, index) => {
              const IconComponent = getSegmentIcon(segment.name);
              return (
                <div key={index} className="border rounded-lg p-4">
                  <div className="flex items-start justify-between mb-3">
                    <div className="flex items-center">
                      <div 
                        className="p-2 rounded-full mr-3"
                        style={{ backgroundColor: `${segment.color}20` }}
                      >
                        <IconComponent className="h-5 w-5" style={{ color: segment.color }} />
                      </div>
                      <div>
                        <h3 className="font-semibold text-lg">{segment.name}</h3>
                        <p className="text-sm text-gray-600">{getSegmentDescription(segment.name)}</p>
                      </div>
                    </div>
                    <Badge style={{ backgroundColor: segment.color, color: 'white' }}>
                      {segment.customers} customers
                    </Badge>
                  </div>
                  
                  <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-3">
                    <div className="text-center">
                      <div className="text-lg font-bold" style={{ color: segment.color }}>
                        ${(segment.revenue / 1000).toFixed(0)}K
                      </div>
                      <div className="text-xs text-gray-600">Total Revenue</div>
                    </div>
                    <div className="text-center">
                      <div className="text-lg font-bold" style={{ color: segment.color }}>
                        ${segment.avgOrderValue}
                      </div>
                      <div className="text-xs text-gray-600">Avg Order Value</div>
                    </div>
                    <div className="text-center">
                      <div className="text-lg font-bold" style={{ color: segment.color }}>
                        {segment.frequency}x
                      </div>
                      <div className="text-xs text-gray-600">Purchase Frequency</div>
                    </div>
                    <div className="text-center">
                      <div className="text-lg font-bold" style={{ color: segment.color }}>
                        {segment.satisfaction}/5
                      </div>
                      <div className="text-xs text-gray-600">Satisfaction</div>
                    </div>
                  </div>
                  
                  <div className="bg-gray-50 rounded p-3">
                    <h4 className="font-medium text-sm mb-1">Recommended Strategy:</h4>
                    <p className="text-sm text-gray-700">{getSegmentStrategy(segment.name)}</p>
                  </div>
                </div>
              );
            })}
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default CustomerSegmentation;
