#!/usr/bin/env node
const { Server } = require('@modelcontextprotocol/sdk/server/index.js');
const { StdioServerTransport } = require('@modelcontextprotocol/sdk/server/stdio.js');
const { CallToolRequestSchema, ListToolsRequestSchema } = require('@modelcontextprotocol/sdk/types.js');
const express = require('express');
const fs = require('fs').promises;
const path = require('path');

class TaskmasterServer {
  constructor() {
    this.server = new Server({ name: 'taskmaster-server', version: '1.0.0' }, { capabilities: { tools: {} } });
    this.port = process.env.MCP_PORT || 8086;
    this.tasksFile = '/data/tasks.json';
    this.setupToolHandlers();
    this.setupHealthCheck();
  }

  async loadTasks() {
    try {
      const data = await fs.readFile(this.tasksFile, 'utf8');
      return JSON.parse(data);
    } catch {
      return [];
    }
  }

  async saveTasks(tasks) {
    await fs.writeFile(this.tasksFile, JSON.stringify(tasks, null, 2));
  }

  setupToolHandlers() {
    this.server.setRequestHandler(ListToolsRequestSchema, async () => ({
      tools: [
        {
          name: 'create_task',
          description: 'Create a new task',
          inputSchema: {
            type: 'object',
            properties: {
              title: { type: 'string', description: 'Task title' },
              description: { type: 'string', description: 'Task description' },
              priority: { type: 'string', enum: ['low', 'medium', 'high'], default: 'medium' },
              due_date: { type: 'string', description: 'Due date (ISO format)' }
            },
            required: ['title']
          }
        },
        {
          name: 'list_tasks',
          description: 'List all tasks',
          inputSchema: {
            type: 'object',
            properties: {
              status: { type: 'string', enum: ['pending', 'completed', 'all'], default: 'all' }
            }
          }
        },
        {
          name: 'complete_task',
          description: 'Mark a task as completed',
          inputSchema: {
            type: 'object',
            properties: { id: { type: 'string', description: 'Task ID' } },
            required: ['id']
          }
        }
      ]
    }));

    this.server.setRequestHandler(CallToolRequestSchema, async (request) => {
      const { name, arguments: args } = request.params;
      
      if (name === 'create_task') {
        const tasks = await this.loadTasks();
        const newTask = {
          id: Date.now().toString(),
          title: args.title,
          description: args.description || '',
          priority: args.priority || 'medium',
          due_date: args.due_date || null,
          status: 'pending',
          created_at: new Date().toISOString()
        };
        tasks.push(newTask);
        await this.saveTasks(tasks);
        return { content: [{ type: 'text', text: JSON.stringify({ success: true, task: newTask }, null, 2) }] };
      }
      
      if (name === 'list_tasks') {
        const tasks = await this.loadTasks();
        const filtered = args.status === 'all' ? tasks : tasks.filter(t => t.status === args.status);
        return { content: [{ type: 'text', text: JSON.stringify({ tasks: filtered }, null, 2) }] };
      }
      
      if (name === 'complete_task') {
        const tasks = await this.loadTasks();
        const task = tasks.find(t => t.id === args.id);
        if (task) {
          task.status = 'completed';
          task.completed_at = new Date().toISOString();
          await this.saveTasks(tasks);
          return { content: [{ type: 'text', text: JSON.stringify({ success: true, task }, null, 2) }] };
        }
        throw new Error('Task not found');
      }
      
      throw new Error(`Unknown tool: ${name}`);
    });
  }

  setupHealthCheck() {
    const app = express();
    app.get('/health', (req, res) => {
      res.json({ status: 'healthy', service: 'taskmaster-mcp', timestamp: new Date().toISOString() });
    });
    app.listen(this.port, () => console.log(`Taskmaster MCP Server health check running on port ${this.port}`));
  }

  async run() {
    const transport = new StdioServerTransport();
    await this.server.connect(transport);
    console.error('Taskmaster MCP server running on stdio');
  }
}

const server = new TaskmasterServer();
server.run().catch(console.error);
