import { useAuth } from '@/auth';
import { Permission, UserRole } from '@/auth';

// Hook for checking permissions in components
export const usePermissionCheck = () => {
  const { user, hasPermission: contextHasPermission } = useAuth(); // Renamed to avoid conflict

  const hasAnyPermission = (permissions: Permission[]): boolean => {
    if (!user) return false;
    return permissions.some(permission => contextHasPermission(permission));
  };

  const hasAllPermissions = (permissions: Permission[]): boolean => {
    if (!user) return false;
    return permissions.every(permission => contextHasPermission(permission));
  };

  const isRole = (role: UserRole): boolean => {
    return user?.role === role;
  };

  // Define role hierarchy for isRoleOrHigher
  const roleHierarchy: Record<UserRole, number> = {
    [UserRole.GUEST]: 1,
    [UserRole.USER]: 2,
    [UserRole.MANAGER]: 3,
    [UserRole.ADMIN]: 4,
    [UserRole.SUPER_ADMIN]: 5
  };

  const isRoleOrHigher = (role: UserRole): boolean => {
    if (!user?.role) return false;
    const userRoleLevel = roleHierarchy[user.role as UserRole] || 0;
    const requiredRoleLevel = roleHierarchy[role] || 0;
    return userRoleLevel >= requiredRoleLevel;
  };

  return {
    user, // Expose user if needed by consumers of this hook
    hasPermission: contextHasPermission, // Expose the original hasPermission from context
    hasAnyPermission,
    hasAllPermissions,
    isRole,
    isRoleOrHigher,
    checkAccess: (permissions: Permission[], requireAll = false) => {
      return requireAll ? hasAllPermissions(permissions) : hasAnyPermission(permissions);
    }
  };
};
