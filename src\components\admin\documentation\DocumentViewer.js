import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
import { useState } from 'react';
import ReactMarkdown from 'react-markdown';
import { Copy, Share2, X } from 'lucide-react';
import { documentService } from './documentService';
import { Button } from '@/components/ui/button';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger, } from '@/components/ui/tooltip';
import { toast } from 'sonner';
export const DocumentViewer = ({ document }) => {
    const [shareUrl, setShareUrl] = useState(document.shareId ?
        `${globalThis.location.origin}/shared-document/${document.shareId}` : null);
    const [isGeneratingLink, setIsGeneratingLink] = useState(false);
    // Function to create a shareable link
    const handleCreateShareableLink = async () => {
        try {
            setIsGeneratingLink(true);
            const url = await documentService.createShareableLink(document.id);
            if (url) {
                setShareUrl(url);
                toast.success("Shareable link created");
            }
            else {
                toast.error("Failed to create shareable link");
            }
        }
        catch (error) {
            console.error('Error creating shareable link:', error);
            toast.error("Failed to create shareable link");
        }
        finally {
            setIsGeneratingLink(false);
        }
    };
    // Function to remove the shareable link
    const handleRemoveShareableLink = async () => {
        try {
            const success = await documentService.removeShareableLink(document.id);
            if (success) {
                setShareUrl(null);
                toast.success("Shareable link removed");
            }
            else {
                toast.error("Failed to remove shareable link");
            }
        }
        catch (error) {
            console.error('Error removing shareable link:', error);
            toast.error("Failed to remove shareable link");
        }
    };
    // Function to copy the shareable link to clipboard
    const handleCopyLink = () => {
        if (shareUrl) {
            navigator.clipboard.writeText(shareUrl);
            toast.success("Link copied to clipboard");
        }
    };
    // Render different content based on document type
    const renderDocumentContent = () => {
        switch (document.type) {
            case 'markdown':
                return (_jsx("div", { className: "prose dark:prose-invert max-w-none", children: _jsx(ReactMarkdown, { children: document.content || '' }) }));
            case 'text':
                return (_jsx("div", { className: "whitespace-pre-wrap font-mono bg-gray-50 dark:bg-gray-900 p-4 rounded", children: document.content }));
            case 'pdf':
                return (_jsxs("div", { className: "flex flex-col items-center", children: [_jsx("div", { className: "text-center mb-4", children: _jsx("a", { href: document.url, target: "_blank", rel: "noreferrer", className: "text-blue-600 hover:underline", children: "Open PDF in new tab" }) }), document.url && (_jsx("iframe", { src: document.url, className: "w-full h-[70vh] border-0 rounded", title: document.title }))] }));
            case 'image':
                return (_jsx("div", { className: "flex justify-center", children: _jsx("img", { src: document.url, alt: document.title, className: "max-w-full max-h-[70vh] object-contain" }) }));
            default:
                return (_jsxs("div", { className: "text-center text-gray-500", children: [_jsx("p", { children: "Preview not available for this document type." }), document.url && (_jsx("a", { href: document.url, target: "_blank", rel: "noreferrer", className: "text-blue-600 hover:underline block mt-2", children: "Open document" }))] }));
        }
    };
    return (_jsxs("div", { className: "h-full flex flex-col", children: [_jsxs("div", { className: "border-b border-gray-200 dark:border-gray-700 p-4 flex justify-between items-center", children: [_jsxs("div", { children: [_jsx("h2", { className: "text-2xl font-bold", children: document.title }), _jsxs("p", { className: "text-sm text-gray-500", children: [document.author && `By ${document.author} • `, "Last updated ", new Date(document.updated_at || document.updatedAt || '').toLocaleDateString()] })] }), _jsx("div", { className: "flex space-x-2", children: shareUrl ? (_jsxs("div", { className: "flex items-center space-x-2 bg-gray-100 dark:bg-gray-800 rounded-lg p-2", children: [_jsx("input", { type: "text", value: shareUrl, readOnly: true, className: "bg-transparent text-sm w-56 border-none focus:ring-0" }), _jsx(TooltipProvider, { children: _jsxs(Tooltip, { children: [_jsx(TooltipTrigger, { asChild: true, children: _jsx(Button, { variant: "ghost", size: "icon", onClick: handleCopyLink, children: _jsx(Copy, { size: 16 }) }) }), _jsx(TooltipContent, { children: _jsx("p", { children: "Copy link" }) })] }) }), _jsx(TooltipProvider, { children: _jsxs(Tooltip, { children: [_jsx(TooltipTrigger, { asChild: true, children: _jsx(Button, { variant: "ghost", size: "icon", onClick: handleRemoveShareableLink, children: _jsx(X, { size: 16 }) }) }), _jsx(TooltipContent, { children: _jsx("p", { children: "Remove sharing" }) })] }) })] })) : (_jsxs(Button, { variant: "outline", size: "sm", onClick: handleCreateShareableLink, disabled: isGeneratingLink, className: "flex items-center space-x-1", children: [_jsx(Share2, { size: 16, className: "mr-1" }), isGeneratingLink ? "Generating..." : "Share"] })) })] }), document.description && (_jsx("div", { className: "p-4 bg-gray-50 dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700", children: _jsx("p", { className: "text-sm text-gray-600 dark:text-gray-300", children: document.description }) })), _jsx("div", { className: "flex-grow p-6 overflow-auto", children: renderDocumentContent() })] }));
};
