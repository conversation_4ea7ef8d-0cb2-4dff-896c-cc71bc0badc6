import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";
import { Checkbox } from "@/components/ui/checkbox";
import { 
  Users, 
  Plus, 
  Edit, 
  Trash2, 
  Search,
  Filter,
  Download,
  Upload,
  Mail,
  Phone,
  Tag,
  ArrowLeft,
  Save,
  UserPlus,
  Settings
} from "lucide-react";
import { Contact, ContactSegment, CustomerCommunicationService } from "@/services/customer-communication";

interface ContactManagerProps {
  onBack?: () => void;
  onCreateSegment?: () => void;
}

const ContactManager: React.FC<ContactManagerProps> = ({
  onBack,
  onCreateSegment
}) => {
  const [activeTab, setActiveTab] = useState<'contacts' | 'segments'>('contacts');
  const [contacts, setContacts] = useState<Contact[]>([]);
  const [segments, setSegments] = useState<ContactSegment[]>([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedContacts, setSelectedContacts] = useState<string[]>([]);
  const [filterSegment, setFilterSegment] = useState<string>('all');
  const [isCreating, setIsCreating] = useState(false);
  const [editingContact, setEditingContact] = useState<Contact | null>(null);
  const [loading, setLoading] = useState(false);

  // Form state for creating/editing contacts
  const [contactForm, setContactForm] = useState({
    name: '',
    email: '',
    phone: '',
    tags: [] as string[],
    segments: [] as string[],
    preferences: {
      email: true,
      sms: true,
      marketing: true,
      notifications: true
    }
  });

  useEffect(() => {
    loadContactsAndSegments();
  }, []);

  const loadContactsAndSegments = async () => {
    try {
      setLoading(true);
      
      // Mock data - replace with actual API calls
      const mockContacts: Contact[] = [
        {
          id: 'contact_1',
          name: 'John Doe',
          email: '<EMAIL>',
          phone: '******-0123',
          customerId: 'customer_1',
          tags: ['vip', 'enterprise'],
          segments: ['vip-customers', 'active-customers'],
          preferences: {
            email: true,
            sms: true,
            marketing: true,
            notifications: true
          },
          createdAt: '2025-05-20T10:00:00Z',
          updatedAt: '2025-05-25T14:30:00Z'
        },
        {
          id: 'contact_2',
          name: 'Sarah Johnson',
          email: '<EMAIL>',
          phone: '******-0124',
          customerId: 'customer_2',
          tags: ['new-customer'],
          segments: ['new-customers'],
          preferences: {
            email: true,
            sms: false,
            marketing: true,
            notifications: true
          },
          createdAt: '2025-05-25T09:15:00Z',
          updatedAt: '2025-05-25T09:15:00Z'
        },
        {
          id: 'contact_3',
          name: 'Michael Chen',
          email: '<EMAIL>',
          phone: '******-0125',
          customerId: 'customer_3',
          tags: ['premium', 'loyal'],
          segments: ['vip-customers', 'active-customers'],
          preferences: {
            email: true,
            sms: true,
            marketing: false,
            notifications: true
          },
          createdAt: '2025-04-15T16:20:00Z',
          updatedAt: '2025-05-24T11:45:00Z'
        }
      ];

      const mockSegments: ContactSegment[] = [
        {
          id: 'new-customers',
          name: 'New Customers',
          description: 'Customers who joined in the last 30 days',
          criteria: [
            { field: 'createdAt', operator: 'greater_than', value: '2025-04-26' }
          ],
          contactCount: 245,
          createdAt: '2025-05-01T10:00:00Z',
          updatedAt: '2025-05-01T10:00:00Z'
        },
        {
          id: 'vip-customers',
          name: 'VIP Customers',
          description: 'High-value customers with premium status',
          criteria: [
            { field: 'tags', operator: 'in', value: ['vip', 'premium', 'enterprise'] }
          ],
          contactCount: 156,
          createdAt: '2025-05-01T10:00:00Z',
          updatedAt: '2025-05-01T10:00:00Z'
        }
      ];

      setContacts(mockContacts);
      setSegments(mockSegments);
    } catch (error) {
      console.error('Failed to load contacts and segments:', error);
    } finally {
      setLoading(false);
    }
  };

  const filteredContacts = () => {
    let filtered = contacts;

    if (searchTerm) {
      filtered = filtered.filter(contact =>
        contact.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        contact.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
        contact.tags.some(tag => tag.toLowerCase().includes(searchTerm.toLowerCase()))
      );
    }

    if (filterSegment !== 'all') {
      filtered = filtered.filter(contact =>
        contact.segments.includes(filterSegment)
      );
    }

    return filtered;
  };

  const handleCreateContact = () => {
    setContactForm({
      name: '',
      email: '',
      phone: '',
      tags: [],
      segments: [],
      preferences: {
        email: true,
        sms: true,
        marketing: true,
        notifications: true
      }
    });
    setEditingContact(null);
    setIsCreating(true);
  };

  const handleSaveContact = async () => {
    if (!contactForm.name.trim() || !contactForm.email.trim()) {
      alert('Please fill in required fields (name and email)');
      return;
    }

    try {
      setLoading(true);
      
      const contactData: Contact = {
        id: editingContact?.id || `contact_${Date.now()}`,
        name: contactForm.name,
        email: contactForm.email,
        phone: contactForm.phone || undefined,
        tags: contactForm.tags,
        segments: contactForm.segments,
        preferences: contactForm.preferences,
        createdAt: editingContact?.createdAt || new Date().toISOString(),
        updatedAt: new Date().toISOString()
      };

      if (editingContact) {
        setContacts(prev => prev.map(c => c.id === contactData.id ? contactData : c));
      } else {
        setContacts(prev => [...prev, contactData]);
      }

      setIsCreating(false);
      setEditingContact(null);
    } catch (error) {
      console.error('Failed to save contact:', error);
      alert('Failed to save contact. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  if (isCreating) {
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-4">
            <Button variant="ghost" onClick={() => setIsCreating(false)}>
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Contacts
            </Button>
            <div>
              <h1 className="text-3xl font-bold tracking-tight">
                {editingContact ? 'Edit' : 'Create'} Contact
              </h1>
            </div>
          </div>
          <Button onClick={handleSaveContact} disabled={loading}>
            <Save className="h-4 w-4 mr-2" />
            Save Contact
          </Button>
        </div>

        <Card>
          <CardHeader>
            <CardTitle>Contact Information</CardTitle>
            <CardDescription>Basic contact details and preferences</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid gap-4 md:grid-cols-2">
              <div>
                <Label htmlFor="contact-name">Name *</Label>
                <Input
                  id="contact-name"
                  value={contactForm.name}
                  onChange={(e) => setContactForm(prev => ({ ...prev, name: e.target.value }))}
                  placeholder="Enter contact name"
                />
              </div>
              <div>
                <Label htmlFor="contact-email">Email *</Label>
                <Input
                  id="contact-email"
                  type="email"
                  value={contactForm.email}
                  onChange={(e) => setContactForm(prev => ({ ...prev, email: e.target.value }))}
                  placeholder="Enter email address"
                />
              </div>
            </div>

            <div>
              <Label htmlFor="contact-phone">Phone</Label>
              <Input
                id="contact-phone"
                value={contactForm.phone}
                onChange={(e) => setContactForm(prev => ({ ...prev, phone: e.target.value }))}
                placeholder="Enter phone number"
              />
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          {onBack && (
            <Button variant="ghost" onClick={onBack}>
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back
            </Button>
          )}
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Contact Management</h1>
            <p className="text-gray-600">Manage contacts and customer segments</p>
          </div>
        </div>
        <div className="flex gap-2">
          <Button variant="outline">
            <Upload className="h-4 w-4 mr-2" />
            Import
          </Button>
          <Button onClick={handleCreateContact}>
            <UserPlus className="h-4 w-4 mr-2" />
            Add Contact
          </Button>
        </div>
      </div>

      <Tabs value={activeTab} onValueChange={(value) => setActiveTab(value as 'contacts' | 'segments')}>
        <TabsList>
          <TabsTrigger value="contacts">Contacts</TabsTrigger>
          <TabsTrigger value="segments">Segments</TabsTrigger>
        </TabsList>

        <TabsContent value="contacts" className="space-y-4">
          <div className="flex items-center gap-4">
            <div className="flex-1 max-w-md">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                <Input
                  placeholder="Search contacts..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>
            <Select value={filterSegment} onValueChange={setFilterSegment}>
              <SelectTrigger className="w-48">
                <SelectValue placeholder="Filter by segment" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Segments</SelectItem>
                {segments.map((segment) => (
                  <SelectItem key={segment.id} value={segment.id}>
                    {segment.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <Card>
            <CardContent className="p-0">
              <div className="overflow-x-auto">
                <table className="w-full">
                  <thead className="border-b">
                    <tr>
                      <th className="text-left p-4">
                        <Checkbox
                          checked={selectedContacts.length === filteredContacts().length && filteredContacts().length > 0}
                          onCheckedChange={() => {
                            const filtered = filteredContacts();
                            if (selectedContacts.length === filtered.length) {
                              setSelectedContacts([]);
                            } else {
                              setSelectedContacts(filtered.map(c => c.id));
                            }
                          }}
                        />
                      </th>
                      <th className="text-left p-4">Name</th>
                      <th className="text-left p-4">Email</th>
                      <th className="text-left p-4">Phone</th>
                      <th className="text-left p-4">Tags</th>
                      <th className="text-left p-4">Segments</th>
                      <th className="text-left p-4">Actions</th>
                    </tr>
                  </thead>
                  <tbody>
                    {filteredContacts().map((contact) => (
                      <tr key={contact.id} className="border-b hover:bg-gray-50">
                        <td className="p-4">
                          <Checkbox
                            checked={selectedContacts.includes(contact.id)}
                            onCheckedChange={() => {
                              setSelectedContacts(prev =>
                                prev.includes(contact.id)
                                  ? prev.filter(id => id !== contact.id)
                                  : [...prev, contact.id]
                              );
                            }}
                          />
                        </td>
                        <td className="p-4 font-medium">{contact.name}</td>
                        <td className="p-4">{contact.email}</td>
                        <td className="p-4">{contact.phone || '-'}</td>
                        <td className="p-4">
                          <div className="flex flex-wrap gap-1">
                            {contact.tags.slice(0, 2).map((tag) => (
                              <Badge key={tag} variant="secondary" className="text-xs">
                                {tag}
                              </Badge>
                            ))}
                            {contact.tags.length > 2 && (
                              <Badge variant="outline" className="text-xs">
                                +{contact.tags.length - 2}
                              </Badge>
                            )}
                          </div>
                        </td>
                        <td className="p-4">
                          <div className="flex flex-wrap gap-1">
                            {contact.segments.slice(0, 2).map((segmentId) => {
                              const segment = segments.find(s => s.id === segmentId);
                              return segment ? (
                                <Badge key={segmentId} variant="outline" className="text-xs">
                                  {segment.name}
                                </Badge>
                              ) : null;
                            })}
                            {contact.segments.length > 2 && (
                              <Badge variant="outline" className="text-xs">
                                +{contact.segments.length - 2}
                              </Badge>
                            )}
                          </div>
                        </td>
                        <td className="p-4">
                          <div className="flex gap-1">
                            <Button variant="ghost" size="sm" onClick={() => {
                              setContactForm({
                                name: contact.name,
                                email: contact.email,
                                phone: contact.phone || '',
                                tags: contact.tags,
                                segments: contact.segments,
                                preferences: contact.preferences
                              });
                              setEditingContact(contact);
                              setIsCreating(true);
                            }}>
                              <Edit className="h-4 w-4" />
                            </Button>
                            <Button variant="ghost" size="sm" onClick={() => {
                              const confirmed = globalThis.confirm('Are you sure you want to delete this contact?');
                              if (confirmed) {
                                setContacts(prev => prev.filter(c => c.id !== contact.id));
                              }
                            }}>
                              <Trash2 className="h-4 w-4" />
                            </Button>
                          </div>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="segments" className="space-y-4">
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
            {segments.map((segment) => (
              <Card key={segment.id}>
                <CardHeader>
                  <CardTitle className="text-lg">{segment.name}</CardTitle>
                  <CardDescription>{segment.description}</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    <div className="flex items-center justify-between">
                      <span className="text-sm text-gray-600">Contacts:</span>
                      <Badge variant="secondary">{segment.contactCount.toLocaleString()}</Badge>
                    </div>
                    <div className="flex gap-2">
                      <Button variant="outline" size="sm" className="flex-1">
                        <Edit className="h-4 w-4 mr-2" />
                        Edit
                      </Button>
                      <Button variant="outline" size="sm">
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default ContactManager;
