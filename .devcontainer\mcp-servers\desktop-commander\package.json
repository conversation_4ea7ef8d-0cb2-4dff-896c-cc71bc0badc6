{"name": "desktop-commander-mcp-server", "version": "1.0.0", "description": "Desktop Commander MCP Server", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js"}, "dependencies": {"@modelcontextprotocol/sdk": "^1.0.0", "express": "^4.18.0", "child_process": "^1.0.0"}, "keywords": ["mcp", "desktop", "commander", "automation"], "author": "NXT Level Tech", "license": "MIT"}