"use strict";
/**
 * Central export file for all Supabase integrations
 *
 * This file ensures that all modules import the same Supabase client instance,
 * preventing the "Multiple GoTrueClient instances" warning.
 */
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __exportStar = (this && this.__exportStar) || function(m, exports) {
    for (var p in m) if (p !== "default" && !Object.prototype.hasOwnProperty.call(exports, p)) __createBinding(exports, m, p);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.handleSupabaseError = exports.SupabaseError = exports.executeSupabaseOperationArray = void 0;
// Export the singleton Supabase client
__exportStar(require("./client"), exports);
// Export utility functions
var supabase_1 = require("./supabase");
Object.defineProperty(exports, "executeSupabaseOperationArray", { enumerable: true, get: function () { return supabase_1.executeSupabaseOperationArray; } });
Object.defineProperty(exports, "SupabaseError", { enumerable: true, get: function () { return supabase_1.SupabaseError; } });
Object.defineProperty(exports, "handleSupabaseError", { enumerable: true, get: function () { return supabase_1.handleSupabaseError; } });
