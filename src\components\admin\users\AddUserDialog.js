import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage, } from '@/components/ui/form';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { UserPlus } from 'lucide-react';
import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import * as z from 'zod';
// Enhanced form schema with validation
const userFormSchema = z.object({
    username: z
        .string()
        .min(3, { message: "Username must be at least 3 characters" })
        .max(50, { message: "Username must be less than 50 characters" })
        .regex(/^[a-zA-Z0-9_]+$/, {
        message: "Username can only contain letters, numbers and underscores"
    }),
    email: z
        .string()
        .email({ message: "Invalid email address" })
        .max(100, { message: "Email must be less than 100 characters" }),
    password: z
        .string()
        .min(8, { message: "Password must be at least 8 characters" })
        .max(100, { message: "Password must be less than 100 characters" })
        .regex(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]+$/, {
        message: "Password must contain at least one uppercase letter, one lowercase letter, one number and one special character"
    }),
    role: z
        .string()
        .min(1, { message: "Please select a role" }),
});
const AddUserDialog = ({ open, onOpenChange, onAddUser, rolesData }) => {
    const userForm = useForm({
        resolver: zodResolver(userFormSchema),
        defaultValues: {
            username: '',
            email: '',
            password: '',
            role: '',
        },
    });
    const handleAddUser = (data) => {
        const newUser = {
            id: Math.random().toString(36).substring(2, 11),
            username: data.username,
            email: data.email,
            role: data.role,
            status: 'active',
            created: new Date().toISOString().split('T')[0]
        };
        onAddUser(newUser);
        userForm.reset();
    };
    return (_jsxs(Dialog, { open: open, onOpenChange: onOpenChange, children: [_jsx(DialogTrigger, { asChild: true, children: _jsxs(Button, { children: [_jsx(UserPlus, { className: "mr-2 h-4 w-4" }), "Add User"] }) }), _jsxs(DialogContent, { className: "sm:max-w-[500px]", children: [_jsxs(DialogHeader, { children: [_jsx(DialogTitle, { children: "Add New User" }), _jsx(DialogDescription, { children: "Create a new user account with specific role and permissions." })] }), _jsx(Form, { ...userForm, children: _jsxs("form", { onSubmit: userForm.handleSubmit(handleAddUser), className: "space-y-4 py-4", children: [_jsx(FormField, { control: userForm.control, name: "username", render: ({ field }) => (_jsxs(FormItem, { children: [_jsx(FormLabel, { children: "Username" }), _jsx(FormControl, { children: _jsx(Input, { placeholder: "Enter username", autoComplete: "username", ...field }) }), _jsx(FormMessage, {})] })) }), _jsx(FormField, { control: userForm.control, name: "email", render: ({ field }) => (_jsxs(FormItem, { children: [_jsx(FormLabel, { children: "Email" }), _jsx(FormControl, { children: _jsx(Input, { placeholder: "Enter email", ...field }) }), _jsx(FormMessage, {})] })) }), _jsx(FormField, { control: userForm.control, name: "password", render: ({ field }) => (_jsxs(FormItem, { children: [_jsx(FormLabel, { children: "Password" }), _jsx(FormControl, { children: _jsx(Input, { type: "password", placeholder: "Enter password", autoComplete: "new-password", ...field }) }), _jsx(FormMessage, {})] })) }), _jsx(FormField, { control: userForm.control, name: "role", render: ({ field }) => (_jsxs(FormItem, { children: [_jsx(FormLabel, { children: "Role" }), _jsxs(Select, { onValueChange: field.onChange, defaultValue: field.value, children: [_jsx(FormControl, { children: _jsx(SelectTrigger, { children: _jsx(SelectValue, { placeholder: "Select a role" }) }) }), _jsx(SelectContent, { children: rolesData.map(role => (_jsx(SelectItem, { value: role.name, children: role.name }, role.id))) })] }), _jsx(FormMessage, {})] })) }), _jsx(DialogFooter, { children: _jsx(Button, { type: "submit", children: "Create User" }) })] }) })] })] }));
};
export default AddUserDialog;
