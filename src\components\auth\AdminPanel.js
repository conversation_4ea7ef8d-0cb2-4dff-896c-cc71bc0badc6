import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
import { useBetterAuth } from '@/providers/BetterAuthProvider';
export function AdminPanel() {
    const { hasPermission, hasAnyPermission, user } = useBetterAuth();
    // Check for specific permission
    if (!hasPermission(Permission.ADMIN_PANEL)) {
        return _jsx("div", { children: "Access denied. Administrator privileges required." });
    }
    return (_jsxs("div", { children: [_jsx("h1", { children: "Administration Panel" }), hasPermission(Permission.USER_MANAGEMENT) && (_jsx("div", { children: "User Management Section" })), hasAnyPermission([Permission.FINANCIAL_ADMIN, Permission.FINANCIAL_VIEW]) && (_jsx("div", { children: "Financial Section" })), hasPermission(Permission.SYSTEM_MONITORING) && (_jsx("div", { children: "System Monitoring Section" }))] }));
}
