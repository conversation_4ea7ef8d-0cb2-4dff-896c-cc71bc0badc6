#!/usr/bin/env node

const https = require('https');

async function testLogin() {
  console.log('🔍 Testing Better Auth Login...\n');

  const authUrl = 'https://nxtdotx.co.za/api/auth/sign-in/email';
  const credentials = {
    email: '<EMAIL>',
    password: 'Admin123!@#'
  };

  console.log('Request URL:', authUrl);
  console.log('Request Body:', JSON.stringify(credentials, null, 2));

  const postData = JSON.stringify(credentials);

  return new Promise((resolve, reject) => {
    const urlObj = new URL(authUrl);
    const options = {
      hostname: urlObj.hostname,
      port: 443,
      path: urlObj.pathname,
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Content-Length': Buffer.byteLength(postData),
        'Accept': 'application/json',
        'User-Agent': 'Better-Auth-Test/1.0'
      }
    };

    console.log('\n🔍 Request Options:', options);

    const req = https.request(options, (res) => {
      let body = '';
      
      console.log('\n📥 Response Status:', res.statusCode);
      console.log('Response Headers:', res.headers);

      res.on('data', (chunk) => body += chunk);
      res.on('end', () => {
        console.log('\n📄 Response Body:', body);
        
        try {
          const parsed = JSON.parse(body);
          console.log('\n✅ Parsed Response:', JSON.stringify(parsed, null, 2));
          
          if (res.statusCode === 200) {
            console.log('\n🎉 Login successful!');
            if (parsed.session) {
              console.log('Session ID:', parsed.session.id);
              console.log('User ID:', parsed.session.userId);
            }
          } else {
            console.log('\n❌ Login failed with status:', res.statusCode);
            if (parsed.error) {
              console.log('Error:', parsed.error.message || parsed.error);
            }
          }
        } catch (e) {
          console.log('\n⚠️  Could not parse response as JSON');
          console.log('Raw response:', body);
        }
        
        resolve({ status: res.statusCode, headers: res.headers, body });
      });
    });

    req.on('error', (error) => {
      console.error('\n❌ Request error:', error.message);
      reject(error);
    });

    req.write(postData);
    req.end();
  });
}

// Also test the session endpoint
async function testSession() {
  console.log('\n\n🔍 Testing Session Endpoint...\n');

  const sessionUrl = 'https://nxtdotx.co.za/api/auth/get-session';

  return new Promise((resolve, reject) => {
    https.get(sessionUrl, (res) => {
      let body = '';
      
      console.log('📥 Session Response Status:', res.statusCode);
      
      res.on('data', (chunk) => body += chunk);
      res.on('end', () => {
        console.log('📄 Session Response:', body);
        resolve({ status: res.statusCode, body });
      });
    }).on('error', (error) => {
      console.error('❌ Session request error:', error.message);
      reject(error);
    });
  });
}

async function runTests() {
  try {
    await testLogin();
    await testSession();
  } catch (error) {
    console.error('Test failed:', error);
  }
}

runTests();