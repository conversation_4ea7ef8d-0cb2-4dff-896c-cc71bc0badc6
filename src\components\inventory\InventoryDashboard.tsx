'use client';

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import { 
  Search, 
  Plus, 
  Package, 
  AlertTriangle, 
  TrendingUp, 
  TrendingDown,
  Warehouse,
  ShoppingCart,
  Edit,
  Trash2,
  MoreHorizontal,
  Filter,
  Download,
  Upload,
  Barcode,
  MapPin,
  Calendar,
  DollarSign
} from 'lucide-react';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@/components/ui/dropdown-menu';
import { toast } from 'sonner';

interface InventoryItem {
  id: string;
  part_number: string;
  part_name: string;
  description: string;
  category: string;
  brand: string;
  model: string;
  current_stock: number;
  minimum_stock: number;
  maximum_stock: number;
  unit_cost: number;
  rental_rate: number;
  location: string;
  condition: 'New' | 'Good' | 'Fair' | 'Poor' | 'Out of Service';
  status: 'Available' | 'Rented' | 'Maintenance' | 'Reserved';
  supplier_id: string;
  supplier_name: string;
  last_maintenance: string;
  next_maintenance: string;
  purchase_date: string;
  warranty_expiry: string;
  serial_number: string;
  barcode: string;
  notes: string;
  total_rentals: number;
  total_revenue: number;
  created_at: string;
}

interface InventoryFormData {
  part_name: string;
  description: string;
  category: string;
  brand: string;
  model: string;
  current_stock: number;
  minimum_stock: number;
  maximum_stock: number;
  unit_cost: number;
  rental_rate: number;
  location: string;
  condition: 'New' | 'Good' | 'Fair' | 'Poor' | 'Out of Service';
  supplier_id: string;
  serial_number: string;
  barcode: string;
  notes: string;
}

interface InventoryMetrics {
  total_items: number;
  total_value: number;
  low_stock_items: number;
  out_of_stock_items: number;
  available_items: number;
  rented_items: number;
  maintenance_items: number;
  monthly_revenue: number;
}

const InventoryDashboard: React.FC = () => {
  const [inventory, setInventory] = useState<InventoryItem[]>([]);
  const [filteredInventory, setFilteredInventory] = useState<InventoryItem[]>([]);
  const [metrics, setMetrics] = useState<InventoryMetrics>({
    total_items: 0,
    total_value: 0,
    low_stock_items: 0,
    out_of_stock_items: 0,
    available_items: 0,
    rented_items: 0,
    maintenance_items: 0,
    monthly_revenue: 0
  });
  const [searchTerm, setSearchTerm] = useState('');
  const [categoryFilter, setCategoryFilter] = useState<string>('all');
  const [statusFilter, setStatusFilter] = useState<string>('all');
  const [conditionFilter, setConditionFilter] = useState<string>('all');
  const [isLoading, setIsLoading] = useState(true);
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [editingItem, setEditingItem] = useState<InventoryItem | null>(null);
  const [formData, setFormData] = useState<InventoryFormData>({
    part_name: '',
    description: '',
    category: '',
    brand: '',
    model: '',
    current_stock: 0,
    minimum_stock: 1,
    maximum_stock: 10,
    unit_cost: 0,
    rental_rate: 0,
    location: '',
    condition: 'New',
    supplier_id: '',
    serial_number: '',
    barcode: '',
    notes: ''
  });

  // Mock data for development
  useEffect(() => {
    const mockInventory: InventoryItem[] = [
      {
        id: '1',
        part_number: 'EXC001',
        part_name: 'Caterpillar 320D Excavator',
        description: '20-ton hydraulic excavator with GPS tracking',
        category: 'Heavy Machinery',
        brand: 'Caterpillar',
        model: '320D',
        current_stock: 3,
        minimum_stock: 1,
        maximum_stock: 5,
        unit_cost: 850000,
        rental_rate: 2500,
        location: 'Yard A - Section 1',
        condition: 'Good',
        status: 'Available',
        supplier_id: 'SUP001',
        supplier_name: 'Heavy Equipment SA',
        last_maintenance: '2024-04-15',
        next_maintenance: '2024-07-15',
        purchase_date: '2023-01-15',
        warranty_expiry: '2026-01-15',
        serial_number: 'CAT320D001',
        barcode: '1234567890123',
        notes: 'Recently serviced, excellent condition',
        total_rentals: 45,
        total_revenue: 112500,
        created_at: '2023-01-15'
      },
      {
        id: '2',
        part_number: 'GEN001',
        part_name: 'Diesel Generator 100kVA',
        description: 'Industrial diesel generator with automatic start',
        category: 'Power Equipment',
        brand: 'Cummins',
        model: 'C100D5',
        current_stock: 0,
        minimum_stock: 2,
        maximum_stock: 8,
        unit_cost: 125000,
        rental_rate: 800,
        location: 'Yard B - Section 3',
        condition: 'Good',
        status: 'Rented',
        supplier_id: 'SUP002',
        supplier_name: 'Power Solutions Ltd',
        last_maintenance: '2024-05-01',
        next_maintenance: '2024-08-01',
        purchase_date: '2023-03-10',
        warranty_expiry: '2025-03-10',
        serial_number: 'CUM100D001',
        barcode: '2345678901234',
        notes: 'All units currently on rental',
        total_rentals: 28,
        total_revenue: 22400,
        created_at: '2023-03-10'
      },
      {
        id: '3',
        part_number: 'SCF001',
        part_name: 'Steel Scaffolding System',
        description: 'Modular steel scaffolding with safety rails',
        category: 'Construction Equipment',
        brand: 'SafeScaff',
        model: 'SS-2000',
        current_stock: 15,
        minimum_stock: 10,
        maximum_stock: 50,
        unit_cost: 2500,
        rental_rate: 25,
        location: 'Warehouse C',
        condition: 'Good',
        status: 'Available',
        supplier_id: 'SUP003',
        supplier_name: 'Construction Supplies Co',
        last_maintenance: '2024-05-10',
        next_maintenance: '2024-11-10',
        purchase_date: '2023-02-20',
        warranty_expiry: '2025-02-20',
        serial_number: 'SS2000-001',
        barcode: '3456789012345',
        notes: 'Popular item, high demand',
        total_rentals: 120,
        total_revenue: 3000,
        created_at: '2023-02-20'
      },
      {
        id: '4',
        part_number: 'CMP001',
        part_name: 'Air Compressor 185CFM',
        description: 'Portable diesel air compressor',
        category: 'Air Tools',
        brand: 'Atlas Copco',
        model: 'XAS185',
        current_stock: 1,
        minimum_stock: 2,
        maximum_stock: 6,
        unit_cost: 95000,
        rental_rate: 450,
        location: 'Yard A - Section 2',
        condition: 'Fair',
        status: 'Maintenance',
        supplier_id: 'SUP004',
        supplier_name: 'Compressed Air Systems',
        last_maintenance: '2024-05-20',
        next_maintenance: '2024-06-20',
        purchase_date: '2022-11-15',
        warranty_expiry: '2024-11-15',
        serial_number: 'XAS185-001',
        barcode: '4567890123456',
        notes: 'Scheduled maintenance in progress',
        total_rentals: 35,
        total_revenue: 15750,
        created_at: '2022-11-15'
      }
    ];

    setTimeout(() => {
      setInventory(mockInventory);
      setFilteredInventory(mockInventory);
      
      // Calculate metrics
      const totalItems = mockInventory.length;
      const totalValue = mockInventory.reduce((sum, item) => sum + (item.unit_cost * item.current_stock), 0);
      const lowStockItems = mockInventory.filter(item => item.current_stock <= item.minimum_stock).length;
      const outOfStockItems = mockInventory.filter(item => item.current_stock === 0).length;
      const availableItems = mockInventory.filter(item => item.status === 'Available').length;
      const rentedItems = mockInventory.filter(item => item.status === 'Rented').length;
      const maintenanceItems = mockInventory.filter(item => item.status === 'Maintenance').length;
      const monthlyRevenue = mockInventory.reduce((sum, item) => sum + item.total_revenue, 0);

      setMetrics({
        total_items: totalItems,
        total_value: totalValue,
        low_stock_items: lowStockItems,
        out_of_stock_items: outOfStockItems,
        available_items: availableItems,
        rented_items: rentedItems,
        maintenance_items: maintenanceItems,
        monthly_revenue: monthlyRevenue
      });

      setIsLoading(false);
    }, 1000);
  }, []);

  // Filter inventory based on search and filters
  useEffect(() => {
    let filtered = inventory;

    if (searchTerm) {
      filtered = filtered.filter(item =>
        item.part_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        item.part_number.toLowerCase().includes(searchTerm.toLowerCase()) ||
        item.brand.toLowerCase().includes(searchTerm.toLowerCase()) ||
        item.model.toLowerCase().includes(searchTerm.toLowerCase()) ||
        item.description.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    if (categoryFilter !== 'all') {
      filtered = filtered.filter(item => item.category === categoryFilter);
    }

    if (statusFilter !== 'all') {
      filtered = filtered.filter(item => item.status === statusFilter);
    }

    if (conditionFilter !== 'all') {
      filtered = filtered.filter(item => item.condition === conditionFilter);
    }

    setFilteredInventory(filtered);
  }, [inventory, searchTerm, categoryFilter, statusFilter, conditionFilter]);

  const handleCreateItem = async () => {
    try {
      const newItem: InventoryItem = {
        id: Date.now().toString(),
        part_number: `ITM${String(inventory.length + 1).padStart(3, '0')}`,
        ...formData,
        status: 'Available',
        supplier_name: 'Default Supplier',
        last_maintenance: '',
        next_maintenance: '',
        purchase_date: new Date().toISOString().split('T')[0],
        warranty_expiry: '',
        total_rentals: 0,
        total_revenue: 0,
        created_at: new Date().toISOString()
      };

      setInventory(prev => [...prev, newItem]);
      setIsDialogOpen(false);
      resetForm();
      toast.success('Inventory item created successfully');
    } catch (error) {
      toast.error('Failed to create inventory item');
    }
  };

  const handleUpdateItem = async () => {
    if (!editingItem) return;

    try {
      const updatedItem = { ...editingItem, ...formData };
      setInventory(prev => prev.map(item => item.id === editingItem.id ? updatedItem : item));
      setIsDialogOpen(false);
      setEditingItem(null);
      resetForm();
      toast.success('Inventory item updated successfully');
    } catch (error) {
      toast.error('Failed to update inventory item');
    }
  };

  const handleDeleteItem = async (itemId: string) => {
    try {
      setInventory(prev => prev.filter(item => item.id !== itemId));
      toast.success('Inventory item deleted successfully');
    } catch (error) {
      toast.error('Failed to delete inventory item');
    }
  };

  const resetForm = () => {
    setFormData({
      part_name: '',
      description: '',
      category: '',
      brand: '',
      model: '',
      current_stock: 0,
      minimum_stock: 1,
      maximum_stock: 10,
      unit_cost: 0,
      rental_rate: 0,
      location: '',
      condition: 'New',
      supplier_id: '',
      serial_number: '',
      barcode: '',
      notes: ''
    });
  };

  const openEditDialog = (item: InventoryItem) => {
    setEditingItem(item);
    setFormData({
      part_name: item.part_name,
      description: item.description,
      category: item.category,
      brand: item.brand,
      model: item.model,
      current_stock: item.current_stock,
      minimum_stock: item.minimum_stock,
      maximum_stock: item.maximum_stock,
      unit_cost: item.unit_cost,
      rental_rate: item.rental_rate,
      location: item.location,
      condition: item.condition,
      supplier_id: item.supplier_id,
      serial_number: item.serial_number,
      barcode: item.barcode,
      notes: item.notes
    });
    setIsDialogOpen(true);
  };

  const getStatusBadgeVariant = (status: string) => {
    switch (status) {
      case 'Available': return 'default';
      case 'Rented': return 'secondary';
      case 'Maintenance': return 'destructive';
      case 'Reserved': return 'outline';
      default: return 'secondary';
    }
  };

  const getConditionBadgeVariant = (condition: string) => {
    switch (condition) {
      case 'New': return 'default';
      case 'Good': return 'secondary';
      case 'Fair': return 'outline';
      case 'Poor': return 'destructive';
      case 'Out of Service': return 'destructive';
      default: return 'secondary';
    }
  };

  const getStockStatus = (item: InventoryItem) => {
    if (item.current_stock === 0) return { status: 'Out of Stock', variant: 'destructive' as const };
    if (item.current_stock <= item.minimum_stock) return { status: 'Low Stock', variant: 'destructive' as const };
    return { status: 'In Stock', variant: 'default' as const };
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-ZA', {
      style: 'currency',
      currency: 'ZAR'
    }).format(amount);
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Inventory Management</h1>
          <p className="text-muted-foreground">
            Track and manage your equipment inventory
          </p>
        </div>
        <div className="flex gap-2">
          <Button variant="outline" size="sm">
            <Download className="h-4 w-4 mr-2" />
            Export
          </Button>
          <Button variant="outline" size="sm">
            <Upload className="h-4 w-4 mr-2" />
            Import
          </Button>
          <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
            <DialogTrigger asChild>
              <Button onClick={() => { setEditingItem(null); resetForm(); }}>
                <Plus className="h-4 w-4 mr-2" />
                Add Item
              </Button>
            </DialogTrigger>
            <DialogContent className="max-w-3xl max-h-[90vh] overflow-y-auto">
              <DialogHeader>
                <DialogTitle>
                  {editingItem ? 'Edit Inventory Item' : 'Add New Inventory Item'}
                </DialogTitle>
                <DialogDescription>
                  {editingItem 
                    ? 'Update inventory item details and specifications.'
                    : 'Add a new item to your inventory with complete specifications.'
                  }
                </DialogDescription>
              </DialogHeader>
              
              <div className="grid gap-4 py-4">
                <Tabs defaultValue="basic" className="w-full">
                  <TabsList className="grid w-full grid-cols-4">
                    <TabsTrigger value="basic">Basic Info</TabsTrigger>
                    <TabsTrigger value="stock">Stock & Pricing</TabsTrigger>
                    <TabsTrigger value="details">Details</TabsTrigger>
                    <TabsTrigger value="tracking">Tracking</TabsTrigger>
                  </TabsList>
                  
                  <TabsContent value="basic" className="space-y-4">
                    <div className="grid grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <Label htmlFor="part_name">Item Name *</Label>
                        <Input
                          id="part_name"
                          value={formData.part_name}
                          onChange={(e) => setFormData(prev => ({ ...prev, part_name: e.target.value }))}
                          placeholder="Enter item name"
                        />
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="category">Category *</Label>
                        <Select
                          value={formData.category}
                          onValueChange={(value) => setFormData(prev => ({ ...prev, category: value }))}
                        >
                          <SelectTrigger>
                            <SelectValue placeholder="Select category" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="Heavy Machinery">Heavy Machinery</SelectItem>
                            <SelectItem value="Power Equipment">Power Equipment</SelectItem>
                            <SelectItem value="Construction Equipment">Construction Equipment</SelectItem>
                            <SelectItem value="Air Tools">Air Tools</SelectItem>
                            <SelectItem value="Hand Tools">Hand Tools</SelectItem>
                            <SelectItem value="Safety Equipment">Safety Equipment</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                    </div>
                    
                    <div className="grid grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <Label htmlFor="brand">Brand</Label>
                        <Input
                          id="brand"
                          value={formData.brand}
                          onChange={(e) => setFormData(prev => ({ ...prev, brand: e.target.value }))}
                          placeholder="Equipment brand"
                        />
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="model">Model</Label>
                        <Input
                          id="model"
                          value={formData.model}
                          onChange={(e) => setFormData(prev => ({ ...prev, model: e.target.value }))}
                          placeholder="Model number"
                        />
                      </div>
                    </div>
                    
                    <div className="space-y-2">
                      <Label htmlFor="description">Description</Label>
                      <Textarea
                        id="description"
                        value={formData.description}
                        onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
                        placeholder="Detailed description of the item..."
                        rows={3}
                      />
                    </div>
                  </TabsContent>
                  
                  <TabsContent value="stock" className="space-y-4">
                    <div className="grid grid-cols-3 gap-4">
                      <div className="space-y-2">
                        <Label htmlFor="current_stock">Current Stock</Label>
                        <Input
                          id="current_stock"
                          type="number"
                          value={formData.current_stock}
                          onChange={(e) => setFormData(prev => ({ ...prev, current_stock: Number(e.target.value) }))}
                          placeholder="0"
                        />
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="minimum_stock">Minimum Stock</Label>
                        <Input
                          id="minimum_stock"
                          type="number"
                          value={formData.minimum_stock}
                          onChange={(e) => setFormData(prev => ({ ...prev, minimum_stock: Number(e.target.value) }))}
                          placeholder="1"
                        />
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="maximum_stock">Maximum Stock</Label>
                        <Input
                          id="maximum_stock"
                          type="number"
                          value={formData.maximum_stock}
                          onChange={(e) => setFormData(prev => ({ ...prev, maximum_stock: Number(e.target.value) }))}
                          placeholder="10"
                        />
                      </div>
                    </div>
                    
                    <div className="grid grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <Label htmlFor="unit_cost">Unit Cost (ZAR)</Label>
                        <Input
                          id="unit_cost"
                          type="number"
                          value={formData.unit_cost}
                          onChange={(e) => setFormData(prev => ({ ...prev, unit_cost: Number(e.target.value) }))}
                          placeholder="0"
                        />
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="rental_rate">Daily Rental Rate (ZAR)</Label>
                        <Input
                          id="rental_rate"
                          type="number"
                          value={formData.rental_rate}
                          onChange={(e) => setFormData(prev => ({ ...prev, rental_rate: Number(e.target.value) }))}
                          placeholder="0"
                        />
                      </div>
                    </div>
                  </TabsContent>
                  
                  <TabsContent value="details" className="space-y-4">
                    <div className="grid grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <Label htmlFor="location">Location</Label>
                        <Input
                          id="location"
                          value={formData.location}
                          onChange={(e) => setFormData(prev => ({ ...prev, location: e.target.value }))}
                          placeholder="Storage location"
                        />
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="condition">Condition</Label>
                        <Select
                          value={formData.condition}
                          onValueChange={(value: 'New' | 'Good' | 'Fair' | 'Poor' | 'Out of Service') => 
                            setFormData(prev => ({ ...prev, condition: value }))
                          }
                        >
                          <SelectTrigger>
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="New">New</SelectItem>
                            <SelectItem value="Good">Good</SelectItem>
                            <SelectItem value="Fair">Fair</SelectItem>
                            <SelectItem value="Poor">Poor</SelectItem>
                            <SelectItem value="Out of Service">Out of Service</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                    </div>
                    
                    <div className="space-y-2">
                      <Label htmlFor="supplier_id">Supplier ID</Label>
                      <Input
                        id="supplier_id"
                        value={formData.supplier_id}
                        onChange={(e) => setFormData(prev => ({ ...prev, supplier_id: e.target.value }))}
                        placeholder="Supplier reference"
                      />
                    </div>
                    
                    <div className="space-y-2">
                      <Label htmlFor="notes">Notes</Label>
                      <Textarea
                        id="notes"
                        value={formData.notes}
                        onChange={(e) => setFormData(prev => ({ ...prev, notes: e.target.value }))}
                        placeholder="Additional notes about this item..."
                        rows={3}
                      />
                    </div>
                  </TabsContent>
                  
                  <TabsContent value="tracking" className="space-y-4">
                    <div className="grid grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <Label htmlFor="serial_number">Serial Number</Label>
                        <Input
                          id="serial_number"
                          value={formData.serial_number}
                          onChange={(e) => setFormData(prev => ({ ...prev, serial_number: e.target.value }))}
                          placeholder="Unique serial number"
                        />
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="barcode">Barcode</Label>
                        <Input
                          id="barcode"
                          value={formData.barcode}
                          onChange={(e) => setFormData(prev => ({ ...prev, barcode: e.target.value }))}
                          placeholder="Barcode for scanning"
                        />
                      </div>
                    </div>
                  </TabsContent>
                </Tabs>
              </div>
              
              <div className="flex justify-end gap-2">
                <Button variant="outline" onClick={() => setIsDialogOpen(false)}>
                  Cancel
                </Button>
                <Button onClick={editingItem ? handleUpdateItem : handleCreateItem}>
                  {editingItem ? 'Update Item' : 'Create Item'}
                </Button>
              </div>
            </DialogContent>
          </Dialog>
        </div>
      </div>

      {/* Metrics Cards */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Items</CardTitle>
            <Package className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{metrics.total_items}</div>
            <p className="text-xs text-muted-foreground">
              {formatCurrency(metrics.total_value)} total value
            </p>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Available</CardTitle>
            <Warehouse className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">{metrics.available_items}</div>
            <p className="text-xs text-muted-foreground">
              Ready for rental
            </p>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Low Stock</CardTitle>
            <AlertTriangle className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-orange-600">{metrics.low_stock_items}</div>
            <p className="text-xs text-muted-foreground">
              {metrics.out_of_stock_items} out of stock
            </p>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Monthly Revenue</CardTitle>
            <DollarSign className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">
              {formatCurrency(metrics.monthly_revenue)}
            </div>
            <p className="text-xs text-muted-foreground">
              From inventory rentals
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Filters and Search */}
      <Card>
        <CardContent className="pt-6">
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
                <Input
                  placeholder="Search inventory by name, part number, brand, or model..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>
            <div className="flex gap-2">
              <Select value={categoryFilter} onValueChange={setCategoryFilter}>
                <SelectTrigger className="w-40">
                  <SelectValue placeholder="Category" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Categories</SelectItem>
                  <SelectItem value="Heavy Machinery">Heavy Machinery</SelectItem>
                  <SelectItem value="Power Equipment">Power Equipment</SelectItem>
                  <SelectItem value="Construction Equipment">Construction Equipment</SelectItem>
                  <SelectItem value="Air Tools">Air Tools</SelectItem>
                </SelectContent>
              </Select>
              <Select value={statusFilter} onValueChange={setStatusFilter}>
                <SelectTrigger className="w-32">
                  <SelectValue placeholder="Status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Status</SelectItem>
                  <SelectItem value="Available">Available</SelectItem>
                  <SelectItem value="Rented">Rented</SelectItem>
                  <SelectItem value="Maintenance">Maintenance</SelectItem>
                  <SelectItem value="Reserved">Reserved</SelectItem>
                </SelectContent>
              </Select>
              <Select value={conditionFilter} onValueChange={setConditionFilter}>
                <SelectTrigger className="w-32">
                  <SelectValue placeholder="Condition" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Conditions</SelectItem>
                  <SelectItem value="New">New</SelectItem>
                  <SelectItem value="Good">Good</SelectItem>
                  <SelectItem value="Fair">Fair</SelectItem>
                  <SelectItem value="Poor">Poor</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Inventory Grid */}
      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
        {filteredInventory.map((item) => {
          const stockStatus = getStockStatus(item);
          return (
            <Card key={item.id} className="hover:shadow-md transition-shadow">
              <CardHeader className="pb-3">
                <div className="flex justify-between items-start">
                  <div className="space-y-1">
                    <CardTitle className="text-lg">{item.part_name}</CardTitle>
                    <CardDescription className="text-sm">
                      {item.part_number} • {item.brand} {item.model}
                    </CardDescription>
                  </div>
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="ghost" size="sm">
                        <MoreHorizontal className="h-4 w-4" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                      <DropdownMenuItem onClick={() => openEditDialog(item)}>
                        <Edit className="h-4 w-4 mr-2" />
                        Edit
                      </DropdownMenuItem>
                      <DropdownMenuItem 
                        onClick={() => handleDeleteItem(item.id)}
                        className="text-destructive"
                      >
                        <Trash2 className="h-4 w-4 mr-2" />
                        Delete
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </div>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex flex-wrap gap-2">
                  <Badge variant={getStatusBadgeVariant(item.status)}>
                    {item.status}
                  </Badge>
                  <Badge variant={getConditionBadgeVariant(item.condition)}>
                    {item.condition}
                  </Badge>
                  <Badge variant={stockStatus.variant}>
                    {stockStatus.status}
                  </Badge>
                </div>
                
                <div className="space-y-2">
                  <div className="flex items-center text-sm text-muted-foreground">
                    <Package className="h-4 w-4 mr-2" />
                    Stock: {item.current_stock} / {item.maximum_stock}
                  </div>
                  <div className="flex items-center text-sm text-muted-foreground">
                    <MapPin className="h-4 w-4 mr-2" />
                    {item.location}
                  </div>
                  <div className="flex items-center text-sm text-muted-foreground">
                    <DollarSign className="h-4 w-4 mr-2" />
                    {formatCurrency(item.rental_rate)}/day
                  </div>
                </div>
                
                <div className="grid grid-cols-2 gap-4 pt-2 border-t">
                  <div className="text-center">
                    <div className="text-lg font-bold text-primary">
                      {item.total_rentals}
                    </div>
                    <div className="text-xs text-muted-foreground">Rentals</div>
                  </div>
                  <div className="text-center">
                    <div className="text-lg font-bold text-green-600">
                      {formatCurrency(item.total_revenue)}
                    </div>
                    <div className="text-xs text-muted-foreground">Revenue</div>
                  </div>
                </div>
                
                {item.description && (
                  <p className="text-sm text-muted-foreground pt-2 border-t">
                    {item.description.length > 100 
                      ? `${item.description.substring(0, 100)}...` 
                      : item.description
                    }
                  </p>
                )}
              </CardContent>
            </Card>
          );
        })}
      </div>

      {filteredInventory.length === 0 && (
        <Card>
          <CardContent className="flex flex-col items-center justify-center py-12">
            <div className="text-muted-foreground text-center">
              <Package className="h-12 w-12 mx-auto mb-4 opacity-50" />
              <h3 className="text-lg font-medium mb-2">No inventory items found</h3>
              <p className="text-sm">
                {searchTerm || categoryFilter !== 'all' || statusFilter !== 'all' || conditionFilter !== 'all'
                  ? 'Try adjusting your search criteria or filters.'
                  : 'Get started by adding your first inventory item.'
                }
              </p>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
};

export default InventoryDashboard;
