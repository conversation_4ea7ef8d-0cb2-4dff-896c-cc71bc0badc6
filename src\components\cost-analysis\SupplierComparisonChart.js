import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON><PERSON>, <PERSON>, Cell, Tooltip, Legend, ResponsiveContainer } from 'recharts';
export const SupplierComparisonChart = ({ data, title, description, colors, className }) => {
    return (_jsxs(Card, { className: `backdrop-blur-md bg-white/30 border border-white/10 ${className}`, children: [_jsxs(CardHeader, { children: [_jsx(CardTitle, { children: title }), _jsx(CardDescription, { children: description })] }), _jsx(CardContent, { className: "h-80", children: _jsx(ResponsiveContainer, { width: "100%", height: "100%", children: _jsxs(<PERSON><PERSON><PERSON>, { children: [_jsx(Pie, { data: data, cx: "50%", cy: "50%", labelLine: false, outerRadius: 80, fill: "#8884d8", dataKey: "value", label: ({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`, children: data.map((entry, index) => (_jsx(Cell, { fill: colors[index % colors.length] }, `cell-${index}`))) }), _jsx(Tooltip, { formatter: (value) => `${value}%` }), _jsx(Legend, {})] }) }) })] }));
};
