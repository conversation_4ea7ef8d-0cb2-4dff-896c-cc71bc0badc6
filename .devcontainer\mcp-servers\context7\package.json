{"name": "context7-mcp-server", "version": "1.0.0", "description": "Context7 MCP Server for Upstash Redis", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js"}, "dependencies": {"@modelcontextprotocol/sdk": "^1.0.0", "axios": "^1.6.0", "express": "^4.18.0", "@upstash/redis": "^1.25.0"}, "keywords": ["mcp", "context7", "upstash", "redis"], "author": "NXT Level Tech", "license": "MIT"}