import React, { useState, useEffect } from 'react';
// Cash Management Component
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { 
  Table, 
  TableBody, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from '@/components/ui/table';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Label } from '@/components/ui/label';
import { 
  Plus, 
  Search, 
  Banknote,
  TrendingUp,
  TrendingDown,
  Building,
  CreditCard,
  Loader2,
  RefreshCw,
  AlertCircle
} from 'lucide-react';
import { CashManagementService } from '../../services/financial';
import { BankAccount, BankAccountType } from '../../types/financial';

const CashManagement: React.FC = () => {
  const [bankAccounts, setBankAccounts] = useState<BankAccount[]>([]);
  const [cashPosition, setCashPosition] = useState<any>(null);
  const [cashFlowForecast, setCashFlowForecast] = useState<any[]>([]);
  const [reconciliationHistory, setReconciliationHistory] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
  const [newAccount, setNewAccount] = useState({
    accountName: '',
    accountNumber: '',
    bankName: '',
    bankCode: '',
    currency: 'USD',
    type: BankAccountType.CHECKING,
    balance: 0
  });

  useEffect(() => {
    loadData();
  }, []);

  const loadData = async () => {
    try {
      setLoading(true);
      const [accounts, position, forecast, history] = await Promise.all([
        CashManagementService.getBankAccounts(),
        CashManagementService.getCashPosition(),
        CashManagementService.getCashFlowForecast(30),
        CashManagementService.getReconciliationHistory()
      ]);
      
      setBankAccounts(accounts);
      setCashPosition(position);
      setCashFlowForecast(forecast);
      setReconciliationHistory(history);
    } catch (error) {
      console.error('Error loading cash management data:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleCreateAccount = async () => {
    try {
      await CashManagementService.createBankAccount({
        ...newAccount,
        isActive: true
      });
      setIsCreateDialogOpen(false);
      setNewAccount({
        accountName: '',
        accountNumber: '',
        bankName: '',
        bankCode: '',
        currency: 'USD',
        type: BankAccountType.CHECKING,
        balance: 0
      });
      loadData();
    } catch (error) {
      console.error('Error creating bank account:', error);
    }
  };

  const formatCurrency = (amount: number, currency: string = 'USD') => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: currency
    }).format(amount);
  };

  const formatDate = (date: Date | string) => {
    const dateObj = typeof date === 'string' ? new Date(date) : date;
    return new Intl.DateTimeFormat('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    }).format(dateObj);
  };

  const getAccountTypeIcon = (type: BankAccountType) => {
    switch (type) {
      case BankAccountType.CHECKING:
        return <Building className="h-4 w-4" />;
      case BankAccountType.SAVINGS:
        return <Banknote className="h-4 w-4" />;
      case BankAccountType.CREDIT_CARD:
        return <CreditCard className="h-4 w-4" />;
      default:
        return <Building className="h-4 w-4" />;
    }
  };

  const getAccountTypeColor = (type: BankAccountType) => {
    switch (type) {
      case BankAccountType.CHECKING:
        return 'bg-blue-100 text-blue-800';
      case BankAccountType.SAVINGS:
        return 'bg-green-100 text-green-800';
      case BankAccountType.CREDIT_CARD:
        return 'bg-purple-100 text-purple-800';
      case BankAccountType.CASH:
        return 'bg-yellow-100 text-yellow-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="flex items-center space-x-2">
          <Loader2 className="h-6 w-6 animate-spin" />
          <span>Loading cash management data...</span>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Cash Management</h1>
          <p className="text-muted-foreground">
            Monitor cash position, bank accounts, and cash flow forecasting
          </p>
        </div>
        <div className="flex space-x-2">
          <Button variant="outline" onClick={loadData}>
            <RefreshCw className="h-4 w-4 mr-2" />
            Refresh
          </Button>
          <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
            <DialogTrigger asChild>
              <Button>
                <Plus className="h-4 w-4 mr-2" />
                Add Bank Account
              </Button>
            </DialogTrigger>
            <DialogContent className="sm:max-w-[425px]">
              <DialogHeader>
                <DialogTitle>Add Bank Account</DialogTitle>
                <DialogDescription>
                  Add a new bank account to track cash position.
                </DialogDescription>
              </DialogHeader>
              <div className="grid gap-4 py-4">
                <div className="grid grid-cols-4 items-center gap-4">
                  <Label htmlFor="accountName" className="text-right">Name</Label>
                  <Input
                    id="accountName"
                    placeholder="e.g., Main Checking"
                    value={newAccount.accountName}
                    onChange={(e) => setNewAccount({...newAccount, accountName: e.target.value})}
                    className="col-span-3"
                  />
                </div>
                <div className="grid grid-cols-4 items-center gap-4">
                  <Label htmlFor="accountNumber" className="text-right">Number</Label>
                  <Input
                    id="accountNumber"
                    placeholder="Account number"
                    value={newAccount.accountNumber}
                    onChange={(e) => setNewAccount({...newAccount, accountNumber: e.target.value})}
                    className="col-span-3"
                  />
                </div>
                <div className="grid grid-cols-4 items-center gap-4">
                  <Label htmlFor="bankName" className="text-right">Bank</Label>
                  <Input
                    id="bankName"
                    placeholder="Bank name"
                    value={newAccount.bankName}
                    onChange={(e) => setNewAccount({...newAccount, bankName: e.target.value})}
                    className="col-span-3"
                  />
                </div>
                <div className="grid grid-cols-4 items-center gap-4">
                  <Label htmlFor="type" className="text-right">Type</Label>
                  <Select value={newAccount.type} onValueChange={(value) => setNewAccount({...newAccount, type: value as BankAccountType})}>
                    <SelectTrigger className="col-span-3">
                      <SelectValue placeholder="Select type" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value={BankAccountType.CHECKING}>Checking</SelectItem>
                      <SelectItem value={BankAccountType.SAVINGS}>Savings</SelectItem>
                      <SelectItem value={BankAccountType.CREDIT_CARD}>Credit Card</SelectItem>
                      <SelectItem value={BankAccountType.CASH}>Cash</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div className="grid grid-cols-4 items-center gap-4">
                  <Label htmlFor="balance" className="text-right">Balance</Label>
                  <Input
                    id="balance"
                    type="number"
                    placeholder="0.00"
                    value={newAccount.balance}
                    onChange={(e) => setNewAccount({...newAccount, balance: parseFloat(e.target.value) || 0})}
                    className="col-span-3"
                  />
                </div>
              </div>
              <DialogFooter>
                <Button onClick={handleCreateAccount}>Add Account</Button>
              </DialogFooter>
            </DialogContent>
          </Dialog>
        </div>
      </div>

      {/* Cash Position Summary */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Cash</CardTitle>
            <Banknote className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{formatCurrency(cashPosition?.totalCash || 0)}</div>
            <p className="text-xs text-muted-foreground">
              Across {bankAccounts.length} accounts
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Available Balance</CardTitle>
            <TrendingUp className="h-4 w-4 text-green-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">{formatCurrency(cashPosition?.totalCash || 0)}</div>
            <p className="text-xs text-muted-foreground">
              Ready for use
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">30-Day Forecast</CardTitle>
            <TrendingDown className="h-4 w-4 text-blue-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{formatCurrency(cashPosition?.totalCash || 0)}</div>
            <p className="text-xs text-muted-foreground">
              Projected balance
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Main Content Tabs */}
      <Tabs defaultValue="accounts" className="space-y-4">
        <TabsList>
          <TabsTrigger value="accounts">Bank Accounts</TabsTrigger>
          <TabsTrigger value="forecast">Cash Flow Forecast</TabsTrigger>
          <TabsTrigger value="reconciliation">Reconciliation</TabsTrigger>
        </TabsList>

        <TabsContent value="accounts" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Bank Accounts</CardTitle>
              <CardDescription>
                Manage your bank accounts and monitor balances
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Account Name</TableHead>
                    <TableHead>Bank</TableHead>
                    <TableHead>Account Number</TableHead>
                    <TableHead>Type</TableHead>
                    <TableHead className="text-right">Balance</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead>Last Reconciled</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {bankAccounts.map((account) => (
                    <TableRow key={account.id}>
                      <TableCell className="font-medium">{account.accountName}</TableCell>
                      <TableCell>{account.bankName}</TableCell>
                      <TableCell>****{account.accountNumber.slice(-4)}</TableCell>
                      <TableCell>
                        <Badge className={getAccountTypeColor(account.type)}>
                          <div className="flex items-center space-x-1">
                            {getAccountTypeIcon(account.type)}
                            <span>{account.type}</span>
                          </div>
                        </Badge>
                      </TableCell>
                      <TableCell className="text-right font-medium">
                        {formatCurrency(account.balance, account.currency)}
                      </TableCell>
                      <TableCell>
                        <Badge variant={account.isActive ? "default" : "secondary"}>
                          {account.isActive ? 'Active' : 'Inactive'}
                        </Badge>
                      </TableCell>
                      <TableCell>
                        {account.lastReconciled ? formatDate(account.lastReconciled) : 'Never'}
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="forecast" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Cash Flow Forecast</CardTitle>
              <CardDescription>
                30-day cash flow projection based on scheduled transactions
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="text-center py-8 text-muted-foreground">
                <AlertCircle className="h-12 w-12 mx-auto mb-4 opacity-50" />
                <p>Cash flow forecast will be displayed here</p>
                <p className="text-sm">Based on scheduled payments and receipts</p>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="reconciliation" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Bank Reconciliation History</CardTitle>
              <CardDescription>
                Track reconciliation status for all bank accounts
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="text-center py-8 text-muted-foreground">
                <AlertCircle className="h-12 w-12 mx-auto mb-4 opacity-50" />
                <p>Reconciliation history will be displayed here</p>
                <p className="text-sm">Start a new reconciliation to begin tracking</p>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default CashManagement;
