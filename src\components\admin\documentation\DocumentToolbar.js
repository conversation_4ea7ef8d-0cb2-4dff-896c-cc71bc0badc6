import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
import { Button } from '@/components/ui/button';
import { AddDocumentDialog } from './AddDocumentDialog';
import { AddCategoryDialog } from './AddCategoryDialog';
import { DocumentUpload } from './DocumentUpload';
import { Popover, PopoverContent, PopoverTrigger, } from "@/components/ui/popover";
import { Upload, RefreshCcw, FileText } from 'lucide-react';
import { Separator } from '@/components/ui/separator';
export const DocumentToolbar = ({ categories, onDocumentAdd, onCategoryAdd, onFileUpload, onRefresh, }) => {
    return (_jsxs("div", { className: "flex flex-wrap gap-2 justify-between items-center bg-white dark:bg-gray-800 p-3 rounded-lg shadow mb-4", children: [_jsxs("div", { className: "flex flex-wrap items-center gap-2", children: [_jsx(AddDocumentDialog, { categories: categories, onDocumentAdd: onDocumentAdd }), _jsx(AddCategoryDialog, { onCategoryAdd: onCategoryAdd }), _jsxs(Popover, { children: [_jsx(PopoverTrigger, { asChild: true, children: _jsxs(Button, { variant: "secondary", className: "flex items-center gap-2", children: [_jsx(Upload, { size: 16 }), "Upload Document"] }) }), _jsxs(PopoverContent, { className: "w-[350px] sm:w-[450px]", children: [_jsxs("div", { className: "font-medium flex items-center gap-2 mb-2", children: [_jsx(FileText, { size: 16 }), _jsx("h3", { children: "Upload Document File" })] }), _jsx(Separator, { className: "my-2" }), _jsx(DocumentUpload, { onFileUpload: onFileUpload, categories: categories })] })] })] }), _jsx(Button, { variant: "outline", size: "icon", onClick: onRefresh, title: "Refresh documents", children: _jsx(RefreshCcw, { size: 16 }) })] }));
};
