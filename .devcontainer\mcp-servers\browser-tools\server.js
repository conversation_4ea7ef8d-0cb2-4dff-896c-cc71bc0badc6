#!/usr/bin/env node

const { Server } = require('@modelcontextprotocol/sdk/server/index.js');
const { StdioServerTransport } = require('@modelcontextprotocol/sdk/server/stdio.js');
const { CallToolRequestSchema, ListToolsRequestSchema } = require('@modelcontextprotocol/sdk/types.js');
const express = require('express');

class BrowserToolsServer {
  constructor() {
    this.server = new Server(
      {
        name: 'browser-tools-server',
        version: '1.0.0',
      },
      {
        capabilities: {
          tools: {},
        },
      }
    );

    this.port = process.env.MCP_PORT || 8088;
    
    this.setupToolHandlers();
    this.setupHealthCheck();
  }

  setupToolHandlers() {
    this.server.setRequestHandler(ListToolsRequestSchema, async () => {
      return {
        tools: [
          {
            name: 'browser_navigate',
            description: 'Navigate to a URL and capture page information',
            inputSchema: {
              type: 'object',
              properties: {
                url: {
                  type: 'string',
                  description: 'URL to navigate to',
                },
                wait_for: {
                  type: 'string',
                  description: 'CSS selector to wait for',
                },
                timeout: {
                  type: 'number',
                  description: 'Timeout in milliseconds',
                  default: 30000,
                },
              },
              required: ['url'],
            },
          },
          {
            name: 'browser_screenshot',
            description: 'Take a screenshot of the current page',
            inputSchema: {
              type: 'object',
              properties: {
                url: {
                  type: 'string',
                  description: 'URL to screenshot',
                },
                full_page: {
                  type: 'boolean',
                  description: 'Capture full page',
                  default: false,
                },
                viewport: {
                  type: 'object',
                  description: 'Viewport dimensions',
                  properties: {
                    width: { type: 'number', default: 1280 },
                    height: { type: 'number', default: 720 },
                  },
                },
              },
              required: ['url'],
            },
          },
          {
            name: 'browser_extract_text',
            description: 'Extract text content from a page',
            inputSchema: {
              type: 'object',
              properties: {
                url: {
                  type: 'string',
                  description: 'URL to extract text from',
                },
                selector: {
                  type: 'string',
                  description: 'CSS selector to extract text from',
                },
              },
              required: ['url'],
            },
          },
          {
            name: 'browser_click',
            description: 'Click an element on the page',
            inputSchema: {
              type: 'object',
              properties: {
                url: {
                  type: 'string',
                  description: 'URL of the page',
                },
                selector: {
                  type: 'string',
                  description: 'CSS selector of element to click',
                },
              },
              required: ['url', 'selector'],
            },
          },
        ],
      };
    });

    this.server.setRequestHandler(CallToolRequestSchema, async (request) => {
      const { name, arguments: args } = request.params;

      switch (name) {
        case 'browser_navigate':
          return await this.handleNavigate(args);
        case 'browser_screenshot':
          return await this.handleScreenshot(args);
        case 'browser_extract_text':
          return await this.handleExtractText(args);
        case 'browser_click':
          return await this.handleClick(args);
        default:
          throw new Error(`Unknown tool: ${name}`);
      }
    });
  }

  async handleNavigate(args) {
    try {
      // Simulated browser navigation (would use Puppeteer in real implementation)
      const result = {
        url: args.url,
        title: 'Page Title',
        status: 'success',
        load_time: Math.random() * 2000 + 500,
        timestamp: new Date().toISOString(),
      };

      return {
        content: [
          {
            type: 'text',
            text: JSON.stringify(result, null, 2),
          },
        ],
      };
    } catch (error) {
      throw new Error(`Browser Navigate error: ${error.message}`);
    }
  }

  async handleScreenshot(args) {
    try {
      // Simulated screenshot (would use Puppeteer in real implementation)
      const result = {
        url: args.url,
        screenshot_path: `/data/screenshots/${Date.now()}.png`,
        full_page: args.full_page || false,
        viewport: args.viewport || { width: 1280, height: 720 },
        timestamp: new Date().toISOString(),
      };

      return {
        content: [
          {
            type: 'text',
            text: JSON.stringify(result, null, 2),
          },
        ],
      };
    } catch (error) {
      throw new Error(`Browser Screenshot error: ${error.message}`);
    }
  }

  async handleExtractText(args) {
    try {
      // Simulated text extraction (would use Puppeteer in real implementation)
      const result = {
        url: args.url,
        selector: args.selector || 'body',
        text: 'Extracted text content from the page...',
        length: 42,
        timestamp: new Date().toISOString(),
      };

      return {
        content: [
          {
            type: 'text',
            text: JSON.stringify(result, null, 2),
          },
        ],
      };
    } catch (error) {
      throw new Error(`Browser Extract Text error: ${error.message}`);
    }
  }

  async handleClick(args) {
    try {
      // Simulated click (would use Puppeteer in real implementation)
      const result = {
        url: args.url,
        selector: args.selector,
        action: 'click',
        success: true,
        timestamp: new Date().toISOString(),
      };

      return {
        content: [
          {
            type: 'text',
            text: JSON.stringify(result, null, 2),
          },
        ],
      };
    } catch (error) {
      throw new Error(`Browser Click error: ${error.message}`);
    }
  }

  setupHealthCheck() {
    const app = express();
    
    app.get('/health', (req, res) => {
      res.json({ 
        status: 'healthy', 
        service: 'browser-tools-mcp',
        timestamp: new Date().toISOString(),
        note: 'Simulated browser tools - would use Puppeteer in production'
      });
    });

    app.listen(this.port, () => {
      console.log(`Browser Tools MCP Server health check running on port ${this.port}`);
    });
  }

  async run() {
    const transport = new StdioServerTransport();
    await this.server.connect(transport);
    console.error('Browser Tools MCP server running on stdio');
  }
}

const server = new BrowserToolsServer();
server.run().catch(console.error);
