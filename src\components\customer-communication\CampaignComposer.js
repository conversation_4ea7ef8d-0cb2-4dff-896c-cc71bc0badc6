import { jsx as _jsx, jsxs as _jsxs, Fragment as _Fragment } from "react/jsx-runtime";
import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Tabs, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";
import { Mail, MessageSquare, Send, Calendar, Eye, Save, ArrowLeft, Clock, Target } from "lucide-react";
import { CustomerCommunicationService } from "@/services/customer-communication";
const CampaignComposer = ({ onBack, onSave, onSend, initialData }) => {
    const [campaignType, setCampaignType] = useState('email');
    const [campaignName, setCampaignName] = useState('');
    const [subject, setSubject] = useState('');
    const [content, setContent] = useState('');
    const [selectedTemplate, setSelectedTemplate] = useState('');
    const [selectedSegments, setSelectedSegments] = useState([]);
    const [scheduledAt, setScheduledAt] = useState('');
    const [isScheduled, setIsScheduled] = useState(false);
    const [emailTemplates, setEmailTemplates] = useState([]);
    const [smsTemplates, setSmsTemplates] = useState([]);
    const [segments, setSegments] = useState([]);
    const [previewMode, setPreviewMode] = useState(false);
    const [loading, setLoading] = useState(false);
    useEffect(() => {
        loadTemplatesAndSegments();
    }, []);
    useEffect(() => {
        if (initialData) {
            setCampaignName(initialData.name || '');
            setCampaignType(initialData.type || 'email');
            setSubject(initialData.subject || '');
            setContent(initialData.content || '');
            setSelectedSegments(initialData.targetSegments || []);
            setScheduledAt(initialData.scheduledAt || '');
            setIsScheduled(!!initialData.scheduledAt);
        }
    }, [initialData]);
    const loadTemplatesAndSegments = async () => {
        try {
            // Mock data - replace with actual API calls
            const mockEmailTemplates = [
                {
                    id: 'welcome-email',
                    name: 'Welcome Email',
                    subject: 'Welcome to {{company_name}}!',
                    content: `
            <h1>Welcome {{customer_name}}!</h1>
            <p>Thank you for joining {{company_name}}. We're excited to have you on board.</p>
            <p>Here's what you can expect:</p>
            <ul>
              <li>Exclusive offers and discounts</li>
              <li>Product updates and news</li>
              <li>Priority customer support</li>
            </ul>
            <p>Best regards,<br>The {{company_name}} Team</p>
          `,
                    type: 'transactional',
                    variables: ['customer_name', 'company_name'],
                    isActive: true,
                    createdAt: '2025-05-20T10:00:00Z',
                    updatedAt: '2025-05-20T10:00:00Z'
                },
                {
                    id: 'product-update',
                    name: 'Product Update',
                    subject: 'New Features Available - {{product_name}}',
                    content: `
            <h1>Exciting Updates to {{product_name}}!</h1>
            <p>Hi {{customer_name}},</p>
            <p>We've just released some amazing new features that we think you'll love:</p>
            <div>{{feature_list}}</div>
            <p><a href="{{update_link}}">Learn More</a></p>
          `,
                    type: 'marketing',
                    variables: ['customer_name', 'product_name', 'feature_list', 'update_link'],
                    isActive: true,
                    createdAt: '2025-05-22T14:00:00Z',
                    updatedAt: '2025-05-22T14:00:00Z'
                }
            ];
            const mockSmsTemplates = [
                {
                    id: 'welcome-sms',
                    name: 'Welcome SMS',
                    content: 'Welcome to {{company_name}}, {{customer_name}}! Your account is ready. Reply STOP to opt out.',
                    type: 'transactional',
                    variables: ['customer_name', 'company_name'],
                    isActive: true,
                    createdAt: '2025-05-20T10:00:00Z',
                    updatedAt: '2025-05-20T10:00:00Z'
                },
                {
                    id: 'flash-sale',
                    name: 'Flash Sale Alert',
                    content: '🔥 FLASH SALE: {{discount}}% off {{product_name}}! Use code {{promo_code}}. Valid until {{expiry}}. Shop now: {{link}}',
                    type: 'marketing',
                    variables: ['discount', 'product_name', 'promo_code', 'expiry', 'link'],
                    isActive: true,
                    createdAt: '2025-05-24T09:00:00Z',
                    updatedAt: '2025-05-24T09:00:00Z'
                }
            ];
            const mockSegments = [
                {
                    id: 'new-customers',
                    name: 'New Customers',
                    description: 'Customers who joined in the last 30 days',
                    criteria: [
                        { field: 'createdAt', operator: 'greater_than', value: '2025-04-26' }
                    ],
                    contactCount: 245,
                    createdAt: '2025-05-01T10:00:00Z',
                    updatedAt: '2025-05-01T10:00:00Z'
                },
                {
                    id: 'vip-customers',
                    name: 'VIP Customers',
                    description: 'High-value customers with premium status',
                    criteria: [
                        { field: 'segments', operator: 'in', value: ['vip', 'premium'] }
                    ],
                    contactCount: 156,
                    createdAt: '2025-05-01T10:00:00Z',
                    updatedAt: '2025-05-01T10:00:00Z'
                },
                {
                    id: 'active-customers',
                    name: 'Active Customers',
                    description: 'Customers with recent activity',
                    criteria: [
                        { field: 'lastPurchaseDate', operator: 'greater_than', value: '2025-04-01' }
                    ],
                    contactCount: 1823,
                    createdAt: '2025-05-01T10:00:00Z',
                    updatedAt: '2025-05-01T10:00:00Z'
                }
            ];
            setEmailTemplates(mockEmailTemplates);
            setSmsTemplates(mockSmsTemplates);
            setSegments(mockSegments);
        }
        catch (error) {
            console.error('Failed to load templates and segments:', error);
        }
    };
    const handleTemplateSelect = (templateId) => {
        setSelectedTemplate(templateId);
        if (campaignType === 'email') {
            const template = emailTemplates.find(t => t.id === templateId);
            if (template) {
                setSubject(template.subject);
                setContent(template.content);
            }
        }
        else {
            const template = smsTemplates.find(t => t.id === templateId);
            if (template) {
                setContent(template.content);
            }
        }
    };
    const handleSegmentToggle = (segmentId) => {
        setSelectedSegments(prev => prev.includes(segmentId)
            ? prev.filter(id => id !== segmentId)
            : [...prev, segmentId]);
    };
    const handleSave = async () => {
        if (!campaignName.trim()) {
            alert('Please enter a campaign name');
            return;
        }
        if (!content.trim()) {
            alert('Please enter campaign content');
            return;
        }
        if (selectedSegments.length === 0) {
            alert('Please select at least one target segment');
            return;
        }
        setLoading(true);
        try {
            const campaignData = {
                name: campaignName,
                type: campaignType,
                subject: campaignType === 'email' ? subject : undefined,
                content,
                templateId: selectedTemplate,
                targetSegments: selectedSegments,
                scheduledAt: isScheduled ? scheduledAt : undefined,
                status: 'draft'
            };
            if (campaignType === 'email') {
                await CustomerCommunicationService.createEmailCampaign(campaignName, selectedTemplate, selectedSegments, isScheduled ? scheduledAt : undefined);
            }
            else {
                await CustomerCommunicationService.createSMSCampaign(campaignName, selectedTemplate, selectedSegments, isScheduled ? scheduledAt : undefined);
            }
            onSave?.(campaignData);
        }
        catch (error) {
            console.error('Failed to save campaign:', error);
            alert('Failed to save campaign. Please try again.');
        }
        finally {
            setLoading(false);
        }
    };
    const handleSend = async () => {
        if (!campaignName.trim() || !content.trim() || selectedSegments.length === 0) {
            alert('Please complete all required fields before sending');
            return;
        }
        const confirmed = globalThis.confirm(`Are you sure you want to send this ${campaignType} campaign to ${selectedSegments.length} segment(s)?`);
        if (!confirmed)
            return;
        setLoading(true);
        try {
            // Mock sending logic
            const campaignData = {
                name: campaignName,
                type: campaignType,
                subject: campaignType === 'email' ? subject : undefined,
                content,
                templateId: selectedTemplate,
                targetSegments: selectedSegments,
                status: 'sending'
            };
            onSend?.(campaignData);
        }
        catch (error) {
            console.error('Failed to send campaign:', error);
            alert('Failed to send campaign. Please try again.');
        }
        finally {
            setLoading(false);
        }
    };
    const getPreviewContent = () => {
        const mockVariables = {
            customer_name: 'John Doe',
            company_name: 'Your Company',
            product_name: 'Premium Service',
            discount: '25',
            promo_code: 'SAVE25',
            expiry: 'midnight',
            link: 'https://example.com',
            feature_list: '• Enhanced dashboard\n• Real-time analytics\n• Mobile app',
            update_link: 'https://example.com/updates'
        };
        return CustomerCommunicationService.processTemplate(content, mockVariables);
    };
    const totalContacts = selectedSegments.reduce((total, segmentId) => {
        const segment = segments.find(s => s.id === segmentId);
        return total + (segment?.contactCount || 0);
    }, 0);
    return (_jsxs("div", { className: "space-y-6", children: [_jsxs("div", { className: "flex items-center justify-between", children: [_jsxs("div", { className: "flex items-center gap-4", children: [_jsxs(Button, { variant: "ghost", onClick: onBack, children: [_jsx(ArrowLeft, { className: "h-4 w-4 mr-2" }), "Back"] }), _jsxs("div", { children: [_jsx("h1", { className: "text-3xl font-bold tracking-tight", children: "Campaign Composer" }), _jsx("p", { className: "text-gray-600", children: "Create and send email or SMS campaigns" })] })] }), _jsxs("div", { className: "flex gap-2", children: [_jsxs(Button, { variant: "outline", onClick: () => setPreviewMode(!previewMode), children: [_jsx(Eye, { className: "h-4 w-4 mr-2" }), previewMode ? 'Edit' : 'Preview'] }), _jsxs(Button, { variant: "outline", onClick: handleSave, disabled: loading, children: [_jsx(Save, { className: "h-4 w-4 mr-2" }), "Save Draft"] }), _jsxs(Button, { onClick: handleSend, disabled: loading, children: [_jsx(Send, { className: "h-4 w-4 mr-2" }), "Send Campaign"] })] })] }), _jsxs("div", { className: "grid gap-6 lg:grid-cols-3", children: [_jsx("div", { className: "lg:col-span-2 space-y-6", children: previewMode ? (_jsxs(Card, { children: [_jsxs(CardHeader, { children: [_jsx(CardTitle, { children: "Campaign Preview" }), _jsx(CardDescription, { children: "How your campaign will appear to recipients" })] }), _jsxs(CardContent, { children: [campaignType === 'email' && subject && (_jsxs("div", { className: "mb-4", children: [_jsx(Label, { className: "text-sm font-medium", children: "Subject Line" }), _jsx("div", { className: "mt-1 p-3 bg-gray-50 rounded border", children: CustomerCommunicationService.processTemplate(subject, {
                                                        customer_name: 'John Doe',
                                                        company_name: 'Your Company',
                                                        product_name: 'Premium Service'
                                                    }) })] })), _jsxs("div", { children: [_jsx(Label, { className: "text-sm font-medium", children: "Content" }), _jsx("div", { className: "mt-1 p-4 bg-gray-50 rounded border min-h-[200px]", children: campaignType === 'email' ? (_jsx("div", { dangerouslySetInnerHTML: { __html: getPreviewContent() } })) : (_jsx("div", { className: "whitespace-pre-wrap", children: getPreviewContent() })) })] })] })] })) : (_jsxs(_Fragment, { children: [_jsxs(Card, { children: [_jsxs(CardHeader, { children: [_jsx(CardTitle, { children: "Campaign Details" }), _jsx(CardDescription, { children: "Configure your campaign settings" })] }), _jsxs(CardContent, { className: "space-y-4", children: [_jsxs("div", { children: [_jsx(Label, { htmlFor: "campaign-name", children: "Campaign Name" }), _jsx(Input, { id: "campaign-name", value: campaignName, onChange: (e) => setCampaignName(e.target.value), placeholder: "Enter campaign name" })] }), _jsxs("div", { children: [_jsx(Label, { children: "Campaign Type" }), _jsx(Tabs, { value: campaignType, onValueChange: (value) => setCampaignType(value), children: _jsxs(TabsList, { className: "grid w-full grid-cols-2", children: [_jsxs(TabsTrigger, { value: "email", className: "flex items-center gap-2", children: [_jsx(Mail, { className: "h-4 w-4" }), "Email"] }), _jsxs(TabsTrigger, { value: "sms", className: "flex items-center gap-2", children: [_jsx(MessageSquare, { className: "h-4 w-4" }), "SMS"] })] }) })] }), campaignType === 'email' && (_jsxs("div", { children: [_jsx(Label, { htmlFor: "subject", children: "Subject Line" }), _jsx(Input, { id: "subject", value: subject, onChange: (e) => setSubject(e.target.value), placeholder: "Enter email subject" })] }))] })] }), _jsxs(Card, { children: [_jsxs(CardHeader, { children: [_jsx(CardTitle, { children: "Template Selection" }), _jsx(CardDescription, { children: "Choose a template or create from scratch" })] }), _jsx(CardContent, { children: _jsxs("div", { className: "space-y-4", children: [_jsxs(Select, { value: selectedTemplate, onValueChange: handleTemplateSelect, children: [_jsx(SelectTrigger, { children: _jsx(SelectValue, { placeholder: "Select a template (optional)" }) }), _jsx(SelectContent, { children: (campaignType === 'email' ? emailTemplates : smsTemplates).map((template) => (_jsx(SelectItem, { value: template.id, children: template.name }, template.id))) })] }), _jsxs("div", { children: [_jsx(Label, { htmlFor: "content", children: "Content" }), _jsx(Textarea, { id: "content", value: content, onChange: (e) => setContent(e.target.value), placeholder: campaignType === 'email' ? 'Enter email content (HTML supported)' : 'Enter SMS content', rows: campaignType === 'email' ? 10 : 4, className: "font-mono" }), _jsx("p", { className: "text-xs text-gray-500 mt-1", children: "Use variables like {{customer_name}} for personalization" })] })] }) })] })] })) }), _jsxs("div", { className: "space-y-6", children: [_jsxs(Card, { children: [_jsxs(CardHeader, { children: [_jsxs(CardTitle, { className: "flex items-center gap-2", children: [_jsx(Target, { className: "h-5 w-5" }), "Target Audience"] }), _jsx(CardDescription, { children: "Select customer segments" })] }), _jsxs(CardContent, { children: [_jsx("div", { className: "space-y-3", children: segments.map((segment) => (_jsx("div", { className: `p-3 border rounded cursor-pointer transition-colors ${selectedSegments.includes(segment.id)
                                                        ? 'border-blue-500 bg-blue-50'
                                                        : 'border-gray-200 hover:border-gray-300'}`, onClick: () => handleSegmentToggle(segment.id), children: _jsxs("div", { className: "flex items-center justify-between", children: [_jsxs("div", { children: [_jsx("p", { className: "font-medium text-sm", children: segment.name }), _jsx("p", { className: "text-xs text-gray-600", children: segment.description })] }), _jsx(Badge, { variant: "secondary", children: segment.contactCount.toLocaleString() })] }) }, segment.id))) }), selectedSegments.length > 0 && (_jsx("div", { className: "mt-4 p-3 bg-blue-50 rounded", children: _jsxs("p", { className: "text-sm font-medium text-blue-900", children: ["Total Recipients: ", totalContacts.toLocaleString()] }) }))] })] }), _jsxs(Card, { children: [_jsxs(CardHeader, { children: [_jsxs(CardTitle, { className: "flex items-center gap-2", children: [_jsx(Calendar, { className: "h-5 w-5" }), "Scheduling"] }), _jsx(CardDescription, { children: "When to send this campaign" })] }), _jsxs(CardContent, { className: "space-y-4", children: [_jsxs("div", { className: "flex items-center space-x-2", children: [_jsx("input", { type: "checkbox", id: "schedule-later", checked: isScheduled, onChange: (e) => setIsScheduled(e.target.checked), className: "rounded" }), _jsx(Label, { htmlFor: "schedule-later", children: "Schedule for later" })] }), isScheduled && (_jsxs("div", { children: [_jsx(Label, { htmlFor: "scheduled-at", children: "Send Date & Time" }), _jsx(Input, { id: "scheduled-at", type: "datetime-local", value: scheduledAt, onChange: (e) => setScheduledAt(e.target.value), min: new Date().toISOString().slice(0, 16) })] })), !isScheduled && (_jsxs("div", { className: "flex items-center gap-2 text-sm text-gray-600", children: [_jsx(Clock, { className: "h-4 w-4" }), "Will send immediately"] }))] })] }), _jsxs(Card, { children: [_jsx(CardHeader, { children: _jsx(CardTitle, { children: "Campaign Summary" }) }), _jsxs(CardContent, { className: "space-y-3", children: [_jsxs("div", { className: "flex justify-between text-sm", children: [_jsx("span", { className: "text-gray-600", children: "Type:" }), _jsx("span", { className: "font-medium", children: campaignType.toUpperCase() })] }), _jsxs("div", { className: "flex justify-between text-sm", children: [_jsx("span", { className: "text-gray-600", children: "Recipients:" }), _jsx("span", { className: "font-medium", children: totalContacts.toLocaleString() })] }), _jsxs("div", { className: "flex justify-between text-sm", children: [_jsx("span", { className: "text-gray-600", children: "Segments:" }), _jsx("span", { className: "font-medium", children: selectedSegments.length })] }), _jsxs("div", { className: "flex justify-between text-sm", children: [_jsx("span", { className: "text-gray-600", children: "Status:" }), _jsx("span", { className: "font-medium", children: isScheduled ? 'Scheduled' : 'Ready to Send' })] })] })] })] })] })] }));
};
export default CampaignComposer;
