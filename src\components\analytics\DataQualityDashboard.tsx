import React, { useState, useEffect, useCallback } from 'react';

import { dataQualityService } from '../../services/analytics/dataQualityService';
import {
  DataQualityRule,
  DataQualityReport
} from '../../services/analytics/types';

import { AdvancedChart } from './AdvancedChart';

export const DataQualityDashboard: React.FC = (): JSX.Element => {
  const [qualityReport, setQualityReport] = useState<DataQualityReport | null>(null);
  const [rules, setRules] = useState<DataQualityRule[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedTable, setSelectedTable] = useState<string>('all');
  const [showRuleModal, setShowRuleModal] = useState(false);  const loadDashboardData = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);

      const [rulesResponse, reportsResponse] = await Promise.all([
        dataQualityService.getRules(),
        dataQualityService.getQualityReports()
      ]);

      setRules(rulesResponse.data);
      setQualityReport(reportsResponse.data[0] || null);
      setLoading(false);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load data quality dashboard');
      setLoading(false);
    }
  }, []);

  useEffect(() => {
    loadDashboardData();
  }, [loadDashboardData]);

  const runQualityCheck = async () => {
    try {
      setLoading(true);
      const report = await dataQualityService.runQualityCheck('all_tables');
      setQualityReport(report);
      setLoading(false);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to run quality check');
      setLoading(false);
    }
  };

  const getScoreColor = (score: number) => {
    if (score >= 90) return 'text-green-600';
    if (score >= 70) return 'text-yellow-600';
    return 'text-red-600';
  };

  const getScoreBgColor = (score: number) => {
    if (score >= 90) return 'bg-green-100';
    if (score >= 70) return 'bg-yellow-100';
    return 'bg-red-100';
  };

  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'critical': return 'text-red-600 bg-red-100';
      case 'high': return 'text-orange-600 bg-orange-100';
      case 'medium': return 'text-yellow-600 bg-yellow-100';
      case 'low': return 'text-blue-600 bg-blue-100';
      default: return 'text-gray-600 bg-gray-100';
    }
  };
  const renderQualityOverview = () => {
    if (!qualityReport) return null;

    return (
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <div className={`p-6 rounded-lg ${getScoreBgColor(qualityReport.overall_score)}`}>
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Overall Score</p>
              <p className={`text-3xl font-bold ${getScoreColor(qualityReport.overall_score)}`}>
                {qualityReport.overall_score.toFixed(1)}%
              </p>
            </div>
            <div className="text-2xl">📊</div>
          </div>
        </div>

        <div className="bg-white p-6 rounded-lg shadow-sm border">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Rules Evaluated</p>
              <p className="text-3xl font-bold text-blue-600">
                {qualityReport.rules_evaluated}
              </p>
            </div>
            <div className="text-2xl">📋</div>
          </div>
        </div>

        <div className="bg-white p-6 rounded-lg shadow-sm border">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Issues Found</p>
              <p className="text-3xl font-bold text-orange-600">
                {qualityReport.issues_found}
              </p>
            </div>
            <div className="text-2xl">⚠️</div>
          </div>
        </div>

        <div className="bg-white p-6 rounded-lg shadow-sm border">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Critical Issues</p>
              <p className="text-3xl font-bold text-red-600">
                {qualityReport.critical_issues}
              </p>
            </div>
            <div className="text-2xl">🚨</div>
          </div>
        </div>
      </div>
    );
  };

  const renderRulesTable = () => {
    return (
      <div className="bg-white rounded-lg shadow-sm border">
        <div className="px-6 py-4 border-b border-gray-200">
          <div className="flex justify-between items-center">
            <h3 className="text-lg font-medium text-gray-900">Data Quality Rules</h3>
            <button
              onClick={() => setShowRuleModal(true)}
              className="bg-blue-600 text-white px-4 py-2 rounded-md text-sm hover:bg-blue-700"
            >
              Add Rule
            </button>
          </div>
        </div>

        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Rule Name
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Type
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Field
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Severity
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Status
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {rules.map((rule) => (
                <tr key={rule.id} className="hover:bg-gray-50">
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm font-medium text-gray-900">{rule.name}</div>
                    <div className="text-sm text-gray-500">{rule.description}</div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                      {rule.type}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    {rule.field}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getSeverityColor(rule.severity)}`}>
                      {rule.severity}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                      rule.isActive ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'
                    }`}>
                      {rule.isActive ? 'Active' : 'Inactive'}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                    <button className="text-blue-600 hover:text-blue-900 mr-3">
                      Edit
                    </button>
                    <button className="text-red-600 hover:text-red-900">
                      Delete
                    </button>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
    );
  };

  const renderQualityTrends = () => {
    // Mock trend data - in real implementation, this would come from historical reports
    const trendData = [
      { x: new Date('2024-01-01'), y: 85 },
      { x: new Date('2024-01-15'), y: 87 },
      { x: new Date('2024-02-01'), y: 89 },
      { x: new Date('2024-02-15'), y: 91 },
      { x: new Date('2024-03-01'), y: 88 },
      { x: new Date('2024-03-15'), y: 92 },
    ];

    return (
      <div className="bg-white rounded-lg shadow-sm border p-6">
        <h3 className="text-lg font-medium text-gray-900 mb-4">Quality Score Trends</h3>
        <AdvancedChart
          type="line"
          data={trendData}
          config={{
            type: 'line',
            xAxis: { label: 'Date', type: 'time' },
            yAxis: { label: 'Quality Score (%)', type: 'value', min: 0, max: 100 },
            colors: ['#3b82f6']
          }}
          width={600}
          height={300}
          interactive
        />
      </div>
    );
  };

  const renderIssuesByType = () => {
    if (!qualityReport?.results) return null;

    const issuesByType = qualityReport.results.reduce((acc, result) => {
      result.issues.forEach(issue => {
        acc[issue.type] = (acc[issue.type] || 0) + 1;
      });
      return acc;
    }, {} as Record<string, number>);

    const chartData = Object.entries(issuesByType).map(([type, count]) => ({
      name: type,
      value: count
    }));

    return (
      <div className="bg-white rounded-lg shadow-sm border p-6">
        <h3 className="text-lg font-medium text-gray-900 mb-4">Issues by Type</h3>
        <AdvancedChart
          type="pie"
          data={chartData}
          config={{
            type: 'pie',
            colors: ['#ef4444', '#f59e0b', '#10b981', '#3b82f6', '#8b5cf6']
          }}
          width={400}
          height={300}
          interactive
        />
      </div>
    );
  };

  const renderRecentIssues = () => {
    if (!qualityReport?.results) return null;

    const allIssues = qualityReport.results.flatMap(result => 
      result.issues.map(issue => ({
        ...issue,
        ruleName: result.ruleName
      }))
    ).slice(0, 10); // Show only recent 10 issues

    return (
      <div className="bg-white rounded-lg shadow-sm border">
        <div className="px-6 py-4 border-b border-gray-200">
          <h3 className="text-lg font-medium text-gray-900">Recent Issues</h3>
        </div>
        <div className="divide-y divide-gray-200">
          {allIssues.map((issue, index) => (
            <div key={index} className="px-6 py-4">
              <div className="flex items-start justify-between">
                <div className="flex-1">
                  <div className="flex items-center">
                    <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getSeverityColor(issue.severity)} mr-2`}>
                      {issue.severity}
                    </span>
                    <h4 className="text-sm font-medium text-gray-900">{issue.type}</h4>
                  </div>
                  <p className="text-sm text-gray-600 mt-1">{issue.description}</p>
                  <div className="text-xs text-gray-500 mt-2">
                    Field: {issue.field} | Rule: {issue.ruleName}
                  </div>
                </div>
                <button className="text-blue-600 hover:text-blue-900 text-sm">
                  View Details
                </button>
              </div>
            </div>
          ))}
        </div>
      </div>
    );
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-red-50 border border-red-200 rounded-lg p-4">
        <div className="text-red-800">
          <h3 className="font-medium">Data Quality Dashboard Error</h3>
          <p className="text-sm mt-1">{error}</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Data Quality Dashboard</h1>
          <p className="text-gray-600">Monitor and manage data quality across your organization</p>
        </div>
        
        <div className="flex items-center space-x-4">
          <select
            className="border border-gray-300 rounded-md px-3 py-2 text-sm"
            title="Select table"
            aria-label="Select table for quality analysis"
            value={selectedTable}
            onChange={(e) => setSelectedTable(e.target.value)}
          >
            <option value="all">All Tables</option>
            <option value="users">Users</option>
            <option value="orders">Orders</option>
            <option value="products">Products</option>
          </select>

          <button
            onClick={runQualityCheck}
            className="bg-blue-600 text-white px-4 py-2 rounded-md text-sm hover:bg-blue-700"
          >
            Run Quality Check
          </button>
        </div>
      </div>

      {/* Quality Overview */}
      {renderQualityOverview()}

      {/* Charts Row */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {renderQualityTrends()}
        {renderIssuesByType()}
      </div>

      {/* Tables Row */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <div>{renderRulesTable()}</div>
        <div>{renderRecentIssues()}</div>
      </div>

      {/* Rule Modal - would be implemented as a separate component */}
      {showRuleModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 w-96">
            <h3 className="text-lg font-medium mb-4">Add Data Quality Rule</h3>
            <p className="text-gray-600">Rule creation form would go here...</p>
            <div className="flex justify-end space-x-3 mt-6">
              <button
                onClick={() => setShowRuleModal(false)}
                className="px-4 py-2 text-gray-700 border border-gray-300 rounded-md hover:bg-gray-50"
              >
                Cancel
              </button>
              <button
                onClick={() => setShowRuleModal(false)}
                className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
              >
                Create Rule
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default DataQualityDashboard;
