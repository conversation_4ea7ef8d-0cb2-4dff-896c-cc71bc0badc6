import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON>hart, Bar, Cell, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer } from 'recharts';
export const CategoryVariationChart = ({ data, title, description, className }) => {
    return (_jsxs(Card, { className: `backdrop-blur-md bg-white/30 border border-white/10 ${className}`, children: [_jsxs(CardHeader, { children: [_jsx(CardTitle, { children: title }), _jsx(CardDescription, { children: description })] }), _jsx(CardContent, { className: "h-80", children: _jsx(ResponsiveContainer, { width: "100%", height: "100%", children: _jsxs(Bar<PERSON><PERSON>, { data: data, margin: { top: 20, right: 30, left: 20, bottom: 5 }, children: [_jsx(Car<PERSON>ian<PERSON><PERSON>, { strokeDasharray: "3 3" }), _jsx(XAxis, { dataKey: "category" }), _jsx(YAxis, { label: { value: 'Variance %', angle: -90, position: 'insideLeft' } }), _jsx(Tooltip, { formatter: (value) => `${value}%` }), _jsx(Bar, { dataKey: "variance", fill: "#8884d8", children: data.map((entry, index) => (_jsx(Cell, { fill: entry.variance > 10 ? '#FF8042' : entry.variance > 5 ? '#FFBB28' : '#00C49F' }, `cell-${index}`))) })] }) }) })] }));
};
