# Quick Fix Checklist for Better Auth Issues

## Immediate Actions (Priority 1)

### 1. Verify Server Accessibility
```bash
# Test if the server is running and accessible
curl -I https://nxtdotx.co.za/health
curl -I https://nxtdotx.co.za/api/version

# Expected: 200 OK responses with proper headers
```

### 2. Test Authentication Endpoint
```bash
# Test the auth endpoint directly
curl -X POST https://nxtdotx.co.za/api/auth/sign-in \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"Admin123!@#"}'

# Expected: JSON response (not 404 or connection error)
```

### 3. Check Environment Variables
Verify these variables are set correctly in production:
- `BETTER_AUTH_URL=https://nxtdotx.co.za`
- `VITE_BETTER_AUTH_URL=https://nxtdotx.co.za/api/auth`
- `DATABASE_URL` (properly formatted PostgreSQL connection string)
- `BETTER_AUTH_SECRET` (minimum 32 characters)

## Configuration Fixes (Priority 2)

### 4. Fix Client Configuration
In `src/lib/better-auth-client.ts`:
```typescript
// Current (potentially problematic)
const authURL = import.meta.env.VITE_BETTER_AUTH_URL || 'https://nxtdotx.co.za/api/auth';

// Suggested fix - add more debugging
const authURL = import.meta.env.VITE_BETTER_AUTH_URL || 'https://nxtdotx.co.za/api/auth';
console.log('🔍 Better Auth Client Config:', {
  authURL,
  environment: import.meta.env.MODE,
  allEnvVars: import.meta.env
});
```

### 5. Verify Database Users
Check if test users exist in the database:
```sql
-- Connect to your PostgreSQL database and run:
SELECT id, email, "emailVerified", "createdAt" FROM "user" WHERE email IN (
  '<EMAIL>',
  '<EMAIL>',
  '<EMAIL>'
);

-- Check if they have password accounts:
SELECT u.email, a.password IS NOT NULL as has_password 
FROM "user" u 
LEFT JOIN "account" a ON u.id = a."userId" 
WHERE a."providerId" = 'credential';
```

### 6. Simplify Login Component
Temporarily simplify the login flow in `BetterAuthLogin.tsx`:
```typescript
const handleSubmit = async (e: React.FormEvent) => {
  e.preventDefault()
  setError('')
  setIsLoading(true)

  try {
    console.log('🔍 Attempting login with:', email)
    const result = await signIn(email, password)
    console.log('🔍 Login result:', result)

    if (result.error) {
      console.error('❌ Login error:', result.error)
      setError(result.error.message || 'Login failed')
    } else {
      console.log('✅ Login successful, navigating immediately...')
      // Navigate immediately instead of waiting for state change
      navigate(redirectTo)
    }
  } catch (err) {
    console.error('❌ Unexpected error:', err)
    setError('An unexpected error occurred')
  } finally {
    setIsLoading(false)
  }
}
```

## Database Fixes (Priority 3)

### 7. Create Test User Manually
If users don't exist, create them directly:
```sql
-- Insert test user
INSERT INTO "user" (id, email, "emailVerified", name, "createdAt", "updatedAt")
VALUES (
  'test-user-001',
  '<EMAIL>',
  true,
  'Dev Admin',
  NOW(),
  NOW()
) ON CONFLICT (email) DO NOTHING;

-- Insert password account (use proper hashing)
INSERT INTO "account" (
  id, "accountId", "providerId", "userId", 
  password, "createdAt", "updatedAt"
)
VALUES (
  'account-test-001',
  '<EMAIL>',
  'credential',
  'test-user-001',
  '$2b$10$example.hash.here', -- Replace with properly hashed password
  NOW(),
  NOW()
) ON CONFLICT (id) DO NOTHING;
```

## Server Fixes (Priority 4)

### 8. Add Debug Logging to Server
In `production-better-auth-server.mjs`, add more logging:
```javascript
app.use("/api/auth*", async (req, res) => {
  console.log('🔍 Auth request received:', {
    method: req.method,
    url: req.url,
    headers: req.headers,
    body: req.body,
    timestamp: new Date().toISOString()
  });
  
  // ... existing auth handler code
});
```

### 9. Test Custom Password Functions
Verify the custom password verification works:
```javascript
// Add this test endpoint temporarily
app.get('/test-password', async (req, res) => {
  try {
    const testPassword = 'Admin123!@#';
    const hashedPassword = await customHashPassword(testPassword);
    const isValid = await customVerifyPassword(testPassword, hashedPassword);
    
    res.json({
      success: true,
      hashedPassword,
      isValid,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});
```

## Deployment Verification

### 10. Check Production Deployment
Ensure the server is properly deployed and running:
- Verify the server process is running on the production server
- Check that port 3000 is accessible (or properly proxied)
- Verify SSL certificates are valid for HTTPS
- Check that environment variables are loaded correctly

### 11. CORS Configuration
Verify CORS is properly configured for your domain:
```javascript
// In the server, ensure this matches your actual domain
trustedOrigins: [
  'https://nxtdotx.co.za',
  'https://www.nxtdotx.co.za',
  // Add any other domains you're using
]
```

## Testing Commands

Run these tests in order to verify fixes:

```bash
# 1. Basic connectivity
curl -v https://nxtdotx.co.za/health

# 2. Auth endpoint availability  
curl -v https://nxtdotx.co.za/api/auth/session

# 3. Login attempt
curl -X POST https://nxtdotx.co.za/api/auth/sign-in \
  -H "Content-Type: application/json" \
  -H "Accept: application/json" \
  -d '{"email":"<EMAIL>","password":"Admin123!@#"}' \
  -v

# 4. Check response headers for session cookies
```

## Expected Results

After implementing these fixes, you should see:
1. ✅ Server health check returns 200 OK
2. ✅ Auth endpoints return proper responses (not 404)
3. ✅ Login attempts return authentication responses (not network errors)
4. ✅ Successful login creates session and redirects to dashboard
5. ✅ User can access protected routes after login

If any step fails, focus on that specific area before proceeding to the next step.

