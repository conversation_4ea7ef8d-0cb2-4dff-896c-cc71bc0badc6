#!/usr/bin/env node
process.env.SUPABASE_PROJECT_ID = 'utxxvdztmbbjcwdkqxcc';
process.env.SUPABASE_ACCESS_TOKEN = '********************************************';
const express = require('express');
const { spawn } = require('child_process');

class SupabaseMcpWrapper {
  constructor() {
    this.port = process.env.MCP_PORT || 8087;
    this.setupHealthCheck();
    this.startSupabaseServer();
  }

  startSupabaseServer() {
    const args = [
      '@supabase/mcp-server-supabase@latest',
      '--access-token', process.env.SUPABASE_ACCESS_TOKEN || '',
      '--project-id', process.env.SUPABASE_PROJECT_ID || ''
    ].filter(arg => arg !== '');
    console.log('Supabase server args:', args);

    this.supabaseProcess = spawn('./node_modules/.bin/npx.cmd', args, {
      stdio: ['pipe', 'pipe', 'pipe']
    });

    this.supabaseProcess.stdout.on('data', (data) => {
      console.log(`Supabase MCP: ${data}`);
    });

    this.supabaseProcess.stderr.on('data', (data) => {
      console.error(`Supabase MCP Error: ${data}`);
    });
  }

  setupHealthCheck() {
    const app = express();
    app.get('/health', (req, res) => {
      res.json({ 
        status: 'healthy', 
        service: 'supabase-mcp',
        timestamp: new Date().toISOString(),
        access_token_configured: !!process.env.SUPABASE_ACCESS_TOKEN
      });
    });
    app.listen(this.port, () => console.log(`Supabase MCP Server health check running on port ${this.port}`));
  }
}

new SupabaseMcpWrapper();
