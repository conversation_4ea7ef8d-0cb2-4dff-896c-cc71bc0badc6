const fetch = require('node-fetch');
const https = require('https');

// Create agent that accepts self-signed certificates for local development
const agent = new https.Agent({
  rejectUnauthorized: false
});

// Test the local development auth server directly
const BASE_URL = 'https://nxtdotx.co.za/api/auth';

const ADMIN_EMAIL = "<EMAIL>";
const FIRST_NAME = "Admin";
const LAST_NAME = "Two";
const PASSWORD = "MySecurePassword123";

async function testBetterAuthDirect() {
  try {
    console.log("Testing Better Auth directly on local dev server...");
    console.log("Base URL:", BASE_URL);

    // 1. Test health/version endpoint first
    console.log("\n1. Testing server connectivity...");
    try {
      const healthResp = await fetch(`https://nxtdotx.co.za/health`, {
        method: "GET",
        agent: agent,
      });
      
      if (healthResp.ok) {
        const healthData = await healthResp.text();
        console.log("✅ Server is running:", healthData);
      } else {
        console.log("❌ Health check failed:", healthResp.status, healthResp.statusText);
      }
    } catch (healthError) {
      console.log("❌ Server connectivity test failed:", healthError.message);
    }

    // 2. Register admin user
    console.log("\n2. Registering admin user...");
    const registerResp = await fetch(`${BASE_URL}/sign-up/email`, {
      method: "POST",
      headers: { "Content-Type": "application/json" },
      agent: agent,
      body: JSON.stringify({
        email: ADMIN_EMAIL,
        password: PASSWORD,
        firstName: FIRST_NAME,
        lastName: LAST_NAME
      })
    });

    console.log("Registration response status:", registerResp.status);
    const registerText = await registerResp.text();
    console.log("Registration response:", registerText);

    if (!registerResp.ok) {
      if (!registerText.includes("already exists")) {
        throw new Error(`Failed to register: ${registerResp.statusText} - ${registerText}`);
      } else {
        console.log("✅ User already exists, proceeding to login...");
      }
    } else {
      console.log("✅ Admin user registered successfully");
    }

    // 3. Login to confirm success
    console.log("\n3. Testing login...");
    const loginResp = await fetch(`${BASE_URL}/sign-in/email`, {
      method: "POST",
      headers: { "Content-Type": "application/json" },
      agent: agent,
      body: JSON.stringify({
        email: ADMIN_EMAIL,
        password: PASSWORD,
      })
    });

    console.log("Login response status:", loginResp.status);
    const loginText = await loginResp.text();
    console.log("Login response:", loginText);

    if (!loginResp.ok) {
        throw new Error(`Failed to login: ${loginResp.statusText} - ${loginText}`);
    }

    const loginData = JSON.parse(loginText);
    console.log("✅ Admin login successful. Login result:", loginData);

    console.log("\n✅ Better Auth direct test complete!");
    console.log("Email:", ADMIN_EMAIL);
    console.log("Password:", PASSWORD);
  } catch (err) {
    console.error("❌ Better Auth direct test failed:", err.message || err);
    process.exit(1);
  }
}

testBetterAuthDirect();