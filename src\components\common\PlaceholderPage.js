import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
import { useLocation } from 'react-router-dom';
import { <PERSON><PERSON><PERSON>riangle, ArrowLeft, Settings } from 'lucide-react';
const PlaceholderPage = ({ title, description, icon: Icon = Settings }) => {
    const location = useLocation();
    // Extract module name from path if title not provided
    const moduleName = title || location.pathname
        .split('/')
        .filter(Boolean)
        .map(segment => segment.split('-')
        .map(word => word.charAt(0).toUpperCase() + word.slice(1))
        .join(' '))
        .join(' > ');
    return (_jsx("div", { className: "min-h-screen bg-gray-50 dark:bg-gray-900", children: _jsxs("div", { className: "max-w-4xl mx-auto px-4 py-8", children: [_jsxs("div", { className: "mb-8", children: [_jsxs("button", { onClick: () => globalThis.history.back(), className: "flex items-center text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-gray-100 mb-4", children: [_jsx(ArrowLeft, { className: "w-4 h-4 mr-2" }), "Back"] }), _jsxs("div", { className: "flex items-center mb-4", children: [_jsx(Icon, { className: "w-8 h-8 text-blue-600 dark:text-blue-400 mr-3" }), _jsx("h1", { className: "text-3xl font-bold text-gray-900 dark:text-gray-100", children: moduleName })] }), description && (_jsx("p", { className: "text-gray-600 dark:text-gray-400 text-lg", children: description }))] }), _jsx("div", { className: "bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-8", children: _jsxs("div", { className: "text-center", children: [_jsx("div", { className: "mx-auto flex items-center justify-center h-16 w-16 rounded-full bg-yellow-100 dark:bg-yellow-900/20 mb-6", children: _jsx(AlertTriangle, { className: "h-8 w-8 text-yellow-600 dark:text-yellow-400" }) }), _jsx("h2", { className: "text-2xl font-semibold text-gray-900 dark:text-gray-100 mb-4", children: "Module Under Development" }), _jsx("p", { className: "text-gray-600 dark:text-gray-400 mb-6 max-w-md mx-auto", children: "This module is currently being developed and will be available in a future release. Please check back later for updates." }), _jsxs("div", { className: "bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4 mb-6", children: [_jsx("h3", { className: "text-sm font-medium text-blue-900 dark:text-blue-100 mb-2", children: "Current Path" }), _jsx("code", { className: "text-sm text-blue-700 dark:text-blue-300 bg-blue-100 dark:bg-blue-900/40 px-2 py-1 rounded", children: location.pathname })] }), _jsxs("div", { className: "flex justify-center space-x-4", children: [_jsx("button", { onClick: () => globalThis.location.href = '/', className: "bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-lg transition-colors", children: "Return to Dashboard" }), _jsx("button", { onClick: () => globalThis.history.back(), className: "bg-gray-200 dark:bg-gray-700 hover:bg-gray-300 dark:hover:bg-gray-600 text-gray-900 dark:text-gray-100 px-6 py-2 rounded-lg transition-colors", children: "Go Back" })] })] }) }), _jsxs("div", { className: "mt-8 grid grid-cols-1 md:grid-cols-3 gap-6", children: [_jsxs("div", { className: "bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6", children: [_jsx("h3", { className: "text-lg font-semibold text-gray-900 dark:text-gray-100 mb-2", children: "Development Status" }), _jsx("p", { className: "text-gray-600 dark:text-gray-400 text-sm", children: "This module is in the planning phase and will be implemented based on user requirements and priority." })] }), _jsxs("div", { className: "bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6", children: [_jsx("h3", { className: "text-lg font-semibold text-gray-900 dark:text-gray-100 mb-2", children: "Expected Features" }), _jsx("p", { className: "text-gray-600 dark:text-gray-400 text-sm", children: "Full CRUD operations, dashboard analytics, reporting capabilities, and integration with other platform modules." })] }), _jsxs("div", { className: "bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6", children: [_jsx("h3", { className: "text-lg font-semibold text-gray-900 dark:text-gray-100 mb-2", children: "Need This Module?" }), _jsx("p", { className: "text-gray-600 dark:text-gray-400 text-sm", children: "Contact the development team to prioritize this module or request specific features for your use case." })] })] })] }) }));
};
export default PlaceholderPage;
