version: '3.8'

services:
  app:
    build:
      context: .
      dockerfile: Dockerfile.dev
    volumes:
      - ../..:/workspace:cached
      - mcp-data:/mcp-data
    command: sleep infinity
    environment:
      - NODE_ENV=development
    networks:
      - mcp-network
    depends_on:
      - mcp-brave-search
      - mcp-tavily
      - mcp-firecrawl
      - mcp-context7
      - mcp-notion
      - mcp-desktop-commander
      - mcp-taskmaster
      - mcp-supabase
      - mcp-browser-tools
      - mcp-magic
      - mcp-neo4j

  mcp-brave-search:
    build:
      context: ./mcp-servers/brave-search
      dockerfile: Dockerfile
    container_name: mcp-brave-search
    ports:
      - "8080:8080"
    environment:
      - BRAVE_API_KEY=${BRAVE_API_KEY}
      - MCP_PORT=8080
    volumes:
      - mcp-data:/data
    networks:
      - mcp-network
    restart: unless-stopped

  mcp-tavily:
    build:
      context: ./mcp-servers/tavily
      dockerfile: Dockerfile
    container_name: mcp-tavily
    ports:
      - "8081:8081"
    environment:
      - TAVILY_API_KEY=${TAVILY_API_KEY}
      - MCP_PORT=8081
    volumes:
      - mcp-data:/data
    networks:
      - mcp-network
    restart: unless-stopped

  mcp-firecrawl:
    build:
      context: ./mcp-servers/firecrawl
      dockerfile: Dockerfile
    container_name: mcp-firecrawl
    ports:
      - "8082:8082"
    environment:
      - FIRECRAWL_API_KEY=${FIRECRAWL_API_KEY}
      - MCP_PORT=8082
    volumes:
      - mcp-data:/data
    networks:
      - mcp-network
    restart: unless-stopped

  mcp-context7:
    build:
      context: ./mcp-servers/context7
      dockerfile: Dockerfile
    container_name: mcp-context7
    ports:
      - "8083:8083"
    environment:
      - UPSTASH_REDIS_REST_URL=${UPSTASH_REDIS_REST_URL}
      - UPSTASH_REDIS_REST_TOKEN=${UPSTASH_REDIS_REST_TOKEN}
      - MCP_PORT=8083
    volumes:
      - mcp-data:/data
    networks:
      - mcp-network
    restart: unless-stopped

  mcp-notion:
    build:
      context: ./mcp-servers/notion
      dockerfile: Dockerfile
    container_name: mcp-notion
    ports:
      - "8084:8084"
    environment:
      - NOTION_API_KEY=${NOTION_API_KEY}
      - MCP_PORT=8084
    volumes:
      - mcp-data:/data
    networks:
      - mcp-network
    restart: unless-stopped

  mcp-desktop-commander:
    build:
      context: ./mcp-servers/desktop-commander
      dockerfile: Dockerfile
    container_name: mcp-desktop-commander
    ports:
      - "8085:8085"
    environment:
      - MCP_PORT=8085
    volumes:
      - mcp-data:/data
      - /var/run/docker.sock:/var/run/docker.sock
    networks:
      - mcp-network
    restart: unless-stopped

  mcp-taskmaster:
    build:
      context: ./mcp-servers/taskmaster
      dockerfile: Dockerfile
    container_name: mcp-taskmaster
    ports:
      - "8086:8086"
    environment:
      - MCP_PORT=8086
    volumes:
      - mcp-data:/data
    networks:
      - mcp-network
    restart: unless-stopped

  mcp-supabase:
    build:
      context: ./mcp-servers/supabase
      dockerfile: Dockerfile
    container_name: mcp-supabase
    ports:
      - "8087:8087"
    environment:
      - SUPABASE_ACCESS_TOKEN=${SUPABASE_ACCESS_TOKEN}
      - SUPABASE_PROJECT_ID=${SUPABASE_PROJECT_ID}
      - MCP_PORT=8087
    volumes:
      - mcp-data:/data
    networks:
      - mcp-network
    restart: unless-stopped

  mcp-browser-tools:
    build:
      context: ./mcp-servers/browser-tools
      dockerfile: Dockerfile
    container_name: mcp-browser-tools
    ports:
      - "8088:8088"
    environment:
      - MCP_PORT=8088
    volumes:
      - mcp-data:/data
    networks:
      - mcp-network
    restart: unless-stopped

  mcp-magic:
    build:
      context: ./mcp-servers/magic
      dockerfile: Dockerfile
    container_name: mcp-magic
    ports:
      - "8089:8089"
    environment:
      - MCP_PORT=8089
    volumes:
      - mcp-data:/data
    networks:
      - mcp-network
    restart: unless-stopped

  mcp-neo4j:
    build:
      context: ./mcp-servers/neo4j
      dockerfile: Dockerfile
    container_name: mcp-neo4j
    ports:
      - "8090:8090"
    environment:
      - NEO4J_URI=${NEO4J_URI}
      - NEO4J_USERNAME=${NEO4J_USERNAME}
      - NEO4J_PASSWORD=${NEO4J_PASSWORD}
      - MCP_PORT=8090
    volumes:
      - mcp-data:/data
    networks:
      - mcp-network
    restart: unless-stopped

volumes:
  mcp-data:

networks:
  mcp-network:
    driver: bridge
