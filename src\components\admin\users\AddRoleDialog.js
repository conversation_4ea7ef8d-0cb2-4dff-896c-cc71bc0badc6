import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage, } from '@/components/ui/form';
import { Checkbox } from '@/components/ui/checkbox';
import { PlusCircle } from 'lucide-react';
import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import * as z from 'zod';
// Enhanced form schema with validation
const roleFormSchema = z.object({
    name: z
        .string()
        .min(3, { message: "Role name must be at least 3 characters" })
        .max(50, { message: "Role name must be less than 50 characters" })
        .regex(/^[a-zA-Z0-9_\- ]+$/, {
        message: "Role name can only contain letters, numbers, spaces, underscores and hyphens"
    }),
    description: z
        .string()
        .max(200, { message: "Description must be less than 200 characters" })
        .optional(),
    permissions: z
        .array(z.string())
        .nonempty({ message: "Select at least one permission" }),
});
const AddRoleDialog = ({ open, onOpenChange, onAddRole, permissions }) => {
    const roleForm = useForm({
        resolver: zodResolver(roleFormSchema),
        defaultValues: {
            name: '',
            description: '',
            permissions: [],
        },
    });
    const handleAddRole = (data) => {
        const newRole = {
            id: Math.random().toString(36).substring(2, 11),
            name: data.name,
            description: data.description || '',
            permissions: data.permissions
        };
        onAddRole(newRole);
        roleForm.reset();
    };
    return (_jsxs(Dialog, { open: open, onOpenChange: onOpenChange, children: [_jsx(DialogTrigger, { asChild: true, children: _jsxs(Button, { variant: "outline", children: [_jsx(PlusCircle, { className: "mr-2 h-4 w-4" }), "New Role"] }) }), _jsxs(DialogContent, { className: "sm:max-w-[600px]", children: [_jsxs(DialogHeader, { children: [_jsx(DialogTitle, { children: "Add New Role" }), _jsx(DialogDescription, { children: "Create a new role with specific permissions." })] }), _jsx(Form, { ...roleForm, children: _jsxs("form", { onSubmit: roleForm.handleSubmit(handleAddRole), className: "space-y-4 py-4", children: [_jsx(FormField, { control: roleForm.control, name: "name", render: ({ field }) => (_jsxs(FormItem, { children: [_jsx(FormLabel, { children: "Role Name" }), _jsx(FormControl, { children: _jsx(Input, { placeholder: "Enter role name", ...field }) }), _jsx(FormMessage, {})] })) }), _jsx(FormField, { control: roleForm.control, name: "description", render: ({ field }) => (_jsxs(FormItem, { children: [_jsx(FormLabel, { children: "Description" }), _jsx(FormControl, { children: _jsx(Input, { placeholder: "Enter description", ...field }) }), _jsx(FormMessage, {})] })) }), _jsx(FormField, { control: roleForm.control, name: "permissions", render: () => (_jsxs(FormItem, { children: [_jsx("div", { className: "mb-4", children: _jsx(FormLabel, { className: "text-base", children: "Permissions" }) }), _jsx("div", { className: "grid grid-cols-2 gap-2", children: permissions.map((permission) => (_jsx(FormField, { control: roleForm.control, name: "permissions", render: ({ field }) => {
                                                        return (_jsxs(FormItem, { className: "flex flex-row items-start space-x-3 space-y-0", children: [_jsx(FormControl, { children: _jsx(Checkbox, { checked: field.value?.includes(permission.id), onCheckedChange: (checked) => {
                                                                            const currentPermissions = field.value || [];
                                                                            const newPermissions = checked
                                                                                ? [...currentPermissions, permission.id]
                                                                                : currentPermissions.filter((p) => p !== permission.id);
                                                                            field.onChange(newPermissions);
                                                                        } }) }), _jsxs(FormLabel, { className: "font-normal", children: [permission.name, _jsxs("span", { className: "text-xs text-muted-foreground ml-1", children: ["(", permission.category, ")"] })] })] }, permission.id));
                                                    } }, permission.id))) }), _jsx(FormMessage, {})] })) }), _jsx(DialogFooter, { children: _jsx(Button, { type: "submit", children: "Create Role" }) })] }) })] })] }));
};
export default AddRoleDialog;
