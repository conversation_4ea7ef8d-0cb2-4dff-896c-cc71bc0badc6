{"name": "supabase-mcp-server", "version": "1.0.0", "lockfileVersion": 3, "requires": true, "packages": {"": {"name": "supabase-mcp-server", "version": "1.0.0", "license": "MIT", "dependencies": {"@modelcontextprotocol/sdk": "^1.0.0", "@supabase/mcp-server-supabase": "^0.4.1", "express": "^4.18.0", "npx": "^10.2.2"}}, "node_modules/@deno/eszip": {"version": "0.84.0", "resolved": "https://registry.npmjs.org/@deno/eszip/-/eszip-0.84.0.tgz", "integrity": "sha512-kfTiJ3jYWy57gV/jjd2McRZdfn2dXHxR3UKL6HQksLAMEmRILHo+pZmN1PAjj8UxQiTBQbybsNHGLaqgHeVntQ==", "license": "MIT", "dependencies": {"@deno/shim-deno": "~0.18.0", "undici": "^6.0.0"}}, "node_modules/@deno/shim-deno": {"version": "0.18.2", "resolved": "https://registry.npmjs.org/@deno/shim-deno/-/shim-deno-0.18.2.tgz", "integrity": "sha512-oQ0CVmOio63wlhwQF75zA4ioolPvOwAoK0yuzcS5bDC1JUvH3y1GS8xPh8EOpcoDQRU4FTG8OQfxhpR+c6DrzA==", "license": "MIT", "dependencies": {"@deno/shim-deno-test": "^0.5.0", "which": "^4.0.0"}}, "node_modules/@deno/shim-deno-test": {"version": "0.5.0", "resolved": "https://registry.npmjs.org/@deno/shim-deno-test/-/shim-deno-test-0.5.0.tgz", "integrity": "sha512-4nMhecpGlPi0cSzT67L+Tm+GOJqvuk8gqHBziqcUQOarnuIax1z96/gJHCSIz2Z0zhxE6Rzwb3IZXPtFh51j+w==", "license": "MIT"}, "node_modules/@modelcontextprotocol/sdk": {"version": "1.13.0", "resolved": "https://registry.npmjs.org/@modelcontextprotocol/sdk/-/sdk-1.13.0.tgz", "integrity": "sha512-P5FZsXU0kY881F6Hbk9GhsYx02/KgWK1DYf7/tyE/1lcFKhDYPQR9iYjhQXJn+Sg6hQleMo3DB7h7+p4wgp2Lw==", "license": "MIT", "dependencies": {"ajv": "^6.12.6", "content-type": "^1.0.5", "cors": "^2.8.5", "cross-spawn": "^7.0.5", "eventsource": "^3.0.2", "express": "^5.0.1", "express-rate-limit": "^7.5.0", "pkce-challenge": "^5.0.0", "raw-body": "^3.0.0", "zod": "^3.23.8", "zod-to-json-schema": "^3.24.1"}, "engines": {"node": ">=18"}}, "node_modules/@modelcontextprotocol/sdk/node_modules/accepts": {"version": "2.0.0", "resolved": "https://registry.npmjs.org/accepts/-/accepts-2.0.0.tgz", "integrity": "sha512-5cvg6CtKwfgdmVqY1WIiXKc3Q1bkRqGLi+2W/6ao+6Y7gu/RCwRuAhGEzh5B4KlszSuTLgZYuqFqo5bImjNKng==", "license": "MIT", "dependencies": {"mime-types": "^3.0.0", "negotiator": "^1.0.0"}, "engines": {"node": ">= 0.6"}}, "node_modules/@modelcontextprotocol/sdk/node_modules/body-parser": {"version": "2.2.0", "resolved": "https://registry.npmjs.org/body-parser/-/body-parser-2.2.0.tgz", "integrity": "sha512-02qvAaxv8tp7fBa/mw1ga98OGm+eCbqzJOKoRt70sLmfEEi+jyBYVTDGfCL/k06/4EMk/z01gCe7HoCH/f2LTg==", "license": "MIT", "dependencies": {"bytes": "^3.1.2", "content-type": "^1.0.5", "debug": "^4.4.0", "http-errors": "^2.0.0", "iconv-lite": "^0.6.3", "on-finished": "^2.4.1", "qs": "^6.14.0", "raw-body": "^3.0.0", "type-is": "^2.0.0"}, "engines": {"node": ">=18"}}, "node_modules/@modelcontextprotocol/sdk/node_modules/content-disposition": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/content-disposition/-/content-disposition-1.0.0.tgz", "integrity": "sha512-Au9nRL8VNUut/XSzbQA38+M78dzP4D+eqg3gfJHMIHHYa3bg067xj1KxMUWj+VULbiZMowKngFFbKczUrNJ1mg==", "license": "MIT", "dependencies": {"safe-buffer": "5.2.1"}, "engines": {"node": ">= 0.6"}}, "node_modules/@modelcontextprotocol/sdk/node_modules/cookie-signature": {"version": "1.2.2", "resolved": "https://registry.npmjs.org/cookie-signature/-/cookie-signature-1.2.2.tgz", "integrity": "sha512-D76uU73ulSXrD1UXF4KE2TMxVVwhsnCgfAyTg9k8P6KGZjlXKrOLe4dJQKI3Bxi5wjesZoFXJWElNWBjPZMbhg==", "license": "MIT", "engines": {"node": ">=6.6.0"}}, "node_modules/@modelcontextprotocol/sdk/node_modules/debug": {"version": "4.4.1", "resolved": "https://registry.npmjs.org/debug/-/debug-4.4.1.tgz", "integrity": "sha512-KcKCqiftBJcZr++7ykoDIEwSa3XWowTfNPo92BYxjXiyYEVrUQh2aLyhxBCwww+heortUFxEJYcRzosstTEBYQ==", "license": "MIT", "dependencies": {"ms": "^2.1.3"}, "engines": {"node": ">=6.0"}, "peerDependenciesMeta": {"supports-color": {"optional": true}}}, "node_modules/@modelcontextprotocol/sdk/node_modules/express": {"version": "5.1.0", "resolved": "https://registry.npmjs.org/express/-/express-5.1.0.tgz", "integrity": "sha512-DT9ck5YIRU+8GYzzU5kT3eHGA5iL+1Zd0EutOmTE9Dtk+Tvuzd23VBU+ec7HPNSTxXYO55gPV/hq4pSBJDjFpA==", "license": "MIT", "dependencies": {"accepts": "^2.0.0", "body-parser": "^2.2.0", "content-disposition": "^1.0.0", "content-type": "^1.0.5", "cookie": "^0.7.1", "cookie-signature": "^1.2.1", "debug": "^4.4.0", "encodeurl": "^2.0.0", "escape-html": "^1.0.3", "etag": "^1.8.1", "finalhandler": "^2.1.0", "fresh": "^2.0.0", "http-errors": "^2.0.0", "merge-descriptors": "^2.0.0", "mime-types": "^3.0.0", "on-finished": "^2.4.1", "once": "^1.4.0", "parseurl": "^1.3.3", "proxy-addr": "^2.0.7", "qs": "^6.14.0", "range-parser": "^1.2.1", "router": "^2.2.0", "send": "^1.1.0", "serve-static": "^2.2.0", "statuses": "^2.0.1", "type-is": "^2.0.1", "vary": "^1.1.2"}, "engines": {"node": ">= 18"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/express"}}, "node_modules/@modelcontextprotocol/sdk/node_modules/finalhandler": {"version": "2.1.0", "resolved": "https://registry.npmjs.org/finalhandler/-/finalhandler-2.1.0.tgz", "integrity": "sha512-/t88Ty3d5JWQbWYgaOGCCYfXRwV1+be02WqYYlL6h0lEiUAMPM8o8qKGO01YIkOHzka2up08wvgYD0mDiI+q3Q==", "license": "MIT", "dependencies": {"debug": "^4.4.0", "encodeurl": "^2.0.0", "escape-html": "^1.0.3", "on-finished": "^2.4.1", "parseurl": "^1.3.3", "statuses": "^2.0.1"}, "engines": {"node": ">= 0.8"}}, "node_modules/@modelcontextprotocol/sdk/node_modules/fresh": {"version": "2.0.0", "resolved": "https://registry.npmjs.org/fresh/-/fresh-2.0.0.tgz", "integrity": "sha512-Rx/WycZ60HOaqLKAi6cHRKKI7zxWbJ31MhntmtwMoaTeF7XFH9hhBp8vITaMidfljRQ6eYWCKkaTK+ykVJHP2A==", "license": "MIT", "engines": {"node": ">= 0.8"}}, "node_modules/@modelcontextprotocol/sdk/node_modules/iconv-lite": {"version": "0.6.3", "resolved": "https://registry.npmjs.org/iconv-lite/-/iconv-lite-0.6.3.tgz", "integrity": "sha512-4fCk79wshMdzMp2rH06qWrJE4iolqLhCUH+OiuIgU++RB0+94NlDL81atO7GX55uUKueo0txHNtvEyI6D7WdMw==", "license": "MIT", "dependencies": {"safer-buffer": ">= 2.1.2 < 3.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/@modelcontextprotocol/sdk/node_modules/media-typer": {"version": "1.1.0", "resolved": "https://registry.npmjs.org/media-typer/-/media-typer-1.1.0.tgz", "integrity": "sha512-aisnrDP4GNe06UcKFnV5bfMNPBUw4jsLGaWwWfnH3v02GnBuXX2MCVn5RbrWo0j3pczUilYblq7fQ7Nw2t5XKw==", "license": "MIT", "engines": {"node": ">= 0.8"}}, "node_modules/@modelcontextprotocol/sdk/node_modules/merge-descriptors": {"version": "2.0.0", "resolved": "https://registry.npmjs.org/merge-descriptors/-/merge-descriptors-2.0.0.tgz", "integrity": "sha512-Snk314V5ayFLhp3fkUREub6WtjBfPdCPY1Ln8/8munuLuiYhsABgBVWsozAG+MWMbVEvcdcpbi9R7ww22l9Q3g==", "license": "MIT", "engines": {"node": ">=18"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/@modelcontextprotocol/sdk/node_modules/mime-db": {"version": "1.54.0", "resolved": "https://registry.npmjs.org/mime-db/-/mime-db-1.54.0.tgz", "integrity": "sha512-aU5EJuIN2WDemCcAp2vFBfp/m4EAhWJnUNSSw0ixs7/kXbd6Pg64EmwJkNdFhB8aWt1sH2CTXrLxo/iAGV3oPQ==", "license": "MIT", "engines": {"node": ">= 0.6"}}, "node_modules/@modelcontextprotocol/sdk/node_modules/mime-types": {"version": "3.0.1", "resolved": "https://registry.npmjs.org/mime-types/-/mime-types-3.0.1.tgz", "integrity": "sha512-xRc4oEhT6eaBpU1XF7AjpOFD+xQmXNB5OVKwp4tqCuBpHLS/ZbBDrc07mYTDqVMg6PfxUjjNp85O6Cd2Z/5HWA==", "license": "MIT", "dependencies": {"mime-db": "^1.54.0"}, "engines": {"node": ">= 0.6"}}, "node_modules/@modelcontextprotocol/sdk/node_modules/ms": {"version": "2.1.3", "resolved": "https://registry.npmjs.org/ms/-/ms-2.1.3.tgz", "integrity": "sha512-6FlzubTLZG3J2a/NVCAleEhjzq5oxgHyaCU9yYXvcLsvoVaHJq/s5xXI6/XXP6tz7R9xAOtHnSO/tXtF3WRTlA==", "license": "MIT"}, "node_modules/@modelcontextprotocol/sdk/node_modules/negotiator": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/negotiator/-/negotiator-1.0.0.tgz", "integrity": "sha512-8Ofs/AUQh8MaEcrlq5xOX0CQ9ypTF5dl78mjlMNfOK08fzpgTHQRQPBxcPlEtIw0yRpws+Zo/3r+5WRby7u3Gg==", "license": "MIT", "engines": {"node": ">= 0.6"}}, "node_modules/@modelcontextprotocol/sdk/node_modules/qs": {"version": "6.14.0", "resolved": "https://registry.npmjs.org/qs/-/qs-6.14.0.tgz", "integrity": "sha512-YWWTjgABSKcvs/nWBi9PycY/JiPJqOD4JA6o9Sej2AtvSGarXxKC3OQSk4pAarbdQlKAh5D4FCQkJNkW+GAn3w==", "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"side-channel": "^1.1.0"}, "engines": {"node": ">=0.6"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/@modelcontextprotocol/sdk/node_modules/send": {"version": "1.2.0", "resolved": "https://registry.npmjs.org/send/-/send-1.2.0.tgz", "integrity": "sha512-uaW0WwXKpL9blXE2o0bRhoL2EGXIrZxQ2ZQ4mgcfoBxdFmQold+qWsD2jLrfZ0trjKL6vOw0j//eAwcALFjKSw==", "license": "MIT", "dependencies": {"debug": "^4.3.5", "encodeurl": "^2.0.0", "escape-html": "^1.0.3", "etag": "^1.8.1", "fresh": "^2.0.0", "http-errors": "^2.0.0", "mime-types": "^3.0.1", "ms": "^2.1.3", "on-finished": "^2.4.1", "range-parser": "^1.2.1", "statuses": "^2.0.1"}, "engines": {"node": ">= 18"}}, "node_modules/@modelcontextprotocol/sdk/node_modules/serve-static": {"version": "2.2.0", "resolved": "https://registry.npmjs.org/serve-static/-/serve-static-2.2.0.tgz", "integrity": "sha512-61g9pCh0Vnh7IutZjtLGGpTA355+OPn2TyDv/6ivP2h/AdAVX9azsoxmg2/M6nZeQZNYBEwIcsne1mJd9oQItQ==", "license": "MIT", "dependencies": {"encodeurl": "^2.0.0", "escape-html": "^1.0.3", "parseurl": "^1.3.3", "send": "^1.2.0"}, "engines": {"node": ">= 18"}}, "node_modules/@modelcontextprotocol/sdk/node_modules/type-is": {"version": "2.0.1", "resolved": "https://registry.npmjs.org/type-is/-/type-is-2.0.1.tgz", "integrity": "sha512-OZs6gsjF4vMp32qrCbiVSkrFmXtG/AZhY3t0iAMrMBiAZyV9oALtXO8hsrHbMXF9x6L3grlFuwW2oAz7cav+Gw==", "license": "MIT", "dependencies": {"content-type": "^1.0.5", "media-typer": "^1.1.0", "mime-types": "^3.0.0"}, "engines": {"node": ">= 0.6"}}, "node_modules/@supabase/mcp-server-supabase": {"version": "0.4.4", "resolved": "https://registry.npmjs.org/@supabase/mcp-server-supabase/-/mcp-server-supabase-0.4.4.tgz", "integrity": "sha512-GYgd4R+TTnQICjLxmdW0RRQREqG8Ix+1f9D8kroPASt25p/F60ohD8jPx53l7ym3qjb05Jy5tpJW2pss+ifV5g==", "license": "Apache-2.0", "dependencies": {"@deno/eszip": "^0.84.0", "@modelcontextprotocol/sdk": "^1.11.0", "@supabase/mcp-utils": "0.2.1", "common-tags": "^1.8.2", "graphql": "^16.11.0", "openapi-fetch": "^0.13.5", "zod": "^3.24.1"}, "bin": {"mcp-server-supabase": "dist/transports/stdio.js"}}, "node_modules/@supabase/mcp-utils": {"version": "0.2.1", "resolved": "https://registry.npmjs.org/@supabase/mcp-utils/-/mcp-utils-0.2.1.tgz", "integrity": "sha512-T3LEAEKXOxHGVzhPvxqbAYbxluUKNxQpFnYVyRIazQJOQzZ03tCg+pp3LUYQi0HkWPIo+u+AgtULJVEvgeNr/Q==", "license": "Apache-2.0", "dependencies": {"@modelcontextprotocol/sdk": "^1.11.0", "zod": "^3.24.1", "zod-to-json-schema": "^3.24.1"}}, "node_modules/accepts": {"version": "1.3.8", "resolved": "https://registry.npmjs.org/accepts/-/accepts-1.3.8.tgz", "integrity": "sha512-PYAthTa2m2VKxuvSD3DPC/Gy+U+sOA1LAuT8mkmRuvw+NACSaeXEQ+NHcVF7rONl6qcaxV3Uuemwawk+7+SJLw==", "license": "MIT", "dependencies": {"mime-types": "~2.1.34", "negotiator": "0.6.3"}, "engines": {"node": ">= 0.6"}}, "node_modules/ajv": {"version": "6.12.6", "resolved": "https://registry.npmjs.org/ajv/-/ajv-6.12.6.tgz", "integrity": "sha512-j3fVLgvTo527anyYyJOGTYJbG+vnnQYvE0m5mmkc1TK+nxAppkCLMIL0aZ4dblVCNoGShhm+kzE4ZUykBoMg4g==", "license": "MIT", "dependencies": {"fast-deep-equal": "^3.1.1", "fast-json-stable-stringify": "^2.0.0", "json-schema-traverse": "^0.4.1", "uri-js": "^4.2.2"}, "funding": {"type": "github", "url": "https://github.com/sponsors/epoberezkin"}}, "node_modules/array-flatten": {"version": "1.1.1", "resolved": "https://registry.npmjs.org/array-flatten/-/array-flatten-1.1.1.tgz", "integrity": "sha512-PCVAQswWemu6UdxsDFFX/+gVeYqKAod3D3UVm91jHwynguOwAvYPhx8nNlM++NqRcK6CxxpUafjmhIdKiHibqg==", "license": "MIT"}, "node_modules/body-parser": {"version": "1.20.3", "resolved": "https://registry.npmjs.org/body-parser/-/body-parser-1.20.3.tgz", "integrity": "sha512-7rAxByjUMqQ3/bHJy7D6OGXvx/MMc4IqBn/X0fcM1QUcAItpZrBEYhWGem+tzXH90c+G01ypMcYJBO9Y30203g==", "license": "MIT", "dependencies": {"bytes": "3.1.2", "content-type": "~1.0.5", "debug": "2.6.9", "depd": "2.0.0", "destroy": "1.2.0", "http-errors": "2.0.0", "iconv-lite": "0.4.24", "on-finished": "2.4.1", "qs": "6.13.0", "raw-body": "2.5.2", "type-is": "~1.6.18", "unpipe": "1.0.0"}, "engines": {"node": ">= 0.8", "npm": "1.2.8000 || >= 1.4.16"}}, "node_modules/body-parser/node_modules/raw-body": {"version": "2.5.2", "resolved": "https://registry.npmjs.org/raw-body/-/raw-body-2.5.2.tgz", "integrity": "sha512-8zGqypfENjCIqGhgXToC8aB2r7YrBX+AQAfIPs/Mlk+BtPTztOvTS01NRW/3Eh60J+a48lt8qsCzirQ6loCVfA==", "license": "MIT", "dependencies": {"bytes": "3.1.2", "http-errors": "2.0.0", "iconv-lite": "0.4.24", "unpipe": "1.0.0"}, "engines": {"node": ">= 0.8"}}, "node_modules/bytes": {"version": "3.1.2", "resolved": "https://registry.npmjs.org/bytes/-/bytes-3.1.2.tgz", "integrity": "sha512-/Nf7TyzTx6S3yRJObOAV7956r8cr2+Oj8AC5dt8wSP3BQAoeX58NoHyCU8P8zGkNXStjTSi6fzO6F0pBdcYbEg==", "license": "MIT", "engines": {"node": ">= 0.8"}}, "node_modules/call-bind-apply-helpers": {"version": "1.0.2", "resolved": "https://registry.npmjs.org/call-bind-apply-helpers/-/call-bind-apply-helpers-1.0.2.tgz", "integrity": "sha512-Sp1ablJ0ivDkSzjcaJdxEunN5/XvksFJ2sMBFfq6x0ryhQV/2b/KwFe21cMpmHtPOSij8K99/wSfoEuTObmuMQ==", "license": "MIT", "dependencies": {"es-errors": "^1.3.0", "function-bind": "^1.1.2"}, "engines": {"node": ">= 0.4"}}, "node_modules/call-bound": {"version": "1.0.4", "resolved": "https://registry.npmjs.org/call-bound/-/call-bound-1.0.4.tgz", "integrity": "sha512-+ys997U96po4Kx/ABpBCqhA9EuxJaQWDQg7295H4hBphv3IZg0boBKuwYpt4YXp6MZ5AmZQnU/tyMTlRpaSejg==", "license": "MIT", "dependencies": {"call-bind-apply-helpers": "^1.0.2", "get-intrinsic": "^1.3.0"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/common-tags": {"version": "1.8.2", "resolved": "https://registry.npmjs.org/common-tags/-/common-tags-1.8.2.tgz", "integrity": "sha512-gk/Z852D2Wtb//0I+kRFNKKE9dIIVirjoqPoA1wJU+XePVXZfGeBpk45+A1rKO4Q43prqWBNY/MiIeRLbPWUaA==", "license": "MIT", "engines": {"node": ">=4.0.0"}}, "node_modules/content-disposition": {"version": "0.5.4", "resolved": "https://registry.npmjs.org/content-disposition/-/content-disposition-0.5.4.tgz", "integrity": "sha512-FveZTNuGw04cxlAiWbzi6zTAL/lhehaWbTtgluJh4/E95DqMwTmha3KZN1aAWA8cFIhHzMZUvLevkw5Rqk+tSQ==", "license": "MIT", "dependencies": {"safe-buffer": "5.2.1"}, "engines": {"node": ">= 0.6"}}, "node_modules/content-type": {"version": "1.0.5", "resolved": "https://registry.npmjs.org/content-type/-/content-type-1.0.5.tgz", "integrity": "sha512-nTjqfcBFEipKdXCv4YDQWCfmcLZKm81ldF0pAopTvyrFGVbcR6P/VAAd5G7N+0tTr8QqiU0tFadD6FK4NtJwOA==", "license": "MIT", "engines": {"node": ">= 0.6"}}, "node_modules/cookie": {"version": "0.7.1", "resolved": "https://registry.npmjs.org/cookie/-/cookie-0.7.1.tgz", "integrity": "sha512-6DnInpx7SJ2AK3+CTUE/ZM0vWTUboZCegxhC2xiIydHR9jNuTAASBrfEpHhiGOZw/nX51bHt6YQl8jsGo4y/0w==", "license": "MIT", "engines": {"node": ">= 0.6"}}, "node_modules/cookie-signature": {"version": "1.0.6", "resolved": "https://registry.npmjs.org/cookie-signature/-/cookie-signature-1.0.6.tgz", "integrity": "sha512-QADzlaHc8icV8I7vbaJXJwod9HWYp8uCqf1xa4OfNu1T7JVxQIrUgOWtHdNDtPiywmFbiS12VjotIXLrKM3orQ==", "license": "MIT"}, "node_modules/cors": {"version": "2.8.5", "resolved": "https://registry.npmjs.org/cors/-/cors-2.8.5.tgz", "integrity": "sha512-KIHbLJqu73RGr/hnbrO9uBeixNGuvSQjul/jdFvS/KFSIH1hWVd1ng7zOHx+YrEfInLG7q4n6GHQ9cDtxv/P6g==", "license": "MIT", "dependencies": {"object-assign": "^4", "vary": "^1"}, "engines": {"node": ">= 0.10"}}, "node_modules/cross-spawn": {"version": "7.0.6", "resolved": "https://registry.npmjs.org/cross-spawn/-/cross-spawn-7.0.6.tgz", "integrity": "sha512-uV2QOWP2nWzsy2aMp8aRibhi9dlzF5Hgh5SHaB9OiTGEyDTiJJyx0uy51QXdyWbtAHNua4XJzUKca3OzKUd3vA==", "license": "MIT", "dependencies": {"path-key": "^3.1.0", "shebang-command": "^2.0.0", "which": "^2.0.1"}, "engines": {"node": ">= 8"}}, "node_modules/cross-spawn/node_modules/isexe": {"version": "2.0.0", "resolved": "https://registry.npmjs.org/isexe/-/isexe-2.0.0.tgz", "integrity": "sha512-RHxMLp9lnKHGHRng9QFhRCMbYAcVpn69smSGcq3f36xjgVVWThj4qqLbTLlq7Ssj8B+fIQ1EuCEGI2lKsyQeIw==", "license": "ISC"}, "node_modules/cross-spawn/node_modules/which": {"version": "2.0.2", "resolved": "https://registry.npmjs.org/which/-/which-2.0.2.tgz", "integrity": "sha512-BLI3Tl1TW3Pvl70l3yq3Y64i+awpwXqsGBYWkkqMtnbXgrMD+yj7rhW0kuEDxzJaYXGjEW5ogapKNMEKNMjibA==", "license": "ISC", "dependencies": {"isexe": "^2.0.0"}, "bin": {"node-which": "bin/node-which"}, "engines": {"node": ">= 8"}}, "node_modules/debug": {"version": "2.6.9", "resolved": "https://registry.npmjs.org/debug/-/debug-2.6.9.tgz", "integrity": "sha512-bC7ElrdJaJnPbAP+1EotYvqZsb3ecl5wi6Bfi6BJTUcNowp6cvspg0jXznRTKDjm/E7AdgFBVeAPVMNcKGsHMA==", "license": "MIT", "dependencies": {"ms": "2.0.0"}}, "node_modules/depd": {"version": "2.0.0", "resolved": "https://registry.npmjs.org/depd/-/depd-2.0.0.tgz", "integrity": "sha512-g7nH6P6dyDioJogAAGprGpCtVImJhpPk/roCzdb3fIh61/s/nPsfR6onyMwkCAR/OlC3yBC0lESvUoQEAssIrw==", "license": "MIT", "engines": {"node": ">= 0.8"}}, "node_modules/destroy": {"version": "1.2.0", "resolved": "https://registry.npmjs.org/destroy/-/destroy-1.2.0.tgz", "integrity": "sha512-2sJGJTaXIIaR1w4iJSNoN0hnMY7Gpc/n8D4qSCJw8QqFWXf7cuAgnEHxBpweaVcPevC2l3KpjYCx3NypQQgaJg==", "license": "MIT", "engines": {"node": ">= 0.8", "npm": "1.2.8000 || >= 1.4.16"}}, "node_modules/dunder-proto": {"version": "1.0.1", "resolved": "https://registry.npmjs.org/dunder-proto/-/dunder-proto-1.0.1.tgz", "integrity": "sha512-KIN/nDJBQRcXw0MLVhZE9iQHmG68qAVIBg9CqmUYjmQIhgij9U5MFvrqkUL5FbtyyzZuOeOt0zdeRe4UY7ct+A==", "license": "MIT", "dependencies": {"call-bind-apply-helpers": "^1.0.1", "es-errors": "^1.3.0", "gopd": "^1.2.0"}, "engines": {"node": ">= 0.4"}}, "node_modules/ee-first": {"version": "1.1.1", "resolved": "https://registry.npmjs.org/ee-first/-/ee-first-1.1.1.tgz", "integrity": "sha512-WMwm9LhRUo+WUaRN+vRuETqG89IgZphVSNkdFgeb6sS/E4OrDIN7t48CAewSHXc6C8lefD8KKfr5vY61brQlow==", "license": "MIT"}, "node_modules/encodeurl": {"version": "2.0.0", "resolved": "https://registry.npmjs.org/encodeurl/-/encodeurl-2.0.0.tgz", "integrity": "sha512-Q0n9HRi4m6JuGIV1eFlmvJB7ZEVxu93IrMyiMsGC0lrMJMWzRgx6WGquyfQgZVb31vhGgXnfmPNNXmxnOkRBrg==", "license": "MIT", "engines": {"node": ">= 0.8"}}, "node_modules/es-define-property": {"version": "1.0.1", "resolved": "https://registry.npmjs.org/es-define-property/-/es-define-property-1.0.1.tgz", "integrity": "sha512-e3nRfgfUZ4rNGL232gUgX06QNyyez04KdjFrF+LTRoOXmrOgFKDg4BCdsjW8EnT69eqdYGmRpJwiPVYNrCaW3g==", "license": "MIT", "engines": {"node": ">= 0.4"}}, "node_modules/es-errors": {"version": "1.3.0", "resolved": "https://registry.npmjs.org/es-errors/-/es-errors-1.3.0.tgz", "integrity": "sha512-Zf5H2Kxt2xjTvbJvP2ZWLEICxA6j+hAmMzIlypy4xcBg1vKVnx89Wy0GbS+kf5cwCVFFzdCFh2XSCFNULS6csw==", "license": "MIT", "engines": {"node": ">= 0.4"}}, "node_modules/es-object-atoms": {"version": "1.1.1", "resolved": "https://registry.npmjs.org/es-object-atoms/-/es-object-atoms-1.1.1.tgz", "integrity": "sha512-FGgH2h8zKNim9ljj7dankFPcICIK9Cp5bm+c2gQSYePhpaG5+esrLODihIorn+Pe6FGJzWhXQotPv73jTaldXA==", "license": "MIT", "dependencies": {"es-errors": "^1.3.0"}, "engines": {"node": ">= 0.4"}}, "node_modules/escape-html": {"version": "1.0.3", "resolved": "https://registry.npmjs.org/escape-html/-/escape-html-1.0.3.tgz", "integrity": "sha512-NiSupZ4OeuGwr68lGIeym/ksIZMJodUGOSCZ/FSnTxcrekbvqrgdUxlJOMpijaKZVjAJrWrGs/6Jy8OMuyj9ow==", "license": "MIT"}, "node_modules/etag": {"version": "1.8.1", "resolved": "https://registry.npmjs.org/etag/-/etag-1.8.1.tgz", "integrity": "sha512-aIL5Fx7mawVa300al2BnEE4iNvo1qETxLrPI/o05L7z6go7fCw1J6EQmbK4FmJ2AS7kgVF/KEZWufBfdClMcPg==", "license": "MIT", "engines": {"node": ">= 0.6"}}, "node_modules/eventsource": {"version": "3.0.7", "resolved": "https://registry.npmjs.org/eventsource/-/eventsource-3.0.7.tgz", "integrity": "sha512-CRT1WTyuQoD771GW56XEZFQ/ZoSfWid1alKGDYMmkt2yl8UXrVR4pspqWNEcqKvVIzg6PAltWjxcSSPrboA4iA==", "license": "MIT", "dependencies": {"eventsource-parser": "^3.0.1"}, "engines": {"node": ">=18.0.0"}}, "node_modules/eventsource-parser": {"version": "3.0.2", "resolved": "https://registry.npmjs.org/eventsource-parser/-/eventsource-parser-3.0.2.tgz", "integrity": "sha512-6RxOBZ/cYgd8usLwsEl+EC09Au/9BcmCKYF2/xbml6DNczf7nv0MQb+7BA2F+li6//I+28VNlQR37XfQtcAJuA==", "license": "MIT", "engines": {"node": ">=18.0.0"}}, "node_modules/express": {"version": "4.21.2", "resolved": "https://registry.npmjs.org/express/-/express-4.21.2.tgz", "integrity": "sha512-28HqgMZAmih1Czt9ny7qr6ek2qddF4FclbMzwhCREB6OFfH+rXAnuNCwo1/wFvrtbgsQDb4kSbX9de9lFbrXnA==", "license": "MIT", "dependencies": {"accepts": "~1.3.8", "array-flatten": "1.1.1", "body-parser": "1.20.3", "content-disposition": "0.5.4", "content-type": "~1.0.4", "cookie": "0.7.1", "cookie-signature": "1.0.6", "debug": "2.6.9", "depd": "2.0.0", "encodeurl": "~2.0.0", "escape-html": "~1.0.3", "etag": "~1.8.1", "finalhandler": "1.3.1", "fresh": "0.5.2", "http-errors": "2.0.0", "merge-descriptors": "1.0.3", "methods": "~1.1.2", "on-finished": "2.4.1", "parseurl": "~1.3.3", "path-to-regexp": "0.1.12", "proxy-addr": "~2.0.7", "qs": "6.13.0", "range-parser": "~1.2.1", "safe-buffer": "5.2.1", "send": "0.19.0", "serve-static": "1.16.2", "setprototypeof": "1.2.0", "statuses": "2.0.1", "type-is": "~1.6.18", "utils-merge": "1.0.1", "vary": "~1.1.2"}, "engines": {"node": ">= 0.10.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/express"}}, "node_modules/express-rate-limit": {"version": "7.5.0", "resolved": "https://registry.npmjs.org/express-rate-limit/-/express-rate-limit-7.5.0.tgz", "integrity": "sha512-eB5zbQh5h+VenMPM3fh+nw1YExi5nMr6HUCR62ELSP11huvxm/Uir1H1QEyTkk5QX6A58pX6NmaTMceKZ0Eodg==", "license": "MIT", "engines": {"node": ">= 16"}, "funding": {"url": "https://github.com/sponsors/express-rate-limit"}, "peerDependencies": {"express": "^4.11 || 5 || ^5.0.0-beta.1"}}, "node_modules/fast-deep-equal": {"version": "3.1.3", "resolved": "https://registry.npmjs.org/fast-deep-equal/-/fast-deep-equal-3.1.3.tgz", "integrity": "sha512-f3qQ9oQy9j2AhBe/H9VC91wLmKBCCU/gDOnKNAYG5hswO7BLKj09Hc5HYNz9cGI++xlpDCIgDaitVs03ATR84Q==", "license": "MIT"}, "node_modules/fast-json-stable-stringify": {"version": "2.1.0", "resolved": "https://registry.npmjs.org/fast-json-stable-stringify/-/fast-json-stable-stringify-2.1.0.tgz", "integrity": "sha512-lhd/wF+Lk98HZoTCtlVraHtfh5XYijIjalXck7saUtuanSDyLMxnHhSXEDJqHxD7msR8D0uCmqlkwjCV8xvwHw==", "license": "MIT"}, "node_modules/finalhandler": {"version": "1.3.1", "resolved": "https://registry.npmjs.org/finalhandler/-/finalhandler-1.3.1.tgz", "integrity": "sha512-6BN9trH7bp3qvnrRyzsBz+g3lZxTNZTbVO2EV1CS0WIcDbawYVdYvGflME/9QP0h0pYlCDBCTjYa9nZzMDpyxQ==", "license": "MIT", "dependencies": {"debug": "2.6.9", "encodeurl": "~2.0.0", "escape-html": "~1.0.3", "on-finished": "2.4.1", "parseurl": "~1.3.3", "statuses": "2.0.1", "unpipe": "~1.0.0"}, "engines": {"node": ">= 0.8"}}, "node_modules/forwarded": {"version": "0.2.0", "resolved": "https://registry.npmjs.org/forwarded/-/forwarded-0.2.0.tgz", "integrity": "sha512-buRG0fpBtRHSTCOASe6hD258tEubFoRLb4ZNA6NxMVHNw2gOcwHo9wyablzMzOA5z9xA9L1KNjk/Nt6MT9aYow==", "license": "MIT", "engines": {"node": ">= 0.6"}}, "node_modules/fresh": {"version": "0.5.2", "resolved": "https://registry.npmjs.org/fresh/-/fresh-0.5.2.tgz", "integrity": "sha512-zJ2mQYM18rEFOudeV4GShTGIQ7RbzA7ozbU9I/XBpm7kqgMywgmylMwXHxZJmkVoYkna9d2pVXVXPdYTP9ej8Q==", "license": "MIT", "engines": {"node": ">= 0.6"}}, "node_modules/function-bind": {"version": "1.1.2", "resolved": "https://registry.npmjs.org/function-bind/-/function-bind-1.1.2.tgz", "integrity": "sha512-7XHNxH7qX9xG5mIwxkhumTox/MIRNcOgDrxWsMt2pAr23WHp6MrRlN7FBSFpCpr+oVO0F744iUgR82nJMfG2SA==", "license": "MIT", "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/get-intrinsic": {"version": "1.3.0", "resolved": "https://registry.npmjs.org/get-intrinsic/-/get-intrinsic-1.3.0.tgz", "integrity": "sha512-9fSjSaos/fRIVIp+xSJlE6lfwhES7LNtKaCBIamHsjr2na1BiABJPo0mOjjz8GJDURarmCPGqaiVg5mfjb98CQ==", "license": "MIT", "dependencies": {"call-bind-apply-helpers": "^1.0.2", "es-define-property": "^1.0.1", "es-errors": "^1.3.0", "es-object-atoms": "^1.1.1", "function-bind": "^1.1.2", "get-proto": "^1.0.1", "gopd": "^1.2.0", "has-symbols": "^1.1.0", "hasown": "^2.0.2", "math-intrinsics": "^1.1.0"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/get-proto": {"version": "1.0.1", "resolved": "https://registry.npmjs.org/get-proto/-/get-proto-1.0.1.tgz", "integrity": "sha512-sTSfBjoXBp89JvIKIefqw7U2CCebsc74kiY6awiGogKtoSGbgjYE/G/+l9sF3MWFPNc9IcoOC4ODfKHfxFmp0g==", "license": "MIT", "dependencies": {"dunder-proto": "^1.0.1", "es-object-atoms": "^1.0.0"}, "engines": {"node": ">= 0.4"}}, "node_modules/gopd": {"version": "1.2.0", "resolved": "https://registry.npmjs.org/gopd/-/gopd-1.2.0.tgz", "integrity": "sha512-ZUKRh6/kUFoAiTAtTYPZJ3hw9wNxx+BIBOijnlG9PnrJsCcSjs1wyyD6vJpaYtgnzDrKYRSqf3OO6Rfa93xsRg==", "license": "MIT", "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/graphql": {"version": "16.11.0", "resolved": "https://registry.npmjs.org/graphql/-/graphql-16.11.0.tgz", "integrity": "sha512-mS1lbMsxgQj6hge1XZ6p7GPhbrtFwUFYi3wRzXAC/FmYnyXMTvvI3td3rjmQ2u8ewXueaSvRPWaEcgVVOT9Jnw==", "license": "MIT", "engines": {"node": "^12.22.0 || ^14.16.0 || ^16.0.0 || >=17.0.0"}}, "node_modules/has-symbols": {"version": "1.1.0", "resolved": "https://registry.npmjs.org/has-symbols/-/has-symbols-1.1.0.tgz", "integrity": "sha512-1cDNdwJ2Jaohmb3sg4OmKaMBwuC48sYni5HUw2DvsC8LjGTLK9h+eb1X6RyuOHe4hT0ULCW68iomhjUoKUqlPQ==", "license": "MIT", "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/hasown": {"version": "2.0.2", "resolved": "https://registry.npmjs.org/hasown/-/hasown-2.0.2.tgz", "integrity": "sha512-0hJU9SCPvmMzIBdZFqNPXWa6dqh7WdH0cII9y+CyS8rG3nL48Bclra9HmKhVVUHyPWNH5Y7xDwAB7bfgSjkUMQ==", "license": "MIT", "dependencies": {"function-bind": "^1.1.2"}, "engines": {"node": ">= 0.4"}}, "node_modules/http-errors": {"version": "2.0.0", "resolved": "https://registry.npmjs.org/http-errors/-/http-errors-2.0.0.tgz", "integrity": "sha512-FtwrG/euBzaEjYeRqOgly7G0qviiXoJWnvEH2Z1plBdXgbyjv34pHTSb9zoeHMyDy33+DWy5Wt9Wo+TURtOYSQ==", "license": "MIT", "dependencies": {"depd": "2.0.0", "inherits": "2.0.4", "setprototypeof": "1.2.0", "statuses": "2.0.1", "toidentifier": "1.0.1"}, "engines": {"node": ">= 0.8"}}, "node_modules/iconv-lite": {"version": "0.4.24", "resolved": "https://registry.npmjs.org/iconv-lite/-/iconv-lite-0.4.24.tgz", "integrity": "sha512-v3MXnZAcvnywkTUEZomIActle7RXXeedOR31wwl7VlyoXO4Qi9arvSenNQWne1TcRwhCL1HwLI21bEqdpj8/rA==", "license": "MIT", "dependencies": {"safer-buffer": ">= 2.1.2 < 3"}, "engines": {"node": ">=0.10.0"}}, "node_modules/inherits": {"version": "2.0.4", "resolved": "https://registry.npmjs.org/inherits/-/inherits-2.0.4.tgz", "integrity": "sha512-k/vGaX4/Yla3WzyMCvTQOXYeIHvqOKtnqBduzTHpzpQZzAskKMhZ2K+EnBiSM9zGSoIFeMpXKxa4dYeZIQqewQ==", "license": "ISC"}, "node_modules/ipaddr.js": {"version": "1.9.1", "resolved": "https://registry.npmjs.org/ipaddr.js/-/ipaddr.js-1.9.1.tgz", "integrity": "sha512-0KI/607xoxSToH7GjN1FfSbLoU0+btTicjsQSWQlh/hZykN8KpmMf7uYwPW3R+akZ6R/w18ZlXSHBYXiYUPO3g==", "license": "MIT", "engines": {"node": ">= 0.10"}}, "node_modules/is-promise": {"version": "4.0.0", "resolved": "https://registry.npmjs.org/is-promise/-/is-promise-4.0.0.tgz", "integrity": "sha512-hvpoI6korhJMnej285dSg6nu1+e6uxs7zG3BYAm5byqDsgJNWwxzM6z6iZiAgQR4TJ30JmBTOwqZUw3WlyH3AQ==", "license": "MIT"}, "node_modules/isexe": {"version": "3.1.1", "resolved": "https://registry.npmjs.org/isexe/-/isexe-3.1.1.tgz", "integrity": "sha512-LpB/54B+/2J5hqQ7imZHfdU31OlgQqx7ZicVlkm9kzg9/w8GKLEcFfJl/t7DCEDueOyBAD6zCCwTO6Fzs0NoEQ==", "license": "ISC", "engines": {"node": ">=16"}}, "node_modules/json-schema-traverse": {"version": "0.4.1", "resolved": "https://registry.npmjs.org/json-schema-traverse/-/json-schema-traverse-0.4.1.tgz", "integrity": "sha512-xbbCH5dCYU5T8LcEhhuh7HJ88HXuW3qsI3Y0zOZFKfZEHcpWiHU/Jxzk629Brsab/mMiHQti9wMP+845RPe3Vg==", "license": "MIT"}, "node_modules/math-intrinsics": {"version": "1.1.0", "resolved": "https://registry.npmjs.org/math-intrinsics/-/math-intrinsics-1.1.0.tgz", "integrity": "sha512-/IXtbwEk5HTPyEwyKX6hGkYXxM9nbj64B+ilVJnC/R6B0pH5G4V3b0pVbL7DBj4tkhBAppbQUlf6F6Xl9LHu1g==", "license": "MIT", "engines": {"node": ">= 0.4"}}, "node_modules/media-typer": {"version": "0.3.0", "resolved": "https://registry.npmjs.org/media-typer/-/media-typer-0.3.0.tgz", "integrity": "sha512-dq+qelQ9akHpcOl/gUVRTxVIOkAJ1wR3QAvb4RsVjS8oVoFjDGTc679wJYmUmknUF5HwMLOgb5O+a3KxfWapPQ==", "license": "MIT", "engines": {"node": ">= 0.6"}}, "node_modules/merge-descriptors": {"version": "1.0.3", "resolved": "https://registry.npmjs.org/merge-descriptors/-/merge-descriptors-1.0.3.tgz", "integrity": "sha512-gaNvAS7TZ897/rVaZ0nMtAyxNyi/pdbjbAwUpFQpN70GqnVfOiXpeUUMKRBmzXaSQ8DdTX4/0ms62r2K+hE6mQ==", "license": "MIT", "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/methods": {"version": "1.1.2", "resolved": "https://registry.npmjs.org/methods/-/methods-1.1.2.tgz", "integrity": "sha512-iclAHeNqNm68zFtnZ0e+1L2yUIdvzNoauKU4WBA3VvH/vPFieF7qfRlwUZU+DA9P9bPXIS90ulxoUoCH23sV2w==", "license": "MIT", "engines": {"node": ">= 0.6"}}, "node_modules/mime": {"version": "1.6.0", "resolved": "https://registry.npmjs.org/mime/-/mime-1.6.0.tgz", "integrity": "sha512-x0Vn8spI+wuJ1O6S7gnbaQg8Pxh4NNHb7KSINmEWKiPE4RKOplvijn+NkmYmmRgP68mc70j2EbeTFRsrswaQeg==", "license": "MIT", "bin": {"mime": "cli.js"}, "engines": {"node": ">=4"}}, "node_modules/mime-db": {"version": "1.52.0", "resolved": "https://registry.npmjs.org/mime-db/-/mime-db-1.52.0.tgz", "integrity": "sha512-sPU4uV7dYlvtWJxwwxHD0PuihVNiE7TyAbQ5SWxDCB9mUYvOgroQOwYQQOKPJ8CIbE+1ETVlOoK1UC2nU3gYvg==", "license": "MIT", "engines": {"node": ">= 0.6"}}, "node_modules/mime-types": {"version": "2.1.35", "resolved": "https://registry.npmjs.org/mime-types/-/mime-types-2.1.35.tgz", "integrity": "sha512-ZDY+bPm5zTTF+YpCrAU9nK0UgICYPT0QtT1NZWFv4s++TNkcgVaT0g6+4R2uI4MjQjzysHB1zxuWL50hzaeXiw==", "license": "MIT", "dependencies": {"mime-db": "1.52.0"}, "engines": {"node": ">= 0.6"}}, "node_modules/ms": {"version": "2.0.0", "resolved": "https://registry.npmjs.org/ms/-/ms-2.0.0.tgz", "integrity": "sha512-Tpp60P6IUJDTuOq/5Z8cdskzJujfwqfOTkrwIwj7IRISpnkJnT6SyJ4PCPnGMoFjC9ddhal5KVIYtAt97ix05A==", "license": "MIT"}, "node_modules/negotiator": {"version": "0.6.3", "resolved": "https://registry.npmjs.org/negotiator/-/negotiator-0.6.3.tgz", "integrity": "sha512-+EUsqGPLsM+j/zdChZjsnX51g4XrHFOIXwfnCVPGlQk/k5giakcKsuxCObBRu6DSm9opw/O6slWbJdghQM4bBg==", "license": "MIT", "engines": {"node": ">= 0.6"}}, "node_modules/npx": {"version": "10.2.2", "resolved": "https://registry.npmjs.org/npx/-/npx-10.2.2.tgz", "integrity": "sha512-eImmySusyeWphzs5iNh791XbZnZG0FSNvM4KSah34pdQQIDsdTDhIwg1sjN3AIVcjGLpbQ/YcfqHPshKZQK1fA==", "bundleDependencies": ["npm", "libnpx"], "deprecated": "This package is now part of the npm CLI.", "license": "ISC", "dependencies": {"libnpx": "10.2.2", "npm": "5.1.0"}, "bin": {"npx": "index.js"}}, "node_modules/npx/node_modules/ansi-align": {"version": "2.0.0", "inBundle": true, "license": "ISC", "dependencies": {"string-width": "^2.0.0"}}, "node_modules/npx/node_modules/ansi-regex": {"version": "3.0.0", "inBundle": true, "license": "MIT", "engines": {"node": ">=4"}}, "node_modules/npx/node_modules/ansi-styles": {"version": "3.2.1", "inBundle": true, "license": "MIT", "dependencies": {"color-convert": "^1.9.0"}, "engines": {"node": ">=4"}}, "node_modules/npx/node_modules/balanced-match": {"version": "1.0.0", "inBundle": true, "license": "MIT"}, "node_modules/npx/node_modules/boxen": {"version": "1.3.0", "inBundle": true, "license": "MIT", "dependencies": {"ansi-align": "^2.0.0", "camelcase": "^4.0.0", "chalk": "^2.0.1", "cli-boxes": "^1.0.0", "string-width": "^2.0.0", "term-size": "^1.2.0", "widest-line": "^2.0.0"}, "engines": {"node": ">=4"}}, "node_modules/npx/node_modules/brace-expansion": {"version": "1.1.11", "inBundle": true, "license": "MIT", "dependencies": {"balanced-match": "^1.0.0", "concat-map": "0.0.1"}}, "node_modules/npx/node_modules/builtins": {"version": "1.0.3", "inBundle": true, "license": "MIT"}, "node_modules/npx/node_modules/camelcase": {"version": "4.1.0", "inBundle": true, "license": "MIT", "engines": {"node": ">=4"}}, "node_modules/npx/node_modules/capture-stack-trace": {"version": "1.0.1", "inBundle": true, "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/npx/node_modules/chalk": {"version": "2.4.2", "inBundle": true, "license": "MIT", "dependencies": {"ansi-styles": "^3.2.1", "escape-string-regexp": "^1.0.5", "supports-color": "^5.3.0"}, "engines": {"node": ">=4"}}, "node_modules/npx/node_modules/ci-info": {"version": "1.6.0", "inBundle": true, "license": "MIT"}, "node_modules/npx/node_modules/cli-boxes": {"version": "1.0.0", "inBundle": true, "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/npx/node_modules/cliui": {"version": "4.1.0", "inBundle": true, "license": "ISC", "dependencies": {"string-width": "^2.1.1", "strip-ansi": "^4.0.0", "wrap-ansi": "^2.0.0"}}, "node_modules/npx/node_modules/code-point-at": {"version": "1.1.0", "inBundle": true, "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/npx/node_modules/color-convert": {"version": "1.9.3", "inBundle": true, "license": "MIT", "dependencies": {"color-name": "1.1.3"}}, "node_modules/npx/node_modules/color-name": {"version": "1.1.3", "inBundle": true, "license": "MIT"}, "node_modules/npx/node_modules/concat-map": {"version": "0.0.1", "inBundle": true, "license": "MIT"}, "node_modules/npx/node_modules/configstore": {"version": "3.1.2", "inBundle": true, "license": "BSD-2-<PERSON><PERSON>", "dependencies": {"dot-prop": "^4.1.0", "graceful-fs": "^4.1.2", "make-dir": "^1.0.0", "unique-string": "^1.0.0", "write-file-atomic": "^2.0.0", "xdg-basedir": "^3.0.0"}, "engines": {"node": ">=4"}}, "node_modules/npx/node_modules/create-error-class": {"version": "3.0.2", "inBundle": true, "license": "MIT", "dependencies": {"capture-stack-trace": "^1.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/npx/node_modules/cross-spawn": {"version": "5.1.0", "inBundle": true, "license": "MIT", "dependencies": {"lru-cache": "^4.0.1", "shebang-command": "^1.2.0", "which": "^1.2.9"}}, "node_modules/npx/node_modules/crypto-random-string": {"version": "1.0.0", "inBundle": true, "license": "MIT", "engines": {"node": ">=4"}}, "node_modules/npx/node_modules/decamelize": {"version": "1.2.0", "inBundle": true, "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/npx/node_modules/deep-extend": {"version": "0.6.0", "inBundle": true, "license": "MIT", "engines": {"node": ">=4.0.0"}}, "node_modules/npx/node_modules/dot-prop": {"version": "4.2.0", "inBundle": true, "license": "MIT", "dependencies": {"is-obj": "^1.0.0"}, "engines": {"node": ">=4"}}, "node_modules/npx/node_modules/dotenv": {"version": "5.0.1", "inBundle": true, "license": "BSD-2-<PERSON><PERSON>", "engines": {"node": ">=4.6.0"}}, "node_modules/npx/node_modules/duplexer3": {"version": "0.1.4", "inBundle": true, "license": "BSD-3-<PERSON><PERSON>"}, "node_modules/npx/node_modules/end-of-stream": {"version": "1.4.4", "inBundle": true, "license": "MIT", "dependencies": {"once": "^1.4.0"}}, "node_modules/npx/node_modules/escape-string-regexp": {"version": "1.0.5", "inBundle": true, "license": "MIT", "engines": {"node": ">=0.8.0"}}, "node_modules/npx/node_modules/execa": {"version": "0.7.0", "inBundle": true, "license": "MIT", "dependencies": {"cross-spawn": "^5.0.1", "get-stream": "^3.0.0", "is-stream": "^1.1.0", "npm-run-path": "^2.0.0", "p-finally": "^1.0.0", "signal-exit": "^3.0.0", "strip-eof": "^1.0.0"}, "engines": {"node": ">=4"}}, "node_modules/npx/node_modules/find-up": {"version": "2.1.0", "inBundle": true, "license": "MIT", "dependencies": {"locate-path": "^2.0.0"}, "engines": {"node": ">=4"}}, "node_modules/npx/node_modules/fs.realpath": {"version": "1.0.0", "inBundle": true, "license": "ISC"}, "node_modules/npx/node_modules/get-caller-file": {"version": "1.0.3", "inBundle": true, "license": "ISC"}, "node_modules/npx/node_modules/get-stream": {"version": "3.0.0", "inBundle": true, "license": "MIT", "engines": {"node": ">=4"}}, "node_modules/npx/node_modules/glob": {"version": "7.1.6", "inBundle": true, "license": "ISC", "dependencies": {"fs.realpath": "^1.0.0", "inflight": "^1.0.4", "inherits": "2", "minimatch": "^3.0.4", "once": "^1.3.0", "path-is-absolute": "^1.0.0"}, "engines": {"node": "*"}, "funding": {"url": "https://github.com/sponsors/isaacs"}}, "node_modules/npx/node_modules/global-dirs": {"version": "0.1.1", "inBundle": true, "license": "MIT", "dependencies": {"ini": "^1.3.4"}, "engines": {"node": ">=4"}}, "node_modules/npx/node_modules/got": {"version": "6.7.1", "inBundle": true, "license": "MIT", "dependencies": {"create-error-class": "^3.0.0", "duplexer3": "^0.1.4", "get-stream": "^3.0.0", "is-redirect": "^1.0.0", "is-retry-allowed": "^1.0.0", "is-stream": "^1.0.0", "lowercase-keys": "^1.0.0", "safe-buffer": "^5.0.1", "timed-out": "^4.0.0", "unzip-response": "^2.0.1", "url-parse-lax": "^1.0.0"}, "engines": {"node": ">=4"}}, "node_modules/npx/node_modules/graceful-fs": {"version": "4.2.3", "inBundle": true, "license": "ISC"}, "node_modules/npx/node_modules/has-flag": {"version": "3.0.0", "inBundle": true, "license": "MIT", "engines": {"node": ">=4"}}, "node_modules/npx/node_modules/hosted-git-info": {"version": "2.8.5", "inBundle": true, "license": "ISC"}, "node_modules/npx/node_modules/import-lazy": {"version": "2.1.0", "inBundle": true, "license": "MIT", "engines": {"node": ">=4"}}, "node_modules/npx/node_modules/imurmurhash": {"version": "0.1.4", "inBundle": true, "license": "MIT", "engines": {"node": ">=0.8.19"}}, "node_modules/npx/node_modules/inflight": {"version": "1.0.6", "inBundle": true, "license": "ISC", "dependencies": {"once": "^1.3.0", "wrappy": "1"}}, "node_modules/npx/node_modules/inherits": {"version": "2.0.4", "inBundle": true, "license": "ISC"}, "node_modules/npx/node_modules/ini": {"version": "1.3.5", "inBundle": true, "license": "ISC", "engines": {"node": "*"}}, "node_modules/npx/node_modules/invert-kv": {"version": "2.0.0", "inBundle": true, "license": "MIT", "engines": {"node": ">=4"}}, "node_modules/npx/node_modules/is-ci": {"version": "1.2.1", "inBundle": true, "license": "MIT", "dependencies": {"ci-info": "^1.5.0"}, "bin": {"is-ci": "bin.js"}}, "node_modules/npx/node_modules/is-fullwidth-code-point": {"version": "2.0.0", "inBundle": true, "license": "MIT", "engines": {"node": ">=4"}}, "node_modules/npx/node_modules/is-installed-globally": {"version": "0.1.0", "inBundle": true, "license": "MIT", "dependencies": {"global-dirs": "^0.1.0", "is-path-inside": "^1.0.0"}, "engines": {"node": ">=4"}}, "node_modules/npx/node_modules/is-npm": {"version": "1.0.0", "inBundle": true, "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/npx/node_modules/is-obj": {"version": "1.0.1", "inBundle": true, "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/npx/node_modules/is-path-inside": {"version": "1.0.1", "inBundle": true, "license": "MIT", "dependencies": {"path-is-inside": "^1.0.1"}, "engines": {"node": ">=0.10.0"}}, "node_modules/npx/node_modules/is-redirect": {"version": "1.0.0", "inBundle": true, "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/npx/node_modules/is-retry-allowed": {"version": "1.2.0", "inBundle": true, "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/npx/node_modules/is-stream": {"version": "1.1.0", "inBundle": true, "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/npx/node_modules/isexe": {"version": "2.0.0", "inBundle": true, "license": "ISC"}, "node_modules/npx/node_modules/latest-version": {"version": "3.1.0", "inBundle": true, "license": "MIT", "dependencies": {"package-json": "^4.0.0"}, "engines": {"node": ">=4"}}, "node_modules/npx/node_modules/lcid": {"version": "2.0.0", "inBundle": true, "license": "MIT", "dependencies": {"invert-kv": "^2.0.0"}, "engines": {"node": ">=6"}}, "node_modules/npx/node_modules/libnpx": {"version": "10.2.2", "inBundle": true, "license": "ISC", "dependencies": {"dotenv": "^5.0.1", "npm-package-arg": "^6.0.0", "rimraf": "^2.6.2", "safe-buffer": "^5.1.0", "update-notifier": "^2.3.0", "which": "^1.3.0", "y18n": "^4.0.0", "yargs": "^11.0.0"}, "engines": {"node": ">=4"}}, "node_modules/npx/node_modules/locate-path": {"version": "2.0.0", "inBundle": true, "license": "MIT", "dependencies": {"p-locate": "^2.0.0", "path-exists": "^3.0.0"}, "engines": {"node": ">=4"}}, "node_modules/npx/node_modules/lowercase-keys": {"version": "1.0.1", "inBundle": true, "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/npx/node_modules/lru-cache": {"version": "4.1.5", "inBundle": true, "license": "ISC", "dependencies": {"pseudomap": "^1.0.2", "yallist": "^2.1.2"}}, "node_modules/npx/node_modules/make-dir": {"version": "1.3.0", "inBundle": true, "license": "MIT", "dependencies": {"pify": "^3.0.0"}, "engines": {"node": ">=4"}}, "node_modules/npx/node_modules/map-age-cleaner": {"version": "0.1.3", "inBundle": true, "license": "MIT", "dependencies": {"p-defer": "^1.0.0"}, "engines": {"node": ">=6"}}, "node_modules/npx/node_modules/mem": {"version": "4.3.0", "inBundle": true, "license": "MIT", "dependencies": {"map-age-cleaner": "^0.1.1", "mimic-fn": "^2.0.0", "p-is-promise": "^2.0.0"}, "engines": {"node": ">=6"}}, "node_modules/npx/node_modules/mimic-fn": {"version": "2.1.0", "inBundle": true, "license": "MIT", "engines": {"node": ">=6"}}, "node_modules/npx/node_modules/minimatch": {"version": "3.0.4", "inBundle": true, "license": "ISC", "dependencies": {"brace-expansion": "^1.1.7"}, "engines": {"node": "*"}}, "node_modules/npx/node_modules/minimist": {"version": "1.2.0", "inBundle": true, "license": "MIT"}, "node_modules/npx/node_modules/nice-try": {"version": "1.0.5", "inBundle": true, "license": "MIT"}, "node_modules/npx/node_modules/npm": {"version": "5.1.0", "bundleDependencies": ["abbrev", "ansi-regex", "ansicolors", "ansistyles", "aproba", "archy", "cacache", "call-limit", "bluebird", "chownr", "cmd-shim", "columnify", "config-chain", "debuglog", "detect-indent", "dezalgo", "editor", "fs-vacuum", "fs-write-stream-atomic", "fstream", "fstream-npm", "glob", "graceful-fs", "has-unicode", "hosted-git-info", "iferr", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "inflight", "inherits", "ini", "init-package-json", "JSONStream", "lazy-property", "lockfile", "lodash._baseindexof", "lodash._baseuniq", "lodash._bindcallback", "lodash._cacheindexof", "lodash._createcache", "lodash._getnative", "lodash.clonedeep", "lodash.restparam", "lodash.union", "lodash.uniq", "lodash.without", "lru-cache", "mkdirp", "mississippi", "move-concurrently", "node-gyp", "nopt", "normalize-package-data", "npm-cache-filename", "npm-install-checks", "npm-package-arg", "npm-registry-client", "npm-user-validate", "npmlog", "once", "opener", "osenv", "pacote", "path-is-inside", "promise-inflight", "read", "read-cmd-shim", "read-installed", "read-package-json", "read-package-tree", "readable-stream", "readdir-scoped-modules", "request", "retry", "<PERSON><PERSON><PERSON>", "semver", "sha", "slide", "sorted-object", "sorted-union-stream", "ssri", "strip-ansi", "tar", "text-table", "uid-number", "umask", "unique-filename", "unpipe", "update-notifier", "uuid", "validate-npm-package-license", "validate-npm-package-name", "which", "wrappy", "write-file-atomic", "safe-buffer", "worker-farm"], "inBundle": true, "license": "Artistic-2.0", "dependencies": {"abbrev": "~1.1.0", "ansi-regex": "~3.0.0", "ansicolors": "~0.3.2", "ansistyles": "~0.1.3", "aproba": "~1.1.2", "archy": "~1.0.0", "bluebird": "~3.5.0", "cacache": "~9.2.9", "call-limit": "~1.1.0", "chownr": "~1.0.1", "cmd-shim": "~2.0.2", "columnify": "~1.5.4", "config-chain": "~1.1.11", "debuglog": "*", "detect-indent": "~5.0.0", "dezalgo": "~1.0.3", "editor": "~1.0.0", "fs-vacuum": "~1.2.10", "fs-write-stream-atomic": "~1.0.10", "fstream": "~1.0.11", "fstream-npm": "~1.2.1", "glob": "~7.1.2", "graceful-fs": "~4.1.11", "has-unicode": "~2.0.1", "hosted-git-info": "~2.5.0", "iferr": "~0.1.5", "imurmurhash": "*", "inflight": "~1.0.6", "inherits": "~2.0.3", "ini": "~1.3.4", "init-package-json": "~1.10.1", "JSONStream": "~1.3.1", "lazy-property": "~1.0.0", "lockfile": "~1.0.3", "lodash._baseindexof": "*", "lodash._baseuniq": "~4.6.0", "lodash._bindcallback": "*", "lodash._cacheindexof": "*", "lodash._createcache": "*", "lodash._getnative": "*", "lodash.clonedeep": "~4.5.0", "lodash.restparam": "*", "lodash.union": "~4.6.0", "lodash.uniq": "~4.5.0", "lodash.without": "~4.4.0", "lru-cache": "~4.1.1", "mississippi": "~1.3.0", "mkdirp": "~0.5.1", "move-concurrently": "~1.0.1", "node-gyp": "~3.6.2", "nopt": "~4.0.1", "normalize-package-data": "~2.4.0", "npm-cache-filename": "~1.0.2", "npm-install-checks": "~3.0.0", "npm-package-arg": "~5.1.2", "npm-registry-client": "~8.4.0", "npm-user-validate": "~1.0.0", "npmlog": "~4.1.2", "once": "~1.4.0", "opener": "~1.4.3", "osenv": "~0.1.4", "pacote": "~2.7.38", "path-is-inside": "~1.0.2", "promise-inflight": "~1.0.1", "read": "~1.0.7", "read-cmd-shim": "~1.0.1", "read-installed": "~4.0.3", "read-package-json": "~2.0.9", "read-package-tree": "~5.1.6", "readable-stream": "~2.3.2", "readdir-scoped-modules": "*", "request": "~2.81.0", "retry": "~0.10.1", "rimraf": "~2.6.1", "safe-buffer": "~5.1.1", "semver": "~5.3.0", "sha": "~2.0.1", "slide": "~1.1.6", "sorted-object": "~2.0.1", "sorted-union-stream": "~2.1.3", "ssri": "~4.1.6", "strip-ansi": "~4.0.0", "tar": "~2.2.1", "text-table": "~0.2.0", "uid-number": "0.0.6", "umask": "~1.1.0", "unique-filename": "~1.1.0", "unpipe": "~1.0.0", "update-notifier": "~2.2.0", "uuid": "~3.1.0", "validate-npm-package-license": "*", "validate-npm-package-name": "~3.0.0", "which": "~1.2.14", "worker-farm": "~1.3.1", "wrappy": "~1.0.2", "write-file-atomic": "~2.1.0"}, "bin": {"npm": "bin/npm-cli.js"}}, "node_modules/npx/node_modules/npm-package-arg": {"version": "6.1.1", "inBundle": true, "license": "ISC", "dependencies": {"hosted-git-info": "^2.7.1", "osenv": "^0.1.5", "semver": "^5.6.0", "validate-npm-package-name": "^3.0.0"}}, "node_modules/npx/node_modules/npm-run-path": {"version": "2.0.2", "inBundle": true, "license": "MIT", "dependencies": {"path-key": "^2.0.0"}, "engines": {"node": ">=4"}}, "node_modules/npx/node_modules/npm/node_modules/abbrev": {"version": "1.1.0", "inBundle": true, "license": "ISC"}, "node_modules/npx/node_modules/npm/node_modules/ansi-regex": {"version": "3.0.0", "inBundle": true, "license": "MIT", "engines": {"node": ">=4"}}, "node_modules/npx/node_modules/npm/node_modules/ansicolors": {"version": "0.3.2", "inBundle": true, "license": "MIT"}, "node_modules/npx/node_modules/npm/node_modules/ansistyles": {"version": "0.1.3", "inBundle": true, "license": "MIT"}, "node_modules/npx/node_modules/npm/node_modules/aproba": {"version": "1.1.2", "inBundle": true, "license": "ISC"}, "node_modules/npx/node_modules/npm/node_modules/archy": {"version": "1.0.0", "inBundle": true, "license": "MIT"}, "node_modules/npx/node_modules/npm/node_modules/bluebird": {"version": "3.5.0", "inBundle": true, "license": "MIT"}, "node_modules/npx/node_modules/npm/node_modules/cacache": {"version": "9.2.9", "inBundle": true, "license": "CC0-1.0", "dependencies": {"bluebird": "^3.5.0", "chownr": "^1.0.1", "glob": "^7.1.2", "graceful-fs": "^4.1.11", "lru-cache": "^4.1.1", "mississippi": "^1.3.0", "mkdirp": "^0.5.1", "move-concurrently": "^1.0.1", "promise-inflight": "^1.0.1", "rimraf": "^2.6.1", "ssri": "^4.1.6", "unique-filename": "^1.1.0", "y18n": "^3.2.1"}}, "node_modules/npx/node_modules/npm/node_modules/cacache/node_modules/lru-cache": {"version": "4.1.1", "inBundle": true, "license": "ISC", "dependencies": {"pseudomap": "^1.0.2", "yallist": "^2.1.2"}}, "node_modules/npx/node_modules/npm/node_modules/cacache/node_modules/lru-cache/node_modules/pseudomap": {"version": "1.0.2", "inBundle": true, "license": "ISC"}, "node_modules/npx/node_modules/npm/node_modules/cacache/node_modules/lru-cache/node_modules/yallist": {"version": "2.1.2", "inBundle": true, "license": "ISC"}, "node_modules/npx/node_modules/npm/node_modules/cacache/node_modules/y18n": {"version": "3.2.1", "inBundle": true, "license": "ISC"}, "node_modules/npx/node_modules/npm/node_modules/call-limit": {"version": "1.1.0", "inBundle": true, "license": "ISC"}, "node_modules/npx/node_modules/npm/node_modules/chownr": {"version": "1.0.1", "inBundle": true, "license": "ISC"}, "node_modules/npx/node_modules/npm/node_modules/cmd-shim": {"version": "2.0.2", "inBundle": true, "license": "BSD-2-<PERSON><PERSON>", "dependencies": {"graceful-fs": "^4.1.2", "mkdirp": "~0.5.0"}}, "node_modules/npx/node_modules/npm/node_modules/columnify": {"version": "1.5.4", "inBundle": true, "license": "MIT", "dependencies": {"strip-ansi": "^3.0.0", "wcwidth": "^1.0.0"}}, "node_modules/npx/node_modules/npm/node_modules/columnify/node_modules/strip-ansi": {"version": "3.0.1", "inBundle": true, "license": "MIT", "dependencies": {"ansi-regex": "^2.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/npx/node_modules/npm/node_modules/columnify/node_modules/strip-ansi/node_modules/ansi-regex": {"version": "2.1.1", "inBundle": true, "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/npx/node_modules/npm/node_modules/columnify/node_modules/wcwidth": {"version": "1.0.1", "inBundle": true, "license": "MIT", "dependencies": {"defaults": "^1.0.3"}}, "node_modules/npx/node_modules/npm/node_modules/columnify/node_modules/wcwidth/node_modules/defaults": {"version": "1.0.3", "inBundle": true, "license": "MIT", "dependencies": {"clone": "^1.0.2"}}, "node_modules/npx/node_modules/npm/node_modules/columnify/node_modules/wcwidth/node_modules/defaults/node_modules/clone": {"version": "1.0.2", "inBundle": true, "license": "MIT", "engines": {"node": ">=0.8"}}, "node_modules/npx/node_modules/npm/node_modules/config-chain": {"version": "1.1.11", "inBundle": true, "dependencies": {"ini": "^1.3.4", "proto-list": "~1.2.1"}}, "node_modules/npx/node_modules/npm/node_modules/config-chain/node_modules/proto-list": {"version": "1.2.4", "inBundle": true, "license": "ISC"}, "node_modules/npx/node_modules/npm/node_modules/debuglog": {"version": "1.0.1", "inBundle": true, "license": "MIT", "engines": {"node": "*"}}, "node_modules/npx/node_modules/npm/node_modules/detect-indent": {"version": "5.0.0", "inBundle": true, "license": "MIT", "engines": {"node": ">=4"}}, "node_modules/npx/node_modules/npm/node_modules/dezalgo": {"version": "1.0.3", "inBundle": true, "license": "ISC", "dependencies": {"asap": "^2.0.0", "wrappy": "1"}}, "node_modules/npx/node_modules/npm/node_modules/dezalgo/node_modules/asap": {"version": "2.0.5", "inBundle": true, "license": "MIT"}, "node_modules/npx/node_modules/npm/node_modules/editor": {"version": "1.0.0", "inBundle": true, "license": "MIT"}, "node_modules/npx/node_modules/npm/node_modules/fs-vacuum": {"version": "1.2.10", "inBundle": true, "license": "ISC", "dependencies": {"graceful-fs": "^4.1.2", "path-is-inside": "^1.0.1", "rimraf": "^2.5.2"}}, "node_modules/npx/node_modules/npm/node_modules/fs-write-stream-atomic": {"version": "1.0.10", "inBundle": true, "license": "ISC", "dependencies": {"graceful-fs": "^4.1.2", "iferr": "^0.1.5", "imurmurhash": "^0.1.4", "readable-stream": "1 || 2"}}, "node_modules/npx/node_modules/npm/node_modules/fstream": {"version": "1.0.11", "inBundle": true, "license": "ISC", "dependencies": {"graceful-fs": "^4.1.2", "inherits": "~2.0.0", "mkdirp": ">=0.5 0", "rimraf": "2"}, "engines": {"node": ">=0.6"}}, "node_modules/npx/node_modules/npm/node_modules/fstream-npm": {"version": "1.2.1", "inBundle": true, "license": "ISC", "dependencies": {"fstream-ignore": "^1.0.0", "inherits": "2"}}, "node_modules/npx/node_modules/npm/node_modules/fstream-npm/node_modules/fstream-ignore": {"version": "1.0.5", "inBundle": true, "license": "ISC", "dependencies": {"fstream": "^1.0.0", "inherits": "2", "minimatch": "^3.0.0"}}, "node_modules/npx/node_modules/npm/node_modules/fstream-npm/node_modules/fstream-ignore/node_modules/minimatch": {"version": "3.0.4", "inBundle": true, "license": "ISC", "dependencies": {"brace-expansion": "^1.1.7"}, "engines": {"node": "*"}}, "node_modules/npx/node_modules/npm/node_modules/fstream-npm/node_modules/fstream-ignore/node_modules/minimatch/node_modules/brace-expansion": {"version": "1.1.8", "inBundle": true, "license": "MIT", "dependencies": {"balanced-match": "^1.0.0", "concat-map": "0.0.1"}}, "node_modules/npx/node_modules/npm/node_modules/fstream-npm/node_modules/fstream-ignore/node_modules/minimatch/node_modules/brace-expansion/node_modules/balanced-match": {"version": "1.0.0", "inBundle": true, "license": "MIT"}, "node_modules/npx/node_modules/npm/node_modules/fstream-npm/node_modules/fstream-ignore/node_modules/minimatch/node_modules/brace-expansion/node_modules/concat-map": {"version": "0.0.1", "inBundle": true, "license": "MIT"}, "node_modules/npx/node_modules/npm/node_modules/glob": {"version": "7.1.2", "inBundle": true, "license": "ISC", "dependencies": {"fs.realpath": "^1.0.0", "inflight": "^1.0.4", "inherits": "2", "minimatch": "^3.0.4", "once": "^1.3.0", "path-is-absolute": "^1.0.0"}, "engines": {"node": "*"}}, "node_modules/npx/node_modules/npm/node_modules/glob/node_modules/fs.realpath": {"version": "1.0.0", "inBundle": true, "license": "ISC"}, "node_modules/npx/node_modules/npm/node_modules/glob/node_modules/minimatch": {"version": "3.0.4", "inBundle": true, "license": "ISC", "dependencies": {"brace-expansion": "^1.1.7"}, "engines": {"node": "*"}}, "node_modules/npx/node_modules/npm/node_modules/glob/node_modules/minimatch/node_modules/brace-expansion": {"version": "1.1.8", "inBundle": true, "license": "MIT", "dependencies": {"balanced-match": "^1.0.0", "concat-map": "0.0.1"}}, "node_modules/npx/node_modules/npm/node_modules/glob/node_modules/minimatch/node_modules/brace-expansion/node_modules/balanced-match": {"version": "1.0.0", "inBundle": true, "license": "MIT"}, "node_modules/npx/node_modules/npm/node_modules/glob/node_modules/minimatch/node_modules/brace-expansion/node_modules/concat-map": {"version": "0.0.1", "inBundle": true, "license": "MIT"}, "node_modules/npx/node_modules/npm/node_modules/glob/node_modules/path-is-absolute": {"version": "1.0.1", "inBundle": true, "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/npx/node_modules/npm/node_modules/graceful-fs": {"version": "4.1.11", "inBundle": true, "license": "ISC", "engines": {"node": ">=0.4.0"}}, "node_modules/npx/node_modules/npm/node_modules/has-unicode": {"version": "2.0.1", "inBundle": true, "license": "ISC"}, "node_modules/npx/node_modules/npm/node_modules/hosted-git-info": {"version": "2.5.0", "inBundle": true, "license": "ISC"}, "node_modules/npx/node_modules/npm/node_modules/iferr": {"version": "0.1.5", "inBundle": true, "license": "MIT"}, "node_modules/npx/node_modules/npm/node_modules/imurmurhash": {"version": "0.1.4", "inBundle": true, "license": "MIT", "engines": {"node": ">=0.8.19"}}, "node_modules/npx/node_modules/npm/node_modules/inflight": {"version": "1.0.6", "inBundle": true, "license": "ISC", "dependencies": {"once": "^1.3.0", "wrappy": "1"}}, "node_modules/npx/node_modules/npm/node_modules/inherits": {"version": "2.0.3", "inBundle": true, "license": "ISC"}, "node_modules/npx/node_modules/npm/node_modules/ini": {"version": "1.3.4", "inBundle": true, "license": "ISC", "engines": {"node": "*"}}, "node_modules/npx/node_modules/npm/node_modules/init-package-json": {"version": "1.10.1", "inBundle": true, "license": "ISC", "dependencies": {"glob": "^7.1.1", "npm-package-arg": "^4.0.0 || ^5.0.0", "promzard": "^0.3.0", "read": "~1.0.1", "read-package-json": "1 || 2", "semver": "2.x || 3.x || 4 || 5", "validate-npm-package-license": "^3.0.1", "validate-npm-package-name": "^3.0.0"}}, "node_modules/npx/node_modules/npm/node_modules/init-package-json/node_modules/promzard": {"version": "0.3.0", "inBundle": true, "license": "ISC", "dependencies": {"read": "1"}}, "node_modules/npx/node_modules/npm/node_modules/JSONStream": {"version": "1.3.1", "inBundle": true, "license": "(MIT OR Apache-2.0)", "dependencies": {"jsonparse": "^1.2.0", "through": ">=2.2.7 <3"}, "bin": {"JSONStream": "index.js"}, "engines": {"node": "*"}}, "node_modules/npx/node_modules/npm/node_modules/JSONStream/node_modules/jsonparse": {"version": "1.3.1", "engines": ["node >= 0.2.0"], "inBundle": true, "license": "MIT"}, "node_modules/npx/node_modules/npm/node_modules/JSONStream/node_modules/through": {"version": "2.3.8", "inBundle": true, "license": "MIT"}, "node_modules/npx/node_modules/npm/node_modules/lazy-property": {"version": "1.0.0", "inBundle": true, "license": "MIT"}, "node_modules/npx/node_modules/npm/node_modules/lockfile": {"version": "1.0.3", "inBundle": true, "license": "ISC"}, "node_modules/npx/node_modules/npm/node_modules/lodash._baseindexof": {"version": "3.1.0", "inBundle": true, "license": "MIT"}, "node_modules/npx/node_modules/npm/node_modules/lodash._baseuniq": {"version": "4.6.0", "inBundle": true, "license": "MIT", "dependencies": {"lodash._createset": "~4.0.0", "lodash._root": "~3.0.0"}}, "node_modules/npx/node_modules/npm/node_modules/lodash._baseuniq/node_modules/lodash._createset": {"version": "4.0.3", "inBundle": true, "license": "MIT"}, "node_modules/npx/node_modules/npm/node_modules/lodash._baseuniq/node_modules/lodash._root": {"version": "3.0.1", "inBundle": true, "license": "MIT"}, "node_modules/npx/node_modules/npm/node_modules/lodash._bindcallback": {"version": "3.0.1", "inBundle": true, "license": "MIT"}, "node_modules/npx/node_modules/npm/node_modules/lodash._cacheindexof": {"version": "3.0.2", "inBundle": true, "license": "MIT"}, "node_modules/npx/node_modules/npm/node_modules/lodash._createcache": {"version": "3.1.2", "inBundle": true, "license": "MIT", "dependencies": {"lodash._getnative": "^3.0.0"}}, "node_modules/npx/node_modules/npm/node_modules/lodash._getnative": {"version": "3.9.1", "inBundle": true, "license": "MIT"}, "node_modules/npx/node_modules/npm/node_modules/lodash.clonedeep": {"version": "4.5.0", "inBundle": true, "license": "MIT"}, "node_modules/npx/node_modules/npm/node_modules/lodash.restparam": {"version": "3.6.1", "inBundle": true, "license": "MIT"}, "node_modules/npx/node_modules/npm/node_modules/lodash.union": {"version": "4.6.0", "inBundle": true, "license": "MIT"}, "node_modules/npx/node_modules/npm/node_modules/lodash.uniq": {"version": "4.5.0", "inBundle": true, "license": "MIT"}, "node_modules/npx/node_modules/npm/node_modules/lodash.without": {"version": "4.4.0", "inBundle": true, "license": "MIT"}, "node_modules/npx/node_modules/npm/node_modules/lru-cache": {"version": "4.1.1", "inBundle": true, "license": "ISC", "dependencies": {"pseudomap": "^1.0.2", "yallist": "^2.1.2"}}, "node_modules/npx/node_modules/npm/node_modules/lru-cache/node_modules/pseudomap": {"version": "1.0.2", "inBundle": true, "license": "ISC"}, "node_modules/npx/node_modules/npm/node_modules/lru-cache/node_modules/yallist": {"version": "2.1.2", "inBundle": true, "license": "ISC"}, "node_modules/npx/node_modules/npm/node_modules/mississippi": {"version": "1.3.0", "inBundle": true, "license": "BSD-2-<PERSON><PERSON>", "dependencies": {"concat-stream": "^1.5.0", "duplexify": "^3.4.2", "end-of-stream": "^1.1.0", "flush-write-stream": "^1.0.0", "from2": "^2.1.0", "parallel-transform": "^1.1.0", "pump": "^1.0.0", "pumpify": "^1.3.3", "stream-each": "^1.1.0", "through2": "^2.0.0"}}, "node_modules/npx/node_modules/npm/node_modules/mississippi/node_modules/concat-stream": {"version": "1.6.0", "engines": ["node >= 0.8"], "inBundle": true, "license": "MIT", "dependencies": {"inherits": "^2.0.3", "readable-stream": "^2.2.2", "typedarray": "^0.0.6"}}, "node_modules/npx/node_modules/npm/node_modules/mississippi/node_modules/concat-stream/node_modules/typedarray": {"version": "0.0.6", "inBundle": true, "license": "MIT"}, "node_modules/npx/node_modules/npm/node_modules/mississippi/node_modules/duplexify": {"version": "3.5.0", "inBundle": true, "license": "MIT", "dependencies": {"end-of-stream": "1.0.0", "inherits": "^2.0.1", "readable-stream": "^2.0.0", "stream-shift": "^1.0.0"}}, "node_modules/npx/node_modules/npm/node_modules/mississippi/node_modules/duplexify/node_modules/end-of-stream": {"version": "1.0.0", "inBundle": true, "license": "MIT", "dependencies": {"once": "~1.3.0"}}, "node_modules/npx/node_modules/npm/node_modules/mississippi/node_modules/duplexify/node_modules/end-of-stream/node_modules/once": {"version": "1.3.3", "inBundle": true, "license": "ISC", "dependencies": {"wrappy": "1"}}, "node_modules/npx/node_modules/npm/node_modules/mississippi/node_modules/duplexify/node_modules/stream-shift": {"version": "1.0.0", "inBundle": true, "license": "MIT"}, "node_modules/npx/node_modules/npm/node_modules/mississippi/node_modules/end-of-stream": {"version": "1.4.0", "inBundle": true, "license": "MIT", "dependencies": {"once": "^1.4.0"}}, "node_modules/npx/node_modules/npm/node_modules/mississippi/node_modules/flush-write-stream": {"version": "1.0.2", "inBundle": true, "license": "MIT", "dependencies": {"inherits": "^2.0.1", "readable-stream": "^2.0.4"}}, "node_modules/npx/node_modules/npm/node_modules/mississippi/node_modules/from2": {"version": "2.3.0", "inBundle": true, "license": "MIT", "dependencies": {"inherits": "^2.0.1", "readable-stream": "^2.0.0"}}, "node_modules/npx/node_modules/npm/node_modules/mississippi/node_modules/parallel-transform": {"version": "1.1.0", "inBundle": true, "license": "MIT", "dependencies": {"cyclist": "~0.2.2", "inherits": "^2.0.3", "readable-stream": "^2.1.5"}}, "node_modules/npx/node_modules/npm/node_modules/mississippi/node_modules/parallel-transform/node_modules/cyclist": {"version": "0.2.2", "inBundle": true}, "node_modules/npx/node_modules/npm/node_modules/mississippi/node_modules/pump": {"version": "1.0.2", "inBundle": true, "license": "MIT", "dependencies": {"end-of-stream": "^1.1.0", "once": "^1.3.1"}}, "node_modules/npx/node_modules/npm/node_modules/mississippi/node_modules/pumpify": {"version": "1.3.5", "inBundle": true, "license": "MIT", "dependencies": {"duplexify": "^3.1.2", "inherits": "^2.0.1", "pump": "^1.0.0"}}, "node_modules/npx/node_modules/npm/node_modules/mississippi/node_modules/stream-each": {"version": "1.2.0", "inBundle": true, "license": "MIT", "dependencies": {"end-of-stream": "^1.1.0", "stream-shift": "^1.0.0"}}, "node_modules/npx/node_modules/npm/node_modules/mississippi/node_modules/stream-each/node_modules/stream-shift": {"version": "1.0.0", "inBundle": true, "license": "MIT"}, "node_modules/npx/node_modules/npm/node_modules/mississippi/node_modules/through2": {"version": "2.0.3", "inBundle": true, "license": "MIT", "dependencies": {"readable-stream": "^2.1.5", "xtend": "~4.0.1"}}, "node_modules/npx/node_modules/npm/node_modules/mississippi/node_modules/through2/node_modules/xtend": {"version": "4.0.1", "inBundle": true, "license": "MIT", "engines": {"node": ">=0.4"}}, "node_modules/npx/node_modules/npm/node_modules/mkdirp": {"version": "0.5.1", "inBundle": true, "license": "MIT", "dependencies": {"minimist": "0.0.8"}, "bin": {"mkdirp": "bin/cmd.js"}}, "node_modules/npx/node_modules/npm/node_modules/mkdirp/node_modules/minimist": {"version": "0.0.8", "inBundle": true, "license": "MIT"}, "node_modules/npx/node_modules/npm/node_modules/move-concurrently": {"version": "1.0.1", "inBundle": true, "license": "ISC", "dependencies": {"aproba": "^1.1.1", "copy-concurrently": "^1.0.0", "fs-write-stream-atomic": "^1.0.8", "mkdirp": "^0.5.1", "rimraf": "^2.5.4", "run-queue": "^1.0.3"}}, "node_modules/npx/node_modules/npm/node_modules/move-concurrently/node_modules/copy-concurrently": {"version": "1.0.3", "inBundle": true, "license": "ISC", "dependencies": {"aproba": "^1.1.1", "fs-write-stream-atomic": "^1.0.8", "iferr": "^0.1.5", "mkdirp": "^0.5.1", "rimraf": "^2.5.4", "run-queue": "^1.0.0"}}, "node_modules/npx/node_modules/npm/node_modules/move-concurrently/node_modules/run-queue": {"version": "1.0.3", "inBundle": true, "license": "ISC", "dependencies": {"aproba": "^1.1.1"}}, "node_modules/npx/node_modules/npm/node_modules/node-gyp": {"version": "3.6.2", "inBundle": true, "license": "MIT", "dependencies": {"fstream": "^1.0.0", "glob": "^7.0.3", "graceful-fs": "^4.1.2", "minimatch": "^3.0.2", "mkdirp": "^0.5.0", "nopt": "2 || 3", "npmlog": "0 || 1 || 2 || 3 || 4", "osenv": "0", "request": "2", "rimraf": "2", "semver": "~5.3.0", "tar": "^2.0.0", "which": "1"}, "bin": {"node-gyp": "bin/node-gyp.js"}, "engines": {"node": ">= 0.8.0"}}, "node_modules/npx/node_modules/npm/node_modules/node-gyp/node_modules/minimatch": {"version": "3.0.4", "inBundle": true, "license": "ISC", "dependencies": {"brace-expansion": "^1.1.7"}, "engines": {"node": "*"}}, "node_modules/npx/node_modules/npm/node_modules/node-gyp/node_modules/minimatch/node_modules/brace-expansion": {"version": "1.1.8", "inBundle": true, "license": "MIT", "dependencies": {"balanced-match": "^1.0.0", "concat-map": "0.0.1"}}, "node_modules/npx/node_modules/npm/node_modules/node-gyp/node_modules/minimatch/node_modules/brace-expansion/node_modules/balanced-match": {"version": "1.0.0", "inBundle": true, "license": "MIT"}, "node_modules/npx/node_modules/npm/node_modules/node-gyp/node_modules/minimatch/node_modules/brace-expansion/node_modules/concat-map": {"version": "0.0.1", "inBundle": true, "license": "MIT"}, "node_modules/npx/node_modules/npm/node_modules/node-gyp/node_modules/nopt": {"version": "3.0.6", "inBundle": true, "license": "ISC", "dependencies": {"abbrev": "1"}, "bin": {"nopt": "bin/nopt.js"}}, "node_modules/npx/node_modules/npm/node_modules/nopt": {"version": "4.0.1", "inBundle": true, "license": "ISC", "dependencies": {"abbrev": "1", "osenv": "^0.1.4"}, "bin": {"nopt": "bin/nopt.js"}}, "node_modules/npx/node_modules/npm/node_modules/normalize-package-data": {"version": "2.4.0", "inBundle": true, "license": "BSD-2-<PERSON><PERSON>", "dependencies": {"hosted-git-info": "^2.1.4", "is-builtin-module": "^1.0.0", "semver": "2 || 3 || 4 || 5", "validate-npm-package-license": "^3.0.1"}}, "node_modules/npx/node_modules/npm/node_modules/normalize-package-data/node_modules/is-builtin-module": {"version": "1.0.0", "inBundle": true, "license": "MIT", "dependencies": {"builtin-modules": "^1.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/npx/node_modules/npm/node_modules/normalize-package-data/node_modules/is-builtin-module/node_modules/builtin-modules": {"version": "1.1.1", "inBundle": true, "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/npx/node_modules/npm/node_modules/npm-cache-filename": {"version": "1.0.2", "inBundle": true, "license": "ISC"}, "node_modules/npx/node_modules/npm/node_modules/npm-install-checks": {"version": "3.0.0", "inBundle": true, "license": "BSD-2-<PERSON><PERSON>", "dependencies": {"semver": "^2.3.0 || 3.x || 4 || 5"}}, "node_modules/npx/node_modules/npm/node_modules/npm-package-arg": {"version": "5.1.2", "inBundle": true, "license": "ISC", "dependencies": {"hosted-git-info": "^2.4.2", "osenv": "^0.1.4", "semver": "^5.1.0", "validate-npm-package-name": "^3.0.0"}}, "node_modules/npx/node_modules/npm/node_modules/npm-registry-client": {"version": "8.4.0", "inBundle": true, "license": "ISC", "dependencies": {"concat-stream": "^1.5.2", "graceful-fs": "^4.1.6", "normalize-package-data": "~1.0.1 || ^2.0.0", "npm-package-arg": "^3.0.0 || ^4.0.0 || ^5.0.0", "once": "^1.3.3", "request": "^2.74.0", "retry": "^0.10.0", "semver": "2 >=2.2.1 || 3.x || 4 || 5", "slide": "^1.1.3", "ssri": "^4.1.2"}, "optionalDependencies": {"npmlog": "2 || ^3.1.0 || ^4.0.0"}}, "node_modules/npx/node_modules/npm/node_modules/npm-registry-client/node_modules/concat-stream": {"version": "1.6.0", "engines": ["node >= 0.8"], "inBundle": true, "license": "MIT", "dependencies": {"inherits": "^2.0.3", "readable-stream": "^2.2.2", "typedarray": "^0.0.6"}}, "node_modules/npx/node_modules/npm/node_modules/npm-registry-client/node_modules/concat-stream/node_modules/typedarray": {"version": "0.0.6", "inBundle": true, "license": "MIT"}, "node_modules/npx/node_modules/npm/node_modules/npm-user-validate": {"version": "1.0.0", "inBundle": true, "license": "BSD-2-<PERSON><PERSON>"}, "node_modules/npx/node_modules/npm/node_modules/npmlog": {"version": "4.1.2", "inBundle": true, "license": "ISC", "dependencies": {"are-we-there-yet": "~1.1.2", "console-control-strings": "~1.1.0", "gauge": "~2.7.3", "set-blocking": "~2.0.0"}}, "node_modules/npx/node_modules/npm/node_modules/npmlog/node_modules/are-we-there-yet": {"version": "1.1.4", "inBundle": true, "license": "ISC", "dependencies": {"delegates": "^1.0.0", "readable-stream": "^2.0.6"}}, "node_modules/npx/node_modules/npm/node_modules/npmlog/node_modules/are-we-there-yet/node_modules/delegates": {"version": "1.0.0", "inBundle": true, "license": "MIT"}, "node_modules/npx/node_modules/npm/node_modules/npmlog/node_modules/console-control-strings": {"version": "1.1.0", "inBundle": true, "license": "ISC"}, "node_modules/npx/node_modules/npm/node_modules/npmlog/node_modules/gauge": {"version": "2.7.4", "inBundle": true, "license": "ISC", "dependencies": {"aproba": "^1.0.3", "console-control-strings": "^1.0.0", "has-unicode": "^2.0.0", "object-assign": "^4.1.0", "signal-exit": "^3.0.0", "string-width": "^1.0.1", "strip-ansi": "^3.0.1", "wide-align": "^1.1.0"}}, "node_modules/npx/node_modules/npm/node_modules/npmlog/node_modules/gauge/node_modules/object-assign": {"version": "4.1.1", "inBundle": true, "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/npx/node_modules/npm/node_modules/npmlog/node_modules/gauge/node_modules/signal-exit": {"version": "3.0.2", "inBundle": true, "license": "ISC"}, "node_modules/npx/node_modules/npm/node_modules/npmlog/node_modules/gauge/node_modules/string-width": {"version": "1.0.2", "inBundle": true, "license": "MIT", "dependencies": {"code-point-at": "^1.0.0", "is-fullwidth-code-point": "^1.0.0", "strip-ansi": "^3.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/npx/node_modules/npm/node_modules/npmlog/node_modules/gauge/node_modules/string-width/node_modules/code-point-at": {"version": "1.1.0", "inBundle": true, "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/npx/node_modules/npm/node_modules/npmlog/node_modules/gauge/node_modules/string-width/node_modules/is-fullwidth-code-point": {"version": "1.0.0", "inBundle": true, "license": "MIT", "dependencies": {"number-is-nan": "^1.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/npx/node_modules/npm/node_modules/npmlog/node_modules/gauge/node_modules/string-width/node_modules/is-fullwidth-code-point/node_modules/number-is-nan": {"version": "1.0.1", "inBundle": true, "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/npx/node_modules/npm/node_modules/npmlog/node_modules/gauge/node_modules/strip-ansi": {"version": "3.0.1", "inBundle": true, "license": "MIT", "dependencies": {"ansi-regex": "^2.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/npx/node_modules/npm/node_modules/npmlog/node_modules/gauge/node_modules/strip-ansi/node_modules/ansi-regex": {"version": "2.1.1", "inBundle": true, "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/npx/node_modules/npm/node_modules/npmlog/node_modules/gauge/node_modules/wide-align": {"version": "1.1.2", "inBundle": true, "license": "ISC", "dependencies": {"string-width": "^1.0.2"}}, "node_modules/npx/node_modules/npm/node_modules/npmlog/node_modules/set-blocking": {"version": "2.0.0", "inBundle": true, "license": "ISC"}, "node_modules/npx/node_modules/npm/node_modules/once": {"version": "1.4.0", "inBundle": true, "license": "ISC", "dependencies": {"wrappy": "1"}}, "node_modules/npx/node_modules/npm/node_modules/opener": {"version": "1.4.3", "inBundle": true, "license": "(WTFPL OR MIT)", "bin": {"opener": "opener.js"}}, "node_modules/npx/node_modules/npm/node_modules/osenv": {"version": "0.1.4", "inBundle": true, "license": "ISC", "dependencies": {"os-homedir": "^1.0.0", "os-tmpdir": "^1.0.0"}}, "node_modules/npx/node_modules/npm/node_modules/osenv/node_modules/os-homedir": {"version": "1.0.2", "inBundle": true, "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/npx/node_modules/npm/node_modules/osenv/node_modules/os-tmpdir": {"version": "1.0.2", "inBundle": true, "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/npx/node_modules/npm/node_modules/pacote": {"version": "2.7.38", "inBundle": true, "license": "CC0-1.0", "dependencies": {"bluebird": "^3.5.0", "cacache": "^9.2.9", "glob": "^7.1.2", "lru-cache": "^4.1.1", "make-fetch-happen": "^2.4.13", "minimatch": "^3.0.4", "mississippi": "^1.2.0", "normalize-package-data": "^2.4.0", "npm-package-arg": "^5.1.2", "npm-pick-manifest": "^1.0.4", "osenv": "^0.1.4", "promise-inflight": "^1.0.1", "promise-retry": "^1.1.1", "protoduck": "^4.0.0", "safe-buffer": "^5.1.1", "semver": "^5.3.0", "ssri": "^4.1.6", "tar-fs": "^1.15.3", "tar-stream": "^1.5.4", "unique-filename": "^1.1.0", "which": "^1.2.12"}}, "node_modules/npx/node_modules/npm/node_modules/pacote/node_modules/make-fetch-happen": {"version": "2.4.13", "inBundle": true, "license": "CC0-1.0", "dependencies": {"agentkeepalive": "^3.3.0", "cacache": "^9.2.9", "http-cache-semantics": "^3.7.3", "http-proxy-agent": "^2.0.0", "https-proxy-agent": "^2.0.0", "lru-cache": "^4.1.1", "mississippi": "^1.2.0", "node-fetch-npm": "^2.0.1", "promise-retry": "^1.1.1", "socks-proxy-agent": "^3.0.0", "ssri": "^4.1.6"}}, "node_modules/npx/node_modules/npm/node_modules/pacote/node_modules/make-fetch-happen/node_modules/agentkeepalive": {"version": "3.3.0", "inBundle": true, "license": "MIT", "dependencies": {"humanize-ms": "^1.2.1"}, "engines": {"node": ">= 4.0.0"}}, "node_modules/npx/node_modules/npm/node_modules/pacote/node_modules/make-fetch-happen/node_modules/agentkeepalive/node_modules/humanize-ms": {"version": "1.2.1", "inBundle": true, "license": "MIT", "dependencies": {"ms": "^2.0.0"}}, "node_modules/npx/node_modules/npm/node_modules/pacote/node_modules/make-fetch-happen/node_modules/agentkeepalive/node_modules/humanize-ms/node_modules/ms": {"version": "2.0.0", "inBundle": true, "license": "MIT"}, "node_modules/npx/node_modules/npm/node_modules/pacote/node_modules/make-fetch-happen/node_modules/http-cache-semantics": {"version": "3.7.3", "inBundle": true, "license": "BSD-2-<PERSON><PERSON>"}, "node_modules/npx/node_modules/npm/node_modules/pacote/node_modules/make-fetch-happen/node_modules/http-proxy-agent": {"version": "2.0.0", "inBundle": true, "license": "MIT", "dependencies": {"agent-base": "4", "debug": "2"}}, "node_modules/npx/node_modules/npm/node_modules/pacote/node_modules/make-fetch-happen/node_modules/http-proxy-agent/node_modules/agent-base": {"version": "4.1.0", "inBundle": true, "license": "MIT", "dependencies": {"es6-promisify": "^5.0.0"}, "engines": {"node": ">= 4.0.0"}}, "node_modules/npx/node_modules/npm/node_modules/pacote/node_modules/make-fetch-happen/node_modules/http-proxy-agent/node_modules/agent-base/node_modules/es6-promisify": {"version": "5.0.0", "inBundle": true, "license": "MIT", "dependencies": {"es6-promise": "^4.0.3"}}, "node_modules/npx/node_modules/npm/node_modules/pacote/node_modules/make-fetch-happen/node_modules/http-proxy-agent/node_modules/agent-base/node_modules/es6-promisify/node_modules/es6-promise": {"version": "4.1.1", "inBundle": true, "license": "MIT"}, "node_modules/npx/node_modules/npm/node_modules/pacote/node_modules/make-fetch-happen/node_modules/http-proxy-agent/node_modules/debug": {"version": "2.6.8", "inBundle": true, "license": "MIT", "dependencies": {"ms": "2.0.0"}}, "node_modules/npx/node_modules/npm/node_modules/pacote/node_modules/make-fetch-happen/node_modules/http-proxy-agent/node_modules/debug/node_modules/ms": {"version": "2.0.0", "inBundle": true, "license": "MIT"}, "node_modules/npx/node_modules/npm/node_modules/pacote/node_modules/make-fetch-happen/node_modules/https-proxy-agent": {"version": "2.0.0", "inBundle": true, "license": "MIT", "dependencies": {"agent-base": "^4.1.0", "debug": "^2.4.1"}}, "node_modules/npx/node_modules/npm/node_modules/pacote/node_modules/make-fetch-happen/node_modules/https-proxy-agent/node_modules/agent-base": {"version": "4.1.0", "inBundle": true, "license": "MIT", "dependencies": {"es6-promisify": "^5.0.0"}, "engines": {"node": ">= 4.0.0"}}, "node_modules/npx/node_modules/npm/node_modules/pacote/node_modules/make-fetch-happen/node_modules/https-proxy-agent/node_modules/agent-base/node_modules/es6-promisify": {"version": "5.0.0", "inBundle": true, "license": "MIT", "dependencies": {"es6-promise": "^4.0.3"}}, "node_modules/npx/node_modules/npm/node_modules/pacote/node_modules/make-fetch-happen/node_modules/https-proxy-agent/node_modules/agent-base/node_modules/es6-promisify/node_modules/es6-promise": {"version": "4.1.1", "inBundle": true, "license": "MIT"}, "node_modules/npx/node_modules/npm/node_modules/pacote/node_modules/make-fetch-happen/node_modules/https-proxy-agent/node_modules/debug": {"version": "2.6.8", "inBundle": true, "license": "MIT", "dependencies": {"ms": "2.0.0"}}, "node_modules/npx/node_modules/npm/node_modules/pacote/node_modules/make-fetch-happen/node_modules/https-proxy-agent/node_modules/debug/node_modules/ms": {"version": "2.0.0", "inBundle": true, "license": "MIT"}, "node_modules/npx/node_modules/npm/node_modules/pacote/node_modules/make-fetch-happen/node_modules/node-fetch-npm": {"version": "2.0.1", "inBundle": true, "license": "MIT", "dependencies": {"encoding": "^0.1.11", "json-parse-helpfulerror": "^1.0.3", "safe-buffer": "^5.0.1"}, "engines": {"node": ">=4"}}, "node_modules/npx/node_modules/npm/node_modules/pacote/node_modules/make-fetch-happen/node_modules/node-fetch-npm/node_modules/encoding": {"version": "0.1.12", "inBundle": true, "license": "MIT", "dependencies": {"iconv-lite": "~0.4.13"}}, "node_modules/npx/node_modules/npm/node_modules/pacote/node_modules/make-fetch-happen/node_modules/node-fetch-npm/node_modules/encoding/node_modules/iconv-lite": {"version": "0.4.18", "inBundle": true, "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/npx/node_modules/npm/node_modules/pacote/node_modules/make-fetch-happen/node_modules/node-fetch-npm/node_modules/json-parse-helpfulerror": {"version": "1.0.3", "inBundle": true, "license": "MIT", "dependencies": {"jju": "^1.1.0"}}, "node_modules/npx/node_modules/npm/node_modules/pacote/node_modules/make-fetch-happen/node_modules/node-fetch-npm/node_modules/json-parse-helpfulerror/node_modules/jju": {"version": "1.3.0", "inBundle": true, "license": "WTFPL"}, "node_modules/npx/node_modules/npm/node_modules/pacote/node_modules/make-fetch-happen/node_modules/socks-proxy-agent": {"version": "3.0.0", "inBundle": true, "license": "MIT", "dependencies": {"agent-base": "^4.0.1", "socks": "^1.1.10"}}, "node_modules/npx/node_modules/npm/node_modules/pacote/node_modules/make-fetch-happen/node_modules/socks-proxy-agent/node_modules/agent-base": {"version": "4.1.0", "inBundle": true, "license": "MIT", "dependencies": {"es6-promisify": "^5.0.0"}, "engines": {"node": ">= 4.0.0"}}, "node_modules/npx/node_modules/npm/node_modules/pacote/node_modules/make-fetch-happen/node_modules/socks-proxy-agent/node_modules/agent-base/node_modules/es6-promisify": {"version": "5.0.0", "inBundle": true, "license": "MIT", "dependencies": {"es6-promise": "^4.0.3"}}, "node_modules/npx/node_modules/npm/node_modules/pacote/node_modules/make-fetch-happen/node_modules/socks-proxy-agent/node_modules/agent-base/node_modules/es6-promisify/node_modules/es6-promise": {"version": "4.1.1", "inBundle": true, "license": "MIT"}, "node_modules/npx/node_modules/npm/node_modules/pacote/node_modules/make-fetch-happen/node_modules/socks-proxy-agent/node_modules/socks": {"version": "1.1.10", "inBundle": true, "license": "MIT", "dependencies": {"ip": "^1.1.4", "smart-buffer": "^1.0.13"}, "engines": {"node": ">= 0.10.0", "npm": ">= 1.3.5"}}, "node_modules/npx/node_modules/npm/node_modules/pacote/node_modules/make-fetch-happen/node_modules/socks-proxy-agent/node_modules/socks/node_modules/ip": {"version": "1.1.5", "inBundle": true, "license": "MIT"}, "node_modules/npx/node_modules/npm/node_modules/pacote/node_modules/make-fetch-happen/node_modules/socks-proxy-agent/node_modules/socks/node_modules/smart-buffer": {"version": "1.1.15", "inBundle": true, "license": "MIT", "engines": {"node": ">= 0.10.15", "npm": ">= 1.3.5"}}, "node_modules/npx/node_modules/npm/node_modules/pacote/node_modules/minimatch": {"version": "3.0.4", "inBundle": true, "license": "ISC", "dependencies": {"brace-expansion": "^1.1.7"}, "engines": {"node": "*"}}, "node_modules/npx/node_modules/npm/node_modules/pacote/node_modules/minimatch/node_modules/brace-expansion": {"version": "1.1.8", "inBundle": true, "license": "MIT", "dependencies": {"balanced-match": "^1.0.0", "concat-map": "0.0.1"}}, "node_modules/npx/node_modules/npm/node_modules/pacote/node_modules/minimatch/node_modules/brace-expansion/node_modules/balanced-match": {"version": "1.0.0", "inBundle": true, "license": "MIT"}, "node_modules/npx/node_modules/npm/node_modules/pacote/node_modules/minimatch/node_modules/brace-expansion/node_modules/concat-map": {"version": "0.0.1", "inBundle": true, "license": "MIT"}, "node_modules/npx/node_modules/npm/node_modules/pacote/node_modules/npm-pick-manifest": {"version": "1.0.4", "inBundle": true, "license": "CC0-1.0", "dependencies": {"npm-package-arg": "^5.1.2", "semver": "^5.3.0"}}, "node_modules/npx/node_modules/npm/node_modules/pacote/node_modules/promise-retry": {"version": "1.1.1", "inBundle": true, "license": "MIT", "dependencies": {"err-code": "^1.0.0", "retry": "^0.10.0"}, "engines": {"node": ">=0.12"}}, "node_modules/npx/node_modules/npm/node_modules/pacote/node_modules/promise-retry/node_modules/err-code": {"version": "1.1.2", "inBundle": true, "license": "MIT"}, "node_modules/npx/node_modules/npm/node_modules/pacote/node_modules/protoduck": {"version": "4.0.0", "inBundle": true, "license": "CC0-1.0", "dependencies": {"genfun": "^4.0.1"}}, "node_modules/npx/node_modules/npm/node_modules/pacote/node_modules/protoduck/node_modules/genfun": {"version": "4.0.1", "inBundle": true, "license": "CC0-1.0"}, "node_modules/npx/node_modules/npm/node_modules/pacote/node_modules/tar-fs": {"version": "1.15.3", "inBundle": true, "license": "MIT", "dependencies": {"chownr": "^1.0.1", "mkdirp": "^0.5.1", "pump": "^1.0.0", "tar-stream": "^1.1.2"}}, "node_modules/npx/node_modules/npm/node_modules/pacote/node_modules/tar-fs/node_modules/pump": {"version": "1.0.2", "inBundle": true, "license": "MIT", "dependencies": {"end-of-stream": "^1.1.0", "once": "^1.3.1"}}, "node_modules/npx/node_modules/npm/node_modules/pacote/node_modules/tar-fs/node_modules/pump/node_modules/end-of-stream": {"version": "1.4.0", "inBundle": true, "license": "MIT", "dependencies": {"once": "^1.4.0"}}, "node_modules/npx/node_modules/npm/node_modules/pacote/node_modules/tar-stream": {"version": "1.5.4", "inBundle": true, "license": "MIT", "dependencies": {"bl": "^1.0.0", "end-of-stream": "^1.0.0", "readable-stream": "^2.0.0", "xtend": "^4.0.0"}, "engines": {"node": ">= 0.8.0"}}, "node_modules/npx/node_modules/npm/node_modules/pacote/node_modules/tar-stream/node_modules/bl": {"version": "1.2.1", "inBundle": true, "license": "MIT", "dependencies": {"readable-stream": "^2.0.5"}}, "node_modules/npx/node_modules/npm/node_modules/pacote/node_modules/tar-stream/node_modules/end-of-stream": {"version": "1.4.0", "inBundle": true, "license": "MIT", "dependencies": {"once": "^1.4.0"}}, "node_modules/npx/node_modules/npm/node_modules/pacote/node_modules/tar-stream/node_modules/xtend": {"version": "4.0.1", "inBundle": true, "license": "MIT", "engines": {"node": ">=0.4"}}, "node_modules/npx/node_modules/npm/node_modules/path-is-inside": {"version": "1.0.2", "inBundle": true, "license": "(WTFPL OR MIT)"}, "node_modules/npx/node_modules/npm/node_modules/promise-inflight": {"version": "1.0.1", "inBundle": true, "license": "ISC"}, "node_modules/npx/node_modules/npm/node_modules/read": {"version": "1.0.7", "inBundle": true, "license": "ISC", "dependencies": {"mute-stream": "~0.0.4"}, "engines": {"node": ">=0.8"}}, "node_modules/npx/node_modules/npm/node_modules/read-cmd-shim": {"version": "1.0.1", "inBundle": true, "license": "ISC", "dependencies": {"graceful-fs": "^4.1.2"}}, "node_modules/npx/node_modules/npm/node_modules/read-installed": {"version": "4.0.3", "inBundle": true, "license": "ISC", "dependencies": {"debuglog": "^1.0.1", "read-package-json": "^2.0.0", "readdir-scoped-modules": "^1.0.0", "semver": "2 || 3 || 4 || 5", "slide": "~1.1.3", "util-extend": "^1.0.1"}, "optionalDependencies": {"graceful-fs": "^4.1.2"}}, "node_modules/npx/node_modules/npm/node_modules/read-installed/node_modules/util-extend": {"version": "1.0.3", "inBundle": true, "license": "MIT"}, "node_modules/npx/node_modules/npm/node_modules/read-package-json": {"version": "2.0.9", "inBundle": true, "license": "ISC", "dependencies": {"glob": "^7.1.1", "json-parse-helpfulerror": "^1.0.2", "normalize-package-data": "^2.0.0"}, "optionalDependencies": {"graceful-fs": "^4.1.2"}}, "node_modules/npx/node_modules/npm/node_modules/read-package-json/node_modules/json-parse-helpfulerror": {"version": "1.0.3", "inBundle": true, "license": "MIT", "dependencies": {"jju": "^1.1.0"}}, "node_modules/npx/node_modules/npm/node_modules/read-package-json/node_modules/json-parse-helpfulerror/node_modules/jju": {"version": "1.3.0", "inBundle": true, "license": "WTFPL"}, "node_modules/npx/node_modules/npm/node_modules/read-package-tree": {"version": "5.1.6", "inBundle": true, "license": "ISC", "dependencies": {"debuglog": "^1.0.1", "dezalgo": "^1.0.0", "once": "^1.3.0", "read-package-json": "^2.0.0", "readdir-scoped-modules": "^1.0.0"}}, "node_modules/npx/node_modules/npm/node_modules/read/node_modules/mute-stream": {"version": "0.0.7", "inBundle": true, "license": "ISC"}, "node_modules/npx/node_modules/npm/node_modules/readable-stream": {"version": "2.3.2", "inBundle": true, "license": "MIT", "dependencies": {"core-util-is": "~1.0.0", "inherits": "~2.0.3", "isarray": "~1.0.0", "process-nextick-args": "~1.0.6", "safe-buffer": "~5.1.0", "string_decoder": "~1.0.0", "util-deprecate": "~1.0.1"}}, "node_modules/npx/node_modules/npm/node_modules/readable-stream/node_modules/core-util-is": {"version": "1.0.2", "inBundle": true, "license": "MIT"}, "node_modules/npx/node_modules/npm/node_modules/readable-stream/node_modules/isarray": {"version": "1.0.0", "inBundle": true, "license": "MIT"}, "node_modules/npx/node_modules/npm/node_modules/readable-stream/node_modules/process-nextick-args": {"version": "1.0.7", "inBundle": true, "license": "MIT"}, "node_modules/npx/node_modules/npm/node_modules/readable-stream/node_modules/string_decoder": {"version": "1.0.3", "inBundle": true, "license": "MIT", "dependencies": {"safe-buffer": "~5.1.0"}}, "node_modules/npx/node_modules/npm/node_modules/readable-stream/node_modules/util-deprecate": {"version": "1.0.2", "inBundle": true, "license": "MIT"}, "node_modules/npx/node_modules/npm/node_modules/readdir-scoped-modules": {"version": "1.0.2", "inBundle": true, "license": "ISC", "dependencies": {"debuglog": "^1.0.1", "dezalgo": "^1.0.0", "graceful-fs": "^4.1.2", "once": "^1.3.0"}}, "node_modules/npx/node_modules/npm/node_modules/request": {"version": "2.81.0", "inBundle": true, "license": "Apache-2.0", "dependencies": {"aws-sign2": "~0.6.0", "aws4": "^1.2.1", "caseless": "~0.12.0", "combined-stream": "~1.0.5", "extend": "~3.0.0", "forever-agent": "~0.6.1", "form-data": "~2.1.1", "har-validator": "~4.2.1", "hawk": "~3.1.3", "http-signature": "~1.1.0", "is-typedarray": "~1.0.0", "isstream": "~0.1.2", "json-stringify-safe": "~5.0.1", "mime-types": "~2.1.7", "oauth-sign": "~0.8.1", "performance-now": "^0.2.0", "qs": "~6.4.0", "safe-buffer": "^5.0.1", "stringstream": "~0.0.4", "tough-cookie": "~2.3.0", "tunnel-agent": "^0.6.0", "uuid": "^3.0.0"}, "engines": {"node": ">= 4"}}, "node_modules/npx/node_modules/npm/node_modules/request/node_modules/aws-sign2": {"version": "0.6.0", "inBundle": true, "license": "Apache-2.0", "engines": {"node": "*"}}, "node_modules/npx/node_modules/npm/node_modules/request/node_modules/aws4": {"version": "1.6.0", "inBundle": true, "license": "MIT"}, "node_modules/npx/node_modules/npm/node_modules/request/node_modules/caseless": {"version": "0.12.0", "inBundle": true, "license": "Apache-2.0"}, "node_modules/npx/node_modules/npm/node_modules/request/node_modules/combined-stream": {"version": "1.0.5", "inBundle": true, "license": "MIT", "dependencies": {"delayed-stream": "~1.0.0"}, "engines": {"node": ">= 0.8"}}, "node_modules/npx/node_modules/npm/node_modules/request/node_modules/combined-stream/node_modules/delayed-stream": {"version": "1.0.0", "inBundle": true, "license": "MIT", "engines": {"node": ">=0.4.0"}}, "node_modules/npx/node_modules/npm/node_modules/request/node_modules/extend": {"version": "3.0.1", "inBundle": true, "license": "MIT"}, "node_modules/npx/node_modules/npm/node_modules/request/node_modules/forever-agent": {"version": "0.6.1", "inBundle": true, "license": "Apache-2.0", "engines": {"node": "*"}}, "node_modules/npx/node_modules/npm/node_modules/request/node_modules/form-data": {"version": "2.1.4", "inBundle": true, "license": "MIT", "dependencies": {"asynckit": "^0.4.0", "combined-stream": "^1.0.5", "mime-types": "^2.1.12"}, "engines": {"node": ">= 0.12"}}, "node_modules/npx/node_modules/npm/node_modules/request/node_modules/form-data/node_modules/asynckit": {"version": "0.4.0", "inBundle": true, "license": "MIT"}, "node_modules/npx/node_modules/npm/node_modules/request/node_modules/har-validator": {"version": "4.2.1", "inBundle": true, "license": "ISC", "dependencies": {"ajv": "^4.9.1", "har-schema": "^1.0.5"}, "engines": {"node": ">=4"}}, "node_modules/npx/node_modules/npm/node_modules/request/node_modules/har-validator/node_modules/ajv": {"version": "4.11.8", "inBundle": true, "license": "MIT", "dependencies": {"co": "^4.6.0", "json-stable-stringify": "^1.0.1"}}, "node_modules/npx/node_modules/npm/node_modules/request/node_modules/har-validator/node_modules/ajv/node_modules/co": {"version": "4.6.0", "inBundle": true, "license": "MIT", "engines": {"iojs": ">= 1.0.0", "node": ">= 0.12.0"}}, "node_modules/npx/node_modules/npm/node_modules/request/node_modules/har-validator/node_modules/ajv/node_modules/json-stable-stringify": {"version": "1.0.1", "inBundle": true, "license": "MIT", "dependencies": {"jsonify": "~0.0.0"}}, "node_modules/npx/node_modules/npm/node_modules/request/node_modules/har-validator/node_modules/ajv/node_modules/json-stable-stringify/node_modules/jsonify": {"version": "0.0.0", "inBundle": true, "license": "Public Domain", "engines": {"node": "*"}}, "node_modules/npx/node_modules/npm/node_modules/request/node_modules/har-validator/node_modules/har-schema": {"version": "1.0.5", "inBundle": true, "license": "ISC", "engines": {"node": ">=4"}}, "node_modules/npx/node_modules/npm/node_modules/request/node_modules/hawk": {"version": "3.1.3", "inBundle": true, "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"boom": "2.x.x", "cryptiles": "2.x.x", "hoek": "2.x.x", "sntp": "1.x.x"}, "engines": {"node": ">=0.10.32"}}, "node_modules/npx/node_modules/npm/node_modules/request/node_modules/hawk/node_modules/boom": {"version": "2.10.1", "inBundle": true, "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"hoek": "2.x.x"}, "engines": {"node": ">=0.10.40"}}, "node_modules/npx/node_modules/npm/node_modules/request/node_modules/hawk/node_modules/cryptiles": {"version": "2.0.5", "inBundle": true, "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"boom": "2.x.x"}, "engines": {"node": ">=0.10.40"}}, "node_modules/npx/node_modules/npm/node_modules/request/node_modules/hawk/node_modules/hoek": {"version": "2.16.3", "inBundle": true, "license": "BSD-3-<PERSON><PERSON>", "engines": {"node": ">=0.10.40"}}, "node_modules/npx/node_modules/npm/node_modules/request/node_modules/hawk/node_modules/sntp": {"version": "1.0.9", "inBundle": true, "dependencies": {"hoek": "2.x.x"}, "engines": {"node": ">=0.8.0"}}, "node_modules/npx/node_modules/npm/node_modules/request/node_modules/http-signature": {"version": "1.1.1", "inBundle": true, "license": "MIT", "dependencies": {"assert-plus": "^0.2.0", "jsprim": "^1.2.2", "sshpk": "^1.7.0"}, "engines": {"node": ">=0.8", "npm": ">=1.3.7"}}, "node_modules/npx/node_modules/npm/node_modules/request/node_modules/http-signature/node_modules/assert-plus": {"version": "0.2.0", "inBundle": true, "license": "MIT", "engines": {"node": ">=0.8"}}, "node_modules/npx/node_modules/npm/node_modules/request/node_modules/http-signature/node_modules/jsprim": {"version": "1.4.0", "engines": ["node >=0.6.0"], "inBundle": true, "license": "MIT", "dependencies": {"assert-plus": "1.0.0", "extsprintf": "1.0.2", "json-schema": "0.2.3", "verror": "1.3.6"}}, "node_modules/npx/node_modules/npm/node_modules/request/node_modules/http-signature/node_modules/jsprim/node_modules/assert-plus": {"version": "1.0.0", "inBundle": true, "license": "MIT", "engines": {"node": ">=0.8"}}, "node_modules/npx/node_modules/npm/node_modules/request/node_modules/http-signature/node_modules/jsprim/node_modules/extsprintf": {"version": "1.0.2", "engines": ["node >=0.6.0"], "inBundle": true}, "node_modules/npx/node_modules/npm/node_modules/request/node_modules/http-signature/node_modules/jsprim/node_modules/json-schema": {"version": "0.2.3", "inBundle": true}, "node_modules/npx/node_modules/npm/node_modules/request/node_modules/http-signature/node_modules/jsprim/node_modules/verror": {"version": "1.3.6", "engines": ["node >=0.6.0"], "inBundle": true, "dependencies": {"extsprintf": "1.0.2"}}, "node_modules/npx/node_modules/npm/node_modules/request/node_modules/http-signature/node_modules/sshpk": {"version": "1.13.1", "inBundle": true, "license": "MIT", "dependencies": {"asn1": "~0.2.3", "assert-plus": "^1.0.0", "dashdash": "^1.12.0", "getpass": "^0.1.1"}, "bin": {"sshpk-conv": "bin/sshpk-conv", "sshpk-sign": "bin/sshpk-sign", "sshpk-verify": "bin/sshpk-verify"}, "engines": {"node": ">=0.10.0"}, "optionalDependencies": {"bcrypt-pbkdf": "^1.0.0", "ecc-jsbn": "~0.1.1", "jsbn": "~0.1.0", "tweetnacl": "~0.14.0"}}, "node_modules/npx/node_modules/npm/node_modules/request/node_modules/http-signature/node_modules/sshpk/node_modules/asn1": {"version": "0.2.3", "inBundle": true, "license": "MIT"}, "node_modules/npx/node_modules/npm/node_modules/request/node_modules/http-signature/node_modules/sshpk/node_modules/assert-plus": {"version": "1.0.0", "inBundle": true, "license": "MIT", "engines": {"node": ">=0.8"}}, "node_modules/npx/node_modules/npm/node_modules/request/node_modules/http-signature/node_modules/sshpk/node_modules/bcrypt-pbkdf": {"version": "1.0.1", "inBundle": true, "license": "BSD-3-<PERSON><PERSON>", "optional": true, "dependencies": {"tweetnacl": "^0.14.3"}}, "node_modules/npx/node_modules/npm/node_modules/request/node_modules/http-signature/node_modules/sshpk/node_modules/dashdash": {"version": "1.14.1", "inBundle": true, "license": "MIT", "dependencies": {"assert-plus": "^1.0.0"}, "engines": {"node": ">=0.10"}}, "node_modules/npx/node_modules/npm/node_modules/request/node_modules/http-signature/node_modules/sshpk/node_modules/ecc-jsbn": {"version": "0.1.1", "inBundle": true, "license": "MIT", "optional": true, "dependencies": {"jsbn": "~0.1.0"}}, "node_modules/npx/node_modules/npm/node_modules/request/node_modules/http-signature/node_modules/sshpk/node_modules/getpass": {"version": "0.1.7", "inBundle": true, "license": "MIT", "dependencies": {"assert-plus": "^1.0.0"}}, "node_modules/npx/node_modules/npm/node_modules/request/node_modules/http-signature/node_modules/sshpk/node_modules/jsbn": {"version": "0.1.1", "inBundle": true, "license": "MIT", "optional": true}, "node_modules/npx/node_modules/npm/node_modules/request/node_modules/http-signature/node_modules/sshpk/node_modules/tweetnacl": {"version": "0.14.5", "inBundle": true, "license": "Unlicense", "optional": true}, "node_modules/npx/node_modules/npm/node_modules/request/node_modules/is-typedarray": {"version": "1.0.0", "inBundle": true, "license": "MIT"}, "node_modules/npx/node_modules/npm/node_modules/request/node_modules/isstream": {"version": "0.1.2", "inBundle": true, "license": "MIT"}, "node_modules/npx/node_modules/npm/node_modules/request/node_modules/json-stringify-safe": {"version": "5.0.1", "inBundle": true, "license": "ISC"}, "node_modules/npx/node_modules/npm/node_modules/request/node_modules/mime-types": {"version": "2.1.15", "inBundle": true, "license": "MIT", "dependencies": {"mime-db": "~1.27.0"}, "engines": {"node": ">= 0.6"}}, "node_modules/npx/node_modules/npm/node_modules/request/node_modules/mime-types/node_modules/mime-db": {"version": "1.27.0", "inBundle": true, "license": "MIT", "engines": {"node": ">= 0.6"}}, "node_modules/npx/node_modules/npm/node_modules/request/node_modules/oauth-sign": {"version": "0.8.2", "inBundle": true, "license": "Apache-2.0", "engines": {"node": "*"}}, "node_modules/npx/node_modules/npm/node_modules/request/node_modules/performance-now": {"version": "0.2.0", "inBundle": true, "license": "MIT"}, "node_modules/npx/node_modules/npm/node_modules/request/node_modules/qs": {"version": "6.4.0", "inBundle": true, "license": "BSD-3-<PERSON><PERSON>", "engines": {"node": ">=0.6"}}, "node_modules/npx/node_modules/npm/node_modules/request/node_modules/stringstream": {"version": "0.0.5", "inBundle": true, "license": "MIT"}, "node_modules/npx/node_modules/npm/node_modules/request/node_modules/tough-cookie": {"version": "2.3.2", "inBundle": true, "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"punycode": "^1.4.1"}, "engines": {"node": ">=0.8"}}, "node_modules/npx/node_modules/npm/node_modules/request/node_modules/tough-cookie/node_modules/punycode": {"version": "1.4.1", "inBundle": true, "license": "MIT"}, "node_modules/npx/node_modules/npm/node_modules/request/node_modules/tunnel-agent": {"version": "0.6.0", "inBundle": true, "license": "Apache-2.0", "dependencies": {"safe-buffer": "^5.0.1"}, "engines": {"node": "*"}}, "node_modules/npx/node_modules/npm/node_modules/retry": {"version": "0.10.1", "inBundle": true, "license": "MIT", "engines": {"node": "*"}}, "node_modules/npx/node_modules/npm/node_modules/rimraf": {"version": "2.6.1", "inBundle": true, "license": "ISC", "dependencies": {"glob": "^7.0.5"}, "bin": {"rimraf": "bin.js"}}, "node_modules/npx/node_modules/npm/node_modules/safe-buffer": {"version": "5.1.1", "inBundle": true, "license": "MIT"}, "node_modules/npx/node_modules/npm/node_modules/semver": {"version": "5.3.0", "inBundle": true, "license": "ISC", "bin": {"semver": "bin/semver"}}, "node_modules/npx/node_modules/npm/node_modules/sha": {"version": "2.0.1", "inBundle": true, "license": "(BSD-2-<PERSON><PERSON> OR MIT)", "dependencies": {"graceful-fs": "^4.1.2", "readable-stream": "^2.0.2"}}, "node_modules/npx/node_modules/npm/node_modules/slide": {"version": "1.1.6", "inBundle": true, "license": "ISC", "engines": {"node": "*"}}, "node_modules/npx/node_modules/npm/node_modules/sorted-object": {"version": "2.0.1", "inBundle": true, "license": "(WTFPL OR MIT)"}, "node_modules/npx/node_modules/npm/node_modules/sorted-union-stream": {"version": "2.1.3", "inBundle": true, "license": "MIT", "dependencies": {"from2": "^1.3.0", "stream-iterate": "^1.1.0"}}, "node_modules/npx/node_modules/npm/node_modules/sorted-union-stream/node_modules/from2": {"version": "1.3.0", "inBundle": true, "license": "MIT", "dependencies": {"inherits": "~2.0.1", "readable-stream": "~1.1.10"}}, "node_modules/npx/node_modules/npm/node_modules/sorted-union-stream/node_modules/from2/node_modules/readable-stream": {"version": "1.1.14", "inBundle": true, "license": "MIT", "dependencies": {"core-util-is": "~1.0.0", "inherits": "~2.0.1", "isarray": "0.0.1", "string_decoder": "~0.10.x"}}, "node_modules/npx/node_modules/npm/node_modules/sorted-union-stream/node_modules/from2/node_modules/readable-stream/node_modules/core-util-is": {"version": "1.0.2", "inBundle": true, "license": "MIT"}, "node_modules/npx/node_modules/npm/node_modules/sorted-union-stream/node_modules/from2/node_modules/readable-stream/node_modules/isarray": {"version": "0.0.1", "inBundle": true, "license": "MIT"}, "node_modules/npx/node_modules/npm/node_modules/sorted-union-stream/node_modules/from2/node_modules/readable-stream/node_modules/string_decoder": {"version": "0.10.31", "inBundle": true, "license": "MIT"}, "node_modules/npx/node_modules/npm/node_modules/sorted-union-stream/node_modules/stream-iterate": {"version": "1.2.0", "inBundle": true, "license": "MIT", "dependencies": {"readable-stream": "^2.1.5", "stream-shift": "^1.0.0"}}, "node_modules/npx/node_modules/npm/node_modules/sorted-union-stream/node_modules/stream-iterate/node_modules/stream-shift": {"version": "1.0.0", "inBundle": true, "license": "MIT"}, "node_modules/npx/node_modules/npm/node_modules/ssri": {"version": "4.1.6", "inBundle": true, "license": "CC0-1.0", "dependencies": {"safe-buffer": "^5.1.0"}}, "node_modules/npx/node_modules/npm/node_modules/strip-ansi": {"version": "4.0.0", "inBundle": true, "license": "MIT", "dependencies": {"ansi-regex": "^3.0.0"}, "engines": {"node": ">=4"}}, "node_modules/npx/node_modules/npm/node_modules/strip-ansi/node_modules/ansi-regex": {"version": "3.0.0", "inBundle": true, "license": "MIT", "engines": {"node": ">=4"}}, "node_modules/npx/node_modules/npm/node_modules/tar": {"version": "2.2.1", "inBundle": true, "license": "ISC", "dependencies": {"block-stream": "*", "fstream": "^1.0.2", "inherits": "2"}}, "node_modules/npx/node_modules/npm/node_modules/tar/node_modules/block-stream": {"version": "0.0.9", "inBundle": true, "license": "ISC", "dependencies": {"inherits": "~2.0.0"}, "engines": {"node": "0.4 || >=0.5.8"}}, "node_modules/npx/node_modules/npm/node_modules/text-table": {"version": "0.2.0", "inBundle": true, "license": "MIT"}, "node_modules/npx/node_modules/npm/node_modules/uid-number": {"version": "0.0.6", "inBundle": true, "license": "ISC", "engines": {"node": "*"}}, "node_modules/npx/node_modules/npm/node_modules/umask": {"version": "1.1.0", "inBundle": true, "license": "MIT"}, "node_modules/npx/node_modules/npm/node_modules/unique-filename": {"version": "1.1.0", "inBundle": true, "license": "ISC", "dependencies": {"unique-slug": "^2.0.0"}}, "node_modules/npx/node_modules/npm/node_modules/unique-filename/node_modules/unique-slug": {"version": "2.0.0", "inBundle": true, "license": "ISC", "dependencies": {"imurmurhash": "^0.1.4"}}, "node_modules/npx/node_modules/npm/node_modules/unpipe": {"version": "1.0.0", "inBundle": true, "license": "MIT", "engines": {"node": ">= 0.8"}}, "node_modules/npx/node_modules/npm/node_modules/update-notifier": {"version": "2.2.0", "inBundle": true, "license": "BSD-2-<PERSON><PERSON>", "dependencies": {"boxen": "^1.0.0", "chalk": "^1.0.0", "configstore": "^3.0.0", "import-lazy": "^2.1.0", "is-npm": "^1.0.0", "latest-version": "^3.0.0", "semver-diff": "^2.0.0", "xdg-basedir": "^3.0.0"}, "engines": {"node": ">=4"}}, "node_modules/npx/node_modules/npm/node_modules/update-notifier/node_modules/boxen": {"version": "1.1.0", "inBundle": true, "license": "MIT", "dependencies": {"ansi-align": "^2.0.0", "camelcase": "^4.0.0", "chalk": "^1.1.1", "cli-boxes": "^1.0.0", "string-width": "^2.0.0", "term-size": "^0.1.0", "widest-line": "^1.0.0"}, "engines": {"node": ">=4"}}, "node_modules/npx/node_modules/npm/node_modules/update-notifier/node_modules/boxen/node_modules/ansi-align": {"version": "2.0.0", "inBundle": true, "license": "ISC", "dependencies": {"string-width": "^2.0.0"}}, "node_modules/npx/node_modules/npm/node_modules/update-notifier/node_modules/boxen/node_modules/camelcase": {"version": "4.1.0", "inBundle": true, "license": "MIT", "engines": {"node": ">=4"}}, "node_modules/npx/node_modules/npm/node_modules/update-notifier/node_modules/boxen/node_modules/cli-boxes": {"version": "1.0.0", "inBundle": true, "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/npx/node_modules/npm/node_modules/update-notifier/node_modules/boxen/node_modules/string-width": {"version": "2.1.0", "inBundle": true, "license": "MIT", "dependencies": {"is-fullwidth-code-point": "^2.0.0", "strip-ansi": "^4.0.0"}, "engines": {"node": ">=4"}}, "node_modules/npx/node_modules/npm/node_modules/update-notifier/node_modules/boxen/node_modules/string-width/node_modules/is-fullwidth-code-point": {"version": "2.0.0", "inBundle": true, "license": "MIT", "engines": {"node": ">=4"}}, "node_modules/npx/node_modules/npm/node_modules/update-notifier/node_modules/boxen/node_modules/string-width/node_modules/strip-ansi": {"version": "4.0.0", "inBundle": true, "license": "MIT", "dependencies": {"ansi-regex": "^3.0.0"}, "engines": {"node": ">=4"}}, "node_modules/npx/node_modules/npm/node_modules/update-notifier/node_modules/boxen/node_modules/term-size": {"version": "0.1.1", "inBundle": true, "license": "MIT", "dependencies": {"execa": "^0.4.0"}, "engines": {"node": ">=4"}}, "node_modules/npx/node_modules/npm/node_modules/update-notifier/node_modules/boxen/node_modules/term-size/node_modules/execa": {"version": "0.4.0", "inBundle": true, "license": "MIT", "dependencies": {"cross-spawn-async": "^2.1.1", "is-stream": "^1.1.0", "npm-run-path": "^1.0.0", "object-assign": "^4.0.1", "path-key": "^1.0.0", "strip-eof": "^1.0.0"}, "engines": {"node": ">=0.12"}}, "node_modules/npx/node_modules/npm/node_modules/update-notifier/node_modules/boxen/node_modules/term-size/node_modules/execa/node_modules/cross-spawn-async": {"version": "2.2.5", "inBundle": true, "license": "MIT", "dependencies": {"lru-cache": "^4.0.0", "which": "^1.2.8"}}, "node_modules/npx/node_modules/npm/node_modules/update-notifier/node_modules/boxen/node_modules/term-size/node_modules/execa/node_modules/is-stream": {"version": "1.1.0", "inBundle": true, "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/npx/node_modules/npm/node_modules/update-notifier/node_modules/boxen/node_modules/term-size/node_modules/execa/node_modules/npm-run-path": {"version": "1.0.0", "inBundle": true, "license": "MIT", "dependencies": {"path-key": "^1.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/npx/node_modules/npm/node_modules/update-notifier/node_modules/boxen/node_modules/term-size/node_modules/execa/node_modules/object-assign": {"version": "4.1.1", "inBundle": true, "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/npx/node_modules/npm/node_modules/update-notifier/node_modules/boxen/node_modules/term-size/node_modules/execa/node_modules/path-key": {"version": "1.0.0", "inBundle": true, "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/npx/node_modules/npm/node_modules/update-notifier/node_modules/boxen/node_modules/term-size/node_modules/execa/node_modules/strip-eof": {"version": "1.0.0", "inBundle": true, "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/npx/node_modules/npm/node_modules/update-notifier/node_modules/boxen/node_modules/widest-line": {"version": "1.0.0", "inBundle": true, "license": "MIT", "dependencies": {"string-width": "^1.0.1"}, "engines": {"node": ">=0.10.0"}}, "node_modules/npx/node_modules/npm/node_modules/update-notifier/node_modules/boxen/node_modules/widest-line/node_modules/string-width": {"version": "1.0.2", "inBundle": true, "license": "MIT", "dependencies": {"code-point-at": "^1.0.0", "is-fullwidth-code-point": "^1.0.0", "strip-ansi": "^3.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/npx/node_modules/npm/node_modules/update-notifier/node_modules/boxen/node_modules/widest-line/node_modules/string-width/node_modules/code-point-at": {"version": "1.1.0", "inBundle": true, "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/npx/node_modules/npm/node_modules/update-notifier/node_modules/boxen/node_modules/widest-line/node_modules/string-width/node_modules/is-fullwidth-code-point": {"version": "1.0.0", "inBundle": true, "license": "MIT", "dependencies": {"number-is-nan": "^1.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/npx/node_modules/npm/node_modules/update-notifier/node_modules/boxen/node_modules/widest-line/node_modules/string-width/node_modules/is-fullwidth-code-point/node_modules/number-is-nan": {"version": "1.0.1", "inBundle": true, "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/npx/node_modules/npm/node_modules/update-notifier/node_modules/boxen/node_modules/widest-line/node_modules/string-width/node_modules/strip-ansi": {"version": "3.0.1", "inBundle": true, "license": "MIT", "dependencies": {"ansi-regex": "^2.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/npx/node_modules/npm/node_modules/update-notifier/node_modules/boxen/node_modules/widest-line/node_modules/string-width/node_modules/strip-ansi/node_modules/ansi-regex": {"version": "2.1.1", "inBundle": true, "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/npx/node_modules/npm/node_modules/update-notifier/node_modules/chalk": {"version": "1.1.3", "inBundle": true, "license": "MIT", "dependencies": {"ansi-styles": "^2.2.1", "escape-string-regexp": "^1.0.2", "has-ansi": "^2.0.0", "strip-ansi": "^3.0.0", "supports-color": "^2.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/npx/node_modules/npm/node_modules/update-notifier/node_modules/chalk/node_modules/ansi-styles": {"version": "2.2.1", "inBundle": true, "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/npx/node_modules/npm/node_modules/update-notifier/node_modules/chalk/node_modules/escape-string-regexp": {"version": "1.0.5", "inBundle": true, "license": "MIT", "engines": {"node": ">=0.8.0"}}, "node_modules/npx/node_modules/npm/node_modules/update-notifier/node_modules/chalk/node_modules/has-ansi": {"version": "2.0.0", "inBundle": true, "license": "MIT", "dependencies": {"ansi-regex": "^2.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/npx/node_modules/npm/node_modules/update-notifier/node_modules/chalk/node_modules/has-ansi/node_modules/ansi-regex": {"version": "2.1.1", "inBundle": true, "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/npx/node_modules/npm/node_modules/update-notifier/node_modules/chalk/node_modules/strip-ansi": {"version": "3.0.1", "inBundle": true, "license": "MIT", "dependencies": {"ansi-regex": "^2.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/npx/node_modules/npm/node_modules/update-notifier/node_modules/chalk/node_modules/strip-ansi/node_modules/ansi-regex": {"version": "2.1.1", "inBundle": true, "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/npx/node_modules/npm/node_modules/update-notifier/node_modules/chalk/node_modules/supports-color": {"version": "2.0.0", "inBundle": true, "license": "MIT", "engines": {"node": ">=0.8.0"}}, "node_modules/npx/node_modules/npm/node_modules/update-notifier/node_modules/configstore": {"version": "3.1.0", "inBundle": true, "license": "BSD-2-<PERSON><PERSON>", "dependencies": {"dot-prop": "^4.1.0", "graceful-fs": "^4.1.2", "make-dir": "^1.0.0", "unique-string": "^1.0.0", "write-file-atomic": "^2.0.0", "xdg-basedir": "^3.0.0"}, "engines": {"node": ">=4"}}, "node_modules/npx/node_modules/npm/node_modules/update-notifier/node_modules/configstore/node_modules/dot-prop": {"version": "4.1.1", "inBundle": true, "license": "MIT", "dependencies": {"is-obj": "^1.0.0"}, "engines": {"node": ">=4"}}, "node_modules/npx/node_modules/npm/node_modules/update-notifier/node_modules/configstore/node_modules/dot-prop/node_modules/is-obj": {"version": "1.0.1", "inBundle": true, "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/npx/node_modules/npm/node_modules/update-notifier/node_modules/configstore/node_modules/make-dir": {"version": "1.0.0", "inBundle": true, "license": "MIT", "dependencies": {"pify": "^2.3.0"}, "engines": {"node": ">=4"}}, "node_modules/npx/node_modules/npm/node_modules/update-notifier/node_modules/configstore/node_modules/make-dir/node_modules/pify": {"version": "2.3.0", "inBundle": true, "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/npx/node_modules/npm/node_modules/update-notifier/node_modules/configstore/node_modules/unique-string": {"version": "1.0.0", "inBundle": true, "license": "MIT", "dependencies": {"crypto-random-string": "^1.0.0"}, "engines": {"node": ">=4"}}, "node_modules/npx/node_modules/npm/node_modules/update-notifier/node_modules/configstore/node_modules/unique-string/node_modules/crypto-random-string": {"version": "1.0.0", "inBundle": true, "license": "MIT", "engines": {"node": ">=4"}}, "node_modules/npx/node_modules/npm/node_modules/update-notifier/node_modules/import-lazy": {"version": "2.1.0", "inBundle": true, "license": "MIT", "engines": {"node": ">=4"}}, "node_modules/npx/node_modules/npm/node_modules/update-notifier/node_modules/is-npm": {"version": "1.0.0", "inBundle": true, "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/npx/node_modules/npm/node_modules/update-notifier/node_modules/latest-version": {"version": "3.1.0", "inBundle": true, "license": "MIT", "dependencies": {"package-json": "^4.0.0"}, "engines": {"node": ">=4"}}, "node_modules/npx/node_modules/npm/node_modules/update-notifier/node_modules/latest-version/node_modules/package-json": {"version": "4.0.1", "inBundle": true, "license": "MIT", "dependencies": {"got": "^6.7.1", "registry-auth-token": "^3.0.1", "registry-url": "^3.0.3", "semver": "^5.1.0"}, "engines": {"node": ">=4"}}, "node_modules/npx/node_modules/npm/node_modules/update-notifier/node_modules/latest-version/node_modules/package-json/node_modules/got": {"version": "6.7.1", "inBundle": true, "license": "MIT", "dependencies": {"create-error-class": "^3.0.0", "duplexer3": "^0.1.4", "get-stream": "^3.0.0", "is-redirect": "^1.0.0", "is-retry-allowed": "^1.0.0", "is-stream": "^1.0.0", "lowercase-keys": "^1.0.0", "safe-buffer": "^5.0.1", "timed-out": "^4.0.0", "unzip-response": "^2.0.1", "url-parse-lax": "^1.0.0"}, "engines": {"node": ">=4"}}, "node_modules/npx/node_modules/npm/node_modules/update-notifier/node_modules/latest-version/node_modules/package-json/node_modules/got/node_modules/create-error-class": {"version": "3.0.2", "inBundle": true, "license": "MIT", "dependencies": {"capture-stack-trace": "^1.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/npx/node_modules/npm/node_modules/update-notifier/node_modules/latest-version/node_modules/package-json/node_modules/got/node_modules/create-error-class/node_modules/capture-stack-trace": {"version": "1.0.0", "inBundle": true, "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/npx/node_modules/npm/node_modules/update-notifier/node_modules/latest-version/node_modules/package-json/node_modules/got/node_modules/duplexer3": {"version": "0.1.4", "inBundle": true, "license": "BSD-3-<PERSON><PERSON>"}, "node_modules/npx/node_modules/npm/node_modules/update-notifier/node_modules/latest-version/node_modules/package-json/node_modules/got/node_modules/get-stream": {"version": "3.0.0", "inBundle": true, "license": "MIT", "engines": {"node": ">=4"}}, "node_modules/npx/node_modules/npm/node_modules/update-notifier/node_modules/latest-version/node_modules/package-json/node_modules/got/node_modules/is-redirect": {"version": "1.0.0", "inBundle": true, "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/npx/node_modules/npm/node_modules/update-notifier/node_modules/latest-version/node_modules/package-json/node_modules/got/node_modules/is-retry-allowed": {"version": "1.1.0", "inBundle": true, "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/npx/node_modules/npm/node_modules/update-notifier/node_modules/latest-version/node_modules/package-json/node_modules/got/node_modules/is-stream": {"version": "1.1.0", "inBundle": true, "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/npx/node_modules/npm/node_modules/update-notifier/node_modules/latest-version/node_modules/package-json/node_modules/got/node_modules/lowercase-keys": {"version": "1.0.0", "inBundle": true, "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/npx/node_modules/npm/node_modules/update-notifier/node_modules/latest-version/node_modules/package-json/node_modules/got/node_modules/timed-out": {"version": "4.0.1", "inBundle": true, "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/npx/node_modules/npm/node_modules/update-notifier/node_modules/latest-version/node_modules/package-json/node_modules/got/node_modules/unzip-response": {"version": "2.0.1", "inBundle": true, "license": "MIT", "engines": {"node": ">=4"}}, "node_modules/npx/node_modules/npm/node_modules/update-notifier/node_modules/latest-version/node_modules/package-json/node_modules/got/node_modules/url-parse-lax": {"version": "1.0.0", "inBundle": true, "license": "MIT", "dependencies": {"prepend-http": "^1.0.1"}, "engines": {"node": ">=0.10.0"}}, "node_modules/npx/node_modules/npm/node_modules/update-notifier/node_modules/latest-version/node_modules/package-json/node_modules/got/node_modules/url-parse-lax/node_modules/prepend-http": {"version": "1.0.4", "inBundle": true, "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/npx/node_modules/npm/node_modules/update-notifier/node_modules/latest-version/node_modules/package-json/node_modules/registry-auth-token": {"version": "3.3.1", "inBundle": true, "license": "MIT", "dependencies": {"rc": "^1.1.6", "safe-buffer": "^5.0.1"}}, "node_modules/npx/node_modules/npm/node_modules/update-notifier/node_modules/latest-version/node_modules/package-json/node_modules/registry-auth-token/node_modules/rc": {"version": "1.2.1", "inBundle": true, "license": "(BSD-2-<PERSON><PERSON> OR MIT OR Apache-2.0)", "dependencies": {"deep-extend": "~0.4.0", "ini": "~1.3.0", "minimist": "^1.2.0", "strip-json-comments": "~2.0.1"}, "bin": {"rc": "index.js"}}, "node_modules/npx/node_modules/npm/node_modules/update-notifier/node_modules/latest-version/node_modules/package-json/node_modules/registry-auth-token/node_modules/rc/node_modules/deep-extend": {"version": "0.4.2", "inBundle": true, "license": "MIT", "engines": {"iojs": ">=1.0.0", "node": ">=0.12.0"}}, "node_modules/npx/node_modules/npm/node_modules/update-notifier/node_modules/latest-version/node_modules/package-json/node_modules/registry-auth-token/node_modules/rc/node_modules/minimist": {"version": "1.2.0", "inBundle": true, "license": "MIT"}, "node_modules/npx/node_modules/npm/node_modules/update-notifier/node_modules/latest-version/node_modules/package-json/node_modules/registry-auth-token/node_modules/rc/node_modules/strip-json-comments": {"version": "2.0.1", "inBundle": true, "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/npx/node_modules/npm/node_modules/update-notifier/node_modules/latest-version/node_modules/package-json/node_modules/registry-url": {"version": "3.1.0", "inBundle": true, "license": "MIT", "dependencies": {"rc": "^1.0.1"}, "engines": {"node": ">=0.10.0"}}, "node_modules/npx/node_modules/npm/node_modules/update-notifier/node_modules/latest-version/node_modules/package-json/node_modules/registry-url/node_modules/rc": {"version": "1.2.1", "inBundle": true, "license": "(BSD-2-<PERSON><PERSON> OR MIT OR Apache-2.0)", "dependencies": {"deep-extend": "~0.4.0", "ini": "~1.3.0", "minimist": "^1.2.0", "strip-json-comments": "~2.0.1"}, "bin": {"rc": "index.js"}}, "node_modules/npx/node_modules/npm/node_modules/update-notifier/node_modules/latest-version/node_modules/package-json/node_modules/registry-url/node_modules/rc/node_modules/deep-extend": {"version": "0.4.2", "inBundle": true, "license": "MIT", "engines": {"iojs": ">=1.0.0", "node": ">=0.12.0"}}, "node_modules/npx/node_modules/npm/node_modules/update-notifier/node_modules/latest-version/node_modules/package-json/node_modules/registry-url/node_modules/rc/node_modules/minimist": {"version": "1.2.0", "inBundle": true, "license": "MIT"}, "node_modules/npx/node_modules/npm/node_modules/update-notifier/node_modules/latest-version/node_modules/package-json/node_modules/registry-url/node_modules/rc/node_modules/strip-json-comments": {"version": "2.0.1", "inBundle": true, "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/npx/node_modules/npm/node_modules/update-notifier/node_modules/semver-diff": {"version": "2.1.0", "inBundle": true, "license": "MIT", "dependencies": {"semver": "^5.0.3"}, "engines": {"node": ">=0.10.0"}}, "node_modules/npx/node_modules/npm/node_modules/update-notifier/node_modules/xdg-basedir": {"version": "3.0.0", "inBundle": true, "license": "MIT", "engines": {"node": ">=4"}}, "node_modules/npx/node_modules/npm/node_modules/uuid": {"version": "3.1.0", "inBundle": true, "license": "MIT", "bin": {"uuid": "bin/uuid"}}, "node_modules/npx/node_modules/npm/node_modules/validate-npm-package-license": {"version": "3.0.1", "inBundle": true, "license": "Apache-2.0", "dependencies": {"spdx-correct": "~1.0.0", "spdx-expression-parse": "~1.0.0"}}, "node_modules/npx/node_modules/npm/node_modules/validate-npm-package-license/node_modules/spdx-correct": {"version": "1.0.2", "inBundle": true, "license": "Apache-2.0", "dependencies": {"spdx-license-ids": "^1.0.2"}}, "node_modules/npx/node_modules/npm/node_modules/validate-npm-package-license/node_modules/spdx-correct/node_modules/spdx-license-ids": {"version": "1.2.2", "inBundle": true, "license": "Unlicense"}, "node_modules/npx/node_modules/npm/node_modules/validate-npm-package-license/node_modules/spdx-expression-parse": {"version": "1.0.4", "inBundle": true, "license": "(MIT AND CC-BY-3.0)"}, "node_modules/npx/node_modules/npm/node_modules/validate-npm-package-name": {"version": "3.0.0", "inBundle": true, "license": "ISC", "dependencies": {"builtins": "^1.0.3"}}, "node_modules/npx/node_modules/npm/node_modules/validate-npm-package-name/node_modules/builtins": {"version": "1.0.3", "inBundle": true, "license": "MIT"}, "node_modules/npx/node_modules/npm/node_modules/which": {"version": "1.2.14", "inBundle": true, "license": "ISC", "dependencies": {"isexe": "^2.0.0"}, "bin": {"which": "bin/which"}}, "node_modules/npx/node_modules/npm/node_modules/which/node_modules/isexe": {"version": "2.0.0", "inBundle": true, "license": "ISC"}, "node_modules/npx/node_modules/npm/node_modules/worker-farm": {"version": "1.3.1", "inBundle": true, "license": "MIT", "dependencies": {"errno": ">=0.1.1 <0.2.0-0", "xtend": ">=4.0.0 <4.1.0-0"}}, "node_modules/npx/node_modules/npm/node_modules/worker-farm/node_modules/errno": {"version": "0.1.4", "inBundle": true, "license": "MIT", "dependencies": {"prr": "~0.0.0"}, "bin": {"errno": "cli.js"}}, "node_modules/npx/node_modules/npm/node_modules/worker-farm/node_modules/errno/node_modules/prr": {"version": "0.0.0", "inBundle": true, "license": "MIT"}, "node_modules/npx/node_modules/npm/node_modules/worker-farm/node_modules/xtend": {"version": "4.0.1", "inBundle": true, "engines": {"node": ">=0.4"}}, "node_modules/npx/node_modules/npm/node_modules/wrappy": {"version": "1.0.2", "inBundle": true, "license": "ISC"}, "node_modules/npx/node_modules/npm/node_modules/write-file-atomic": {"version": "2.1.0", "inBundle": true, "license": "ISC", "dependencies": {"graceful-fs": "^4.1.11", "imurmurhash": "^0.1.4", "slide": "^1.1.5"}}, "node_modules/npx/node_modules/number-is-nan": {"version": "1.0.1", "inBundle": true, "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/npx/node_modules/once": {"version": "1.4.0", "inBundle": true, "license": "ISC", "dependencies": {"wrappy": "1"}}, "node_modules/npx/node_modules/os-homedir": {"version": "1.0.2", "inBundle": true, "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/npx/node_modules/os-locale": {"version": "3.1.0", "inBundle": true, "license": "MIT", "dependencies": {"execa": "^1.0.0", "lcid": "^2.0.0", "mem": "^4.0.0"}, "engines": {"node": ">=6"}}, "node_modules/npx/node_modules/os-locale/node_modules/cross-spawn": {"version": "6.0.5", "inBundle": true, "license": "MIT", "dependencies": {"nice-try": "^1.0.4", "path-key": "^2.0.1", "semver": "^5.5.0", "shebang-command": "^1.2.0", "which": "^1.2.9"}, "engines": {"node": ">=4.8"}}, "node_modules/npx/node_modules/os-locale/node_modules/execa": {"version": "1.0.0", "inBundle": true, "license": "MIT", "dependencies": {"cross-spawn": "^6.0.0", "get-stream": "^4.0.0", "is-stream": "^1.1.0", "npm-run-path": "^2.0.0", "p-finally": "^1.0.0", "signal-exit": "^3.0.0", "strip-eof": "^1.0.0"}, "engines": {"node": ">=6"}}, "node_modules/npx/node_modules/os-locale/node_modules/get-stream": {"version": "4.1.0", "inBundle": true, "license": "MIT", "dependencies": {"pump": "^3.0.0"}, "engines": {"node": ">=6"}}, "node_modules/npx/node_modules/os-tmpdir": {"version": "1.0.2", "inBundle": true, "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/npx/node_modules/osenv": {"version": "0.1.5", "inBundle": true, "license": "ISC", "dependencies": {"os-homedir": "^1.0.0", "os-tmpdir": "^1.0.0"}}, "node_modules/npx/node_modules/p-defer": {"version": "1.0.0", "inBundle": true, "license": "MIT", "engines": {"node": ">=4"}}, "node_modules/npx/node_modules/p-finally": {"version": "1.0.0", "inBundle": true, "license": "MIT", "engines": {"node": ">=4"}}, "node_modules/npx/node_modules/p-is-promise": {"version": "2.1.0", "inBundle": true, "license": "MIT", "engines": {"node": ">=6"}}, "node_modules/npx/node_modules/p-limit": {"version": "1.3.0", "inBundle": true, "license": "MIT", "dependencies": {"p-try": "^1.0.0"}, "engines": {"node": ">=4"}}, "node_modules/npx/node_modules/p-locate": {"version": "2.0.0", "inBundle": true, "license": "MIT", "dependencies": {"p-limit": "^1.1.0"}, "engines": {"node": ">=4"}}, "node_modules/npx/node_modules/p-try": {"version": "1.0.0", "inBundle": true, "license": "MIT", "engines": {"node": ">=4"}}, "node_modules/npx/node_modules/package-json": {"version": "4.0.1", "inBundle": true, "license": "MIT", "dependencies": {"got": "^6.7.1", "registry-auth-token": "^3.0.1", "registry-url": "^3.0.3", "semver": "^5.1.0"}, "engines": {"node": ">=4"}}, "node_modules/npx/node_modules/path-exists": {"version": "3.0.0", "inBundle": true, "license": "MIT", "engines": {"node": ">=4"}}, "node_modules/npx/node_modules/path-is-absolute": {"version": "1.0.1", "inBundle": true, "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/npx/node_modules/path-is-inside": {"version": "1.0.2", "inBundle": true, "license": "(WTFPL OR MIT)"}, "node_modules/npx/node_modules/path-key": {"version": "2.0.1", "inBundle": true, "license": "MIT", "engines": {"node": ">=4"}}, "node_modules/npx/node_modules/pify": {"version": "3.0.0", "inBundle": true, "license": "MIT", "engines": {"node": ">=4"}}, "node_modules/npx/node_modules/prepend-http": {"version": "1.0.4", "inBundle": true, "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/npx/node_modules/pseudomap": {"version": "1.0.2", "inBundle": true, "license": "ISC"}, "node_modules/npx/node_modules/pump": {"version": "3.0.0", "inBundle": true, "license": "MIT", "dependencies": {"end-of-stream": "^1.1.0", "once": "^1.3.1"}}, "node_modules/npx/node_modules/rc": {"version": "1.2.8", "inBundle": true, "license": "(BSD-2-<PERSON><PERSON> OR MIT OR Apache-2.0)", "dependencies": {"deep-extend": "^0.6.0", "ini": "~1.3.0", "minimist": "^1.2.0", "strip-json-comments": "~2.0.1"}, "bin": {"rc": "cli.js"}}, "node_modules/npx/node_modules/registry-auth-token": {"version": "3.4.0", "inBundle": true, "license": "MIT", "dependencies": {"rc": "^1.1.6", "safe-buffer": "^5.0.1"}}, "node_modules/npx/node_modules/registry-url": {"version": "3.1.0", "inBundle": true, "license": "MIT", "dependencies": {"rc": "^1.0.1"}, "engines": {"node": ">=0.10.0"}}, "node_modules/npx/node_modules/require-directory": {"version": "2.1.1", "inBundle": true, "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/npx/node_modules/require-main-filename": {"version": "1.0.1", "inBundle": true, "license": "ISC"}, "node_modules/npx/node_modules/rimraf": {"version": "2.7.1", "inBundle": true, "license": "ISC", "dependencies": {"glob": "^7.1.3"}, "bin": {"rimraf": "bin.js"}}, "node_modules/npx/node_modules/safe-buffer": {"version": "5.2.0", "inBundle": true, "license": "MIT"}, "node_modules/npx/node_modules/semver": {"version": "5.7.1", "inBundle": true, "license": "ISC", "bin": {"semver": "bin/semver"}}, "node_modules/npx/node_modules/semver-diff": {"version": "2.1.0", "inBundle": true, "license": "MIT", "dependencies": {"semver": "^5.0.3"}, "engines": {"node": ">=0.10.0"}}, "node_modules/npx/node_modules/set-blocking": {"version": "2.0.0", "inBundle": true, "license": "ISC"}, "node_modules/npx/node_modules/shebang-command": {"version": "1.2.0", "inBundle": true, "license": "MIT", "dependencies": {"shebang-regex": "^1.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/npx/node_modules/shebang-regex": {"version": "1.0.0", "inBundle": true, "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/npx/node_modules/signal-exit": {"version": "3.0.2", "inBundle": true, "license": "ISC"}, "node_modules/npx/node_modules/string-width": {"version": "2.1.1", "inBundle": true, "license": "MIT", "dependencies": {"is-fullwidth-code-point": "^2.0.0", "strip-ansi": "^4.0.0"}, "engines": {"node": ">=4"}}, "node_modules/npx/node_modules/strip-ansi": {"version": "4.0.0", "inBundle": true, "license": "MIT", "dependencies": {"ansi-regex": "^3.0.0"}, "engines": {"node": ">=4"}}, "node_modules/npx/node_modules/strip-eof": {"version": "1.0.0", "inBundle": true, "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/npx/node_modules/strip-json-comments": {"version": "2.0.1", "inBundle": true, "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/npx/node_modules/supports-color": {"version": "5.5.0", "inBundle": true, "license": "MIT", "dependencies": {"has-flag": "^3.0.0"}, "engines": {"node": ">=4"}}, "node_modules/npx/node_modules/term-size": {"version": "1.2.0", "inBundle": true, "license": "MIT", "dependencies": {"execa": "^0.7.0"}, "engines": {"node": ">=4"}}, "node_modules/npx/node_modules/timed-out": {"version": "4.0.1", "inBundle": true, "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/npx/node_modules/unique-string": {"version": "1.0.0", "inBundle": true, "license": "MIT", "dependencies": {"crypto-random-string": "^1.0.0"}, "engines": {"node": ">=4"}}, "node_modules/npx/node_modules/unzip-response": {"version": "2.0.1", "inBundle": true, "license": "MIT", "engines": {"node": ">=4"}}, "node_modules/npx/node_modules/update-notifier": {"version": "2.5.0", "inBundle": true, "license": "BSD-2-<PERSON><PERSON>", "dependencies": {"boxen": "^1.2.1", "chalk": "^2.0.1", "configstore": "^3.0.0", "import-lazy": "^2.1.0", "is-ci": "^1.0.10", "is-installed-globally": "^0.1.0", "is-npm": "^1.0.0", "latest-version": "^3.0.0", "semver-diff": "^2.0.0", "xdg-basedir": "^3.0.0"}, "engines": {"node": ">=4"}}, "node_modules/npx/node_modules/url-parse-lax": {"version": "1.0.0", "inBundle": true, "license": "MIT", "dependencies": {"prepend-http": "^1.0.1"}, "engines": {"node": ">=0.10.0"}}, "node_modules/npx/node_modules/validate-npm-package-name": {"version": "3.0.0", "inBundle": true, "license": "ISC", "dependencies": {"builtins": "^1.0.3"}}, "node_modules/npx/node_modules/which": {"version": "1.3.1", "inBundle": true, "license": "ISC", "dependencies": {"isexe": "^2.0.0"}, "bin": {"which": "bin/which"}}, "node_modules/npx/node_modules/which-module": {"version": "2.0.0", "inBundle": true, "license": "ISC"}, "node_modules/npx/node_modules/widest-line": {"version": "2.0.1", "inBundle": true, "license": "MIT", "dependencies": {"string-width": "^2.1.1"}, "engines": {"node": ">=4"}}, "node_modules/npx/node_modules/wrap-ansi": {"version": "2.1.0", "inBundle": true, "license": "MIT", "dependencies": {"string-width": "^1.0.1", "strip-ansi": "^3.0.1"}, "engines": {"node": ">=0.10.0"}}, "node_modules/npx/node_modules/wrap-ansi/node_modules/ansi-regex": {"version": "2.1.1", "inBundle": true, "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/npx/node_modules/wrap-ansi/node_modules/is-fullwidth-code-point": {"version": "1.0.0", "inBundle": true, "license": "MIT", "dependencies": {"number-is-nan": "^1.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/npx/node_modules/wrap-ansi/node_modules/string-width": {"version": "1.0.2", "inBundle": true, "license": "MIT", "dependencies": {"code-point-at": "^1.0.0", "is-fullwidth-code-point": "^1.0.0", "strip-ansi": "^3.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/npx/node_modules/wrap-ansi/node_modules/strip-ansi": {"version": "3.0.1", "inBundle": true, "license": "MIT", "dependencies": {"ansi-regex": "^2.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/npx/node_modules/wrappy": {"version": "1.0.2", "inBundle": true, "license": "ISC"}, "node_modules/npx/node_modules/write-file-atomic": {"version": "2.4.3", "inBundle": true, "license": "ISC", "dependencies": {"graceful-fs": "^4.1.11", "imurmurhash": "^0.1.4", "signal-exit": "^3.0.2"}}, "node_modules/npx/node_modules/xdg-basedir": {"version": "3.0.0", "inBundle": true, "license": "MIT", "engines": {"node": ">=4"}}, "node_modules/npx/node_modules/y18n": {"version": "4.0.0", "inBundle": true, "license": "ISC"}, "node_modules/npx/node_modules/yallist": {"version": "2.1.2", "inBundle": true, "license": "ISC"}, "node_modules/npx/node_modules/yargs": {"version": "11.1.1", "inBundle": true, "license": "MIT", "dependencies": {"cliui": "^4.0.0", "decamelize": "^1.1.1", "find-up": "^2.1.0", "get-caller-file": "^1.0.1", "os-locale": "^3.1.0", "require-directory": "^2.1.1", "require-main-filename": "^1.0.1", "set-blocking": "^2.0.0", "string-width": "^2.0.0", "which-module": "^2.0.0", "y18n": "^3.2.1", "yargs-parser": "^9.0.2"}}, "node_modules/npx/node_modules/yargs-parser": {"version": "9.0.2", "inBundle": true, "license": "ISC", "dependencies": {"camelcase": "^4.1.0"}}, "node_modules/npx/node_modules/yargs/node_modules/y18n": {"version": "3.2.1", "inBundle": true, "license": "ISC"}, "node_modules/object-assign": {"version": "4.1.1", "resolved": "https://registry.npmjs.org/object-assign/-/object-assign-4.1.1.tgz", "integrity": "sha512-rJgTQnkUnH1sFw8yT6VSU3zD3sWmu6sZhIseY8VX+GRu3P6F7Fu+JNDoXfklElbLJSnc3FUQHVe4cU5hj+BcUg==", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/object-inspect": {"version": "1.13.4", "resolved": "https://registry.npmjs.org/object-inspect/-/object-inspect-1.13.4.tgz", "integrity": "sha512-W67iLl4J2EXEGTbfeHCffrjDfitvLANg0UlX3wFUUSTx92KXRFegMHUVgSqE+wvhAbi4WqjGg9czysTV2Epbew==", "license": "MIT", "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/on-finished": {"version": "2.4.1", "resolved": "https://registry.npmjs.org/on-finished/-/on-finished-2.4.1.tgz", "integrity": "sha512-oVlzkg3ENAhCk2zdv7IJwd/QUD4z2RxRwpkcGY8psCVcCYZNq4wYnVWALHM+brtuJjePWiYF/ClmuDr8Ch5+kg==", "license": "MIT", "dependencies": {"ee-first": "1.1.1"}, "engines": {"node": ">= 0.8"}}, "node_modules/once": {"version": "1.4.0", "resolved": "https://registry.npmjs.org/once/-/once-1.4.0.tgz", "integrity": "sha512-lNaJgI+2Q5URQBkccEKHTQOPaXdUxnZZElQTZY0MFUAuaEqe1E+Nyvgdz/aIyNi6Z9MzO5dv1H8n58/GELp3+w==", "license": "ISC", "dependencies": {"wrappy": "1"}}, "node_modules/openapi-fetch": {"version": "0.13.8", "resolved": "https://registry.npmjs.org/openapi-fetch/-/openapi-fetch-0.13.8.tgz", "integrity": "sha512-yJ4QKRyNxE44baQ9mY5+r/kAzZ8yXMemtNAOFwOzRXJscdjSxxzWSNlyBAr+o5JjkUw9Lc3W7OIoca0cY3PYnQ==", "license": "MIT", "dependencies": {"openapi-typescript-helpers": "^0.0.15"}}, "node_modules/openapi-typescript-helpers": {"version": "0.0.15", "resolved": "https://registry.npmjs.org/openapi-typescript-helpers/-/openapi-typescript-helpers-0.0.15.tgz", "integrity": "sha512-opyTPaunsklCBpTK8JGef6mfPhLSnyy5a0IN9vKtx3+4aExf+KxEqYwIy3hqkedXIB97u357uLMJsOnm3GVjsw==", "license": "MIT"}, "node_modules/parseurl": {"version": "1.3.3", "resolved": "https://registry.npmjs.org/parseurl/-/parseurl-1.3.3.tgz", "integrity": "sha512-CiyeOxFT/JZyN5m0z9PfXw4SCBJ6Sygz1Dpl0wqjlhDEGGBP1GnsUVEL0p63hoG1fcj3fHynXi9NYO4nWOL+qQ==", "license": "MIT", "engines": {"node": ">= 0.8"}}, "node_modules/path-key": {"version": "3.1.1", "resolved": "https://registry.npmjs.org/path-key/-/path-key-3.1.1.tgz", "integrity": "sha512-ojmeN0qd+y0jszEtoY48r0Peq5dwMEkIlCOu6Q5f41lfkswXuKtYrhgoTpLnyIcHm24Uhqx+5Tqm2InSwLhE6Q==", "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/path-to-regexp": {"version": "0.1.12", "resolved": "https://registry.npmjs.org/path-to-regexp/-/path-to-regexp-0.1.12.tgz", "integrity": "sha512-RA1GjUVMnvYFxuqovrEqZoxxW5NUZqbwKtYz/Tt7nXerk0LbLblQmrsgdeOxV5SFHf0UDggjS/bSeOZwt1pmEQ==", "license": "MIT"}, "node_modules/pkce-challenge": {"version": "5.0.0", "resolved": "https://registry.npmjs.org/pkce-challenge/-/pkce-challenge-5.0.0.tgz", "integrity": "sha512-ueGLflrrnvwB3xuo/uGob5pd5FN7l0MsLf0Z87o/UQmRtwjvfylfc9MurIxRAWywCYTgrvpXBcqjV4OfCYGCIQ==", "license": "MIT", "engines": {"node": ">=16.20.0"}}, "node_modules/proxy-addr": {"version": "2.0.7", "resolved": "https://registry.npmjs.org/proxy-addr/-/proxy-addr-2.0.7.tgz", "integrity": "sha512-llQsMLSUDUPT44jdrU/O37qlnifitDP+ZwrmmZcoSKyLKvtZxpyV0n2/bD/N4tBAAZ/gJEdZU7KMraoK1+XYAg==", "license": "MIT", "dependencies": {"forwarded": "0.2.0", "ipaddr.js": "1.9.1"}, "engines": {"node": ">= 0.10"}}, "node_modules/punycode": {"version": "2.3.1", "resolved": "https://registry.npmjs.org/punycode/-/punycode-2.3.1.tgz", "integrity": "sha512-vYt7UD1U9Wg6138shLtLOvdAu+8DsC/ilFtEVHcH+wydcSpNE20AfSOduf6MkRFahL5FY7X1oU7nKVZFtfq8Fg==", "license": "MIT", "engines": {"node": ">=6"}}, "node_modules/qs": {"version": "6.13.0", "resolved": "https://registry.npmjs.org/qs/-/qs-6.13.0.tgz", "integrity": "sha512-+38qI9SOr8tfZ4QmJNplMUxqjbe7LKvvZgWdExBOmd+egZTtjLB67Gu0HRX3u/XOq7UU2Nx6nsjvS16Z9uwfpg==", "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"side-channel": "^1.0.6"}, "engines": {"node": ">=0.6"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/range-parser": {"version": "1.2.1", "resolved": "https://registry.npmjs.org/range-parser/-/range-parser-1.2.1.tgz", "integrity": "sha512-Hrgsx+orqoygnmhFbKaHE6c296J+HTAQXoxEF6gNupROmmGJRoyzfG3ccAveqCBrwr/2yxQ5BVd/GTl5agOwSg==", "license": "MIT", "engines": {"node": ">= 0.6"}}, "node_modules/raw-body": {"version": "3.0.0", "resolved": "https://registry.npmjs.org/raw-body/-/raw-body-3.0.0.tgz", "integrity": "sha512-RmkhL8CAyCRPXCE28MMH0z2PNWQBNk2Q09ZdxM9IOOXwxwZbN+qbWaatPkdkWIKL2ZVDImrN/pK5HTRz2PcS4g==", "license": "MIT", "dependencies": {"bytes": "3.1.2", "http-errors": "2.0.0", "iconv-lite": "0.6.3", "unpipe": "1.0.0"}, "engines": {"node": ">= 0.8"}}, "node_modules/raw-body/node_modules/iconv-lite": {"version": "0.6.3", "resolved": "https://registry.npmjs.org/iconv-lite/-/iconv-lite-0.6.3.tgz", "integrity": "sha512-4fCk79wshMdzMp2rH06qWrJE4iolqLhCUH+OiuIgU++RB0+94NlDL81atO7GX55uUKueo0txHNtvEyI6D7WdMw==", "license": "MIT", "dependencies": {"safer-buffer": ">= 2.1.2 < 3.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/router": {"version": "2.2.0", "resolved": "https://registry.npmjs.org/router/-/router-2.2.0.tgz", "integrity": "sha512-nLTrUKm2UyiL7rlhapu/Zl45FwNgkZGaCpZbIHajDYgwlJCOzLSk+cIPAnsEqV955GjILJnKbdQC1nVPz+gAYQ==", "license": "MIT", "dependencies": {"debug": "^4.4.0", "depd": "^2.0.0", "is-promise": "^4.0.0", "parseurl": "^1.3.3", "path-to-regexp": "^8.0.0"}, "engines": {"node": ">= 18"}}, "node_modules/router/node_modules/debug": {"version": "4.4.1", "resolved": "https://registry.npmjs.org/debug/-/debug-4.4.1.tgz", "integrity": "sha512-KcKCqiftBJcZr++7ykoDIEwSa3XWowTfNPo92BYxjXiyYEVrUQh2aLyhxBCwww+heortUFxEJYcRzosstTEBYQ==", "license": "MIT", "dependencies": {"ms": "^2.1.3"}, "engines": {"node": ">=6.0"}, "peerDependenciesMeta": {"supports-color": {"optional": true}}}, "node_modules/router/node_modules/ms": {"version": "2.1.3", "resolved": "https://registry.npmjs.org/ms/-/ms-2.1.3.tgz", "integrity": "sha512-6FlzubTLZG3J2a/NVCAleEhjzq5oxgHyaCU9yYXvcLsvoVaHJq/s5xXI6/XXP6tz7R9xAOtHnSO/tXtF3WRTlA==", "license": "MIT"}, "node_modules/router/node_modules/path-to-regexp": {"version": "8.2.0", "resolved": "https://registry.npmjs.org/path-to-regexp/-/path-to-regexp-8.2.0.tgz", "integrity": "sha512-TdrF7fW9Rphjq4RjrW0Kp2AW0Ahwu9sRGTkS6bvDi0SCwZlEZYmcfDbEsTz8RVk0EHIS/Vd1bv3JhG+1xZuAyQ==", "license": "MIT", "engines": {"node": ">=16"}}, "node_modules/safe-buffer": {"version": "5.2.1", "resolved": "https://registry.npmjs.org/safe-buffer/-/safe-buffer-5.2.1.tgz", "integrity": "sha512-rp3So07KcdmmKbGvgaNxQSJr7bGVSVk5S9Eq1F+ppbRo70+YeaDxkw5Dd8NPN+GD6bjnYm2VuPuCXmpuYvmCXQ==", "funding": [{"type": "github", "url": "https://github.com/sponsors/feross"}, {"type": "patreon", "url": "https://www.patreon.com/feross"}, {"type": "consulting", "url": "https://feross.org/support"}], "license": "MIT"}, "node_modules/safer-buffer": {"version": "2.1.2", "resolved": "https://registry.npmjs.org/safer-buffer/-/safer-buffer-2.1.2.tgz", "integrity": "sha512-YZo3K82SD7Riyi0E1EQPojLz7kpepnSQI9IyPbHHg1XXXevb5dJI7tpyN2ADxGcQbHG7vcyRHk0cbwqcQriUtg==", "license": "MIT"}, "node_modules/send": {"version": "0.19.0", "resolved": "https://registry.npmjs.org/send/-/send-0.19.0.tgz", "integrity": "sha512-dW41u5VfLXu8SJh5bwRmyYUbAoSB3c9uQh6L8h/KtsFREPWpbX1lrljJo186Jc4nmci/sGUZ9a0a0J2zgfq2hw==", "license": "MIT", "dependencies": {"debug": "2.6.9", "depd": "2.0.0", "destroy": "1.2.0", "encodeurl": "~1.0.2", "escape-html": "~1.0.3", "etag": "~1.8.1", "fresh": "0.5.2", "http-errors": "2.0.0", "mime": "1.6.0", "ms": "2.1.3", "on-finished": "2.4.1", "range-parser": "~1.2.1", "statuses": "2.0.1"}, "engines": {"node": ">= 0.8.0"}}, "node_modules/send/node_modules/encodeurl": {"version": "1.0.2", "resolved": "https://registry.npmjs.org/encodeurl/-/encodeurl-1.0.2.tgz", "integrity": "sha512-TPJXq8JqFaVYm2CWmPvnP2Iyo4ZSM7/QKcSmuMLDObfpH5fi7RUGmd/rTDf+rut/saiDiQEeVTNgAmJEdAOx0w==", "license": "MIT", "engines": {"node": ">= 0.8"}}, "node_modules/send/node_modules/ms": {"version": "2.1.3", "resolved": "https://registry.npmjs.org/ms/-/ms-2.1.3.tgz", "integrity": "sha512-6FlzubTLZG3J2a/NVCAleEhjzq5oxgHyaCU9yYXvcLsvoVaHJq/s5xXI6/XXP6tz7R9xAOtHnSO/tXtF3WRTlA==", "license": "MIT"}, "node_modules/serve-static": {"version": "1.16.2", "resolved": "https://registry.npmjs.org/serve-static/-/serve-static-1.16.2.tgz", "integrity": "sha512-VqpjJZKadQB/PEbEwvFdO43Ax5dFBZ2UECszz8bQ7pi7wt//PWe1P6MN7eCnjsatYtBT6EuiClbjSWP2WrIoTw==", "license": "MIT", "dependencies": {"encodeurl": "~2.0.0", "escape-html": "~1.0.3", "parseurl": "~1.3.3", "send": "0.19.0"}, "engines": {"node": ">= 0.8.0"}}, "node_modules/setprototypeof": {"version": "1.2.0", "resolved": "https://registry.npmjs.org/setprototypeof/-/setprototypeof-1.2.0.tgz", "integrity": "sha512-E5LDX7Wrp85Kil5bhZv46j8jOeboKq5JMmYM3gVGdGH8xFpPWXUMsNrlODCrkoxMEeNi/XZIwuRvY4XNwYMJpw==", "license": "ISC"}, "node_modules/shebang-command": {"version": "2.0.0", "resolved": "https://registry.npmjs.org/shebang-command/-/shebang-command-2.0.0.tgz", "integrity": "sha512-kHxr2zZpYtdmrN1qDjrrX/Z1rR1kG8Dx+gkpK1G4eXmvXswmcE1hTWBWYUzlraYw1/yZp6YuDY77YtvbN0dmDA==", "license": "MIT", "dependencies": {"shebang-regex": "^3.0.0"}, "engines": {"node": ">=8"}}, "node_modules/shebang-regex": {"version": "3.0.0", "resolved": "https://registry.npmjs.org/shebang-regex/-/shebang-regex-3.0.0.tgz", "integrity": "sha512-7++dFhtcx3353uBaq8DDR4NuxBetBzC7ZQOhmTQInHEd6bSrXdiEyzCvG07Z44UYdLShWUyXt5M/yhz8ekcb1A==", "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/side-channel": {"version": "1.1.0", "resolved": "https://registry.npmjs.org/side-channel/-/side-channel-1.1.0.tgz", "integrity": "sha512-ZX99e6tRweoUXqR+VBrslhda51Nh5MTQwou5tnUDgbtyM0dBgmhEDtWGP/xbKn6hqfPRHujUNwz5fy/wbbhnpw==", "license": "MIT", "dependencies": {"es-errors": "^1.3.0", "object-inspect": "^1.13.3", "side-channel-list": "^1.0.0", "side-channel-map": "^1.0.1", "side-channel-weakmap": "^1.0.2"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/side-channel-list": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/side-channel-list/-/side-channel-list-1.0.0.tgz", "integrity": "sha512-FCLHtRD/gnpCiCHEiJLOwdmFP+wzCmDEkc9y7NsYxeF4u7Btsn1ZuwgwJGxImImHicJArLP4R0yX4c2KCrMrTA==", "license": "MIT", "dependencies": {"es-errors": "^1.3.0", "object-inspect": "^1.13.3"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/side-channel-map": {"version": "1.0.1", "resolved": "https://registry.npmjs.org/side-channel-map/-/side-channel-map-1.0.1.tgz", "integrity": "sha512-VCjCNfgMsby3tTdo02nbjtM/ewra6jPHmpThenkTYh8pG9ucZ/1P8So4u4FGBek/BjpOVsDCMoLA/iuBKIFXRA==", "license": "MIT", "dependencies": {"call-bound": "^1.0.2", "es-errors": "^1.3.0", "get-intrinsic": "^1.2.5", "object-inspect": "^1.13.3"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/side-channel-weakmap": {"version": "1.0.2", "resolved": "https://registry.npmjs.org/side-channel-weakmap/-/side-channel-weakmap-1.0.2.tgz", "integrity": "sha512-WPS/HvHQTYnHisLo9McqBHOJk2FkHO/tlpvldyrnem4aeQp4hai3gythswg6p01oSoTl58rcpiFAjF2br2Ak2A==", "license": "MIT", "dependencies": {"call-bound": "^1.0.2", "es-errors": "^1.3.0", "get-intrinsic": "^1.2.5", "object-inspect": "^1.13.3", "side-channel-map": "^1.0.1"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/statuses": {"version": "2.0.1", "resolved": "https://registry.npmjs.org/statuses/-/statuses-2.0.1.tgz", "integrity": "sha512-RwNA9Z/7PrK06rYLIzFMlaF+l73iwpzsqRIFgbMLbTcLD6cOao82TaWefPXQvB2fOC4AjuYSEndS7N/mTCbkdQ==", "license": "MIT", "engines": {"node": ">= 0.8"}}, "node_modules/toidentifier": {"version": "1.0.1", "resolved": "https://registry.npmjs.org/toidentifier/-/toidentifier-1.0.1.tgz", "integrity": "sha512-o5sSPKEkg/DIQNmH43V0/uerLrpzVedkUh8tGNvaeXpfpuwjKenlSox/2O/BTlZUtEe+JG7s5YhEz608PlAHRA==", "license": "MIT", "engines": {"node": ">=0.6"}}, "node_modules/type-is": {"version": "1.6.18", "resolved": "https://registry.npmjs.org/type-is/-/type-is-1.6.18.tgz", "integrity": "sha512-TkRKr9sUTxEH8MdfuCSP7VizJyzRNMjj2J2do2Jr3Kym598JVdEksuzPQCnlFPW4ky9Q+iA+ma9BGm06XQBy8g==", "license": "MIT", "dependencies": {"media-typer": "0.3.0", "mime-types": "~2.1.24"}, "engines": {"node": ">= 0.6"}}, "node_modules/undici": {"version": "6.21.3", "resolved": "https://registry.npmjs.org/undici/-/undici-6.21.3.tgz", "integrity": "sha512-gBLkYIlEnSp8pFbT64yFgGE6UIB9tAkhukC23PmMDCe5Nd+cRqKxSjw5y54MK2AZMgZfJWMaNE4nYUHgi1XEOw==", "license": "MIT", "engines": {"node": ">=18.17"}}, "node_modules/unpipe": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/unpipe/-/unpipe-1.0.0.tgz", "integrity": "sha512-pjy2bYhSsufwWlKwPc+l3cN7+wuJlK6uz0YdJEOlQDbl6jo/YlPi4mb8agUkVC8BF7V8NuzeyPNqRksA3hztKQ==", "license": "MIT", "engines": {"node": ">= 0.8"}}, "node_modules/uri-js": {"version": "4.4.1", "resolved": "https://registry.npmjs.org/uri-js/-/uri-js-4.4.1.tgz", "integrity": "sha512-7rKUyy33Q1yc98pQ1DAmLtwX109F7TIfWlW1Ydo8Wl1ii1SeHieeh0HHfPeL2fMXK6z0s8ecKs9frCuLJvndBg==", "license": "BSD-2-<PERSON><PERSON>", "dependencies": {"punycode": "^2.1.0"}}, "node_modules/utils-merge": {"version": "1.0.1", "resolved": "https://registry.npmjs.org/utils-merge/-/utils-merge-1.0.1.tgz", "integrity": "sha512-pMZTvIkT1d+TFGvDOqodOclx0QWkkgi6Tdoa8gC8ffGAAqz9pzPTZWAybbsHHoED/ztMtkv/VoYTYyShUn81hA==", "license": "MIT", "engines": {"node": ">= 0.4.0"}}, "node_modules/vary": {"version": "1.1.2", "resolved": "https://registry.npmjs.org/vary/-/vary-1.1.2.tgz", "integrity": "sha512-BNGbWLfd0eUPabhkXUVm0j8uuvREyTh5ovRa/dyow/BqAbZJyC+5fU+IzQOzmAKzYqYRAISoRhdQr3eIZ/PXqg==", "license": "MIT", "engines": {"node": ">= 0.8"}}, "node_modules/which": {"version": "4.0.0", "resolved": "https://registry.npmjs.org/which/-/which-4.0.0.tgz", "integrity": "sha512-GlaYyEb07DPxYCKhKzplCWBJtvxZcZMrL+4UkrTSJHHPyZU4mYYTv3qaOe77H7EODLSSopAUFAc6W8U4yqvscg==", "license": "ISC", "dependencies": {"isexe": "^3.1.1"}, "bin": {"node-which": "bin/which.js"}, "engines": {"node": "^16.13.0 || >=18.0.0"}}, "node_modules/wrappy": {"version": "1.0.2", "resolved": "https://registry.npmjs.org/wrappy/-/wrappy-1.0.2.tgz", "integrity": "sha512-l4Sp/DRseor9wL6EvV2+TuQn63dMkPjZ/sp9XkghTEbV9KlPS1xUsZ3u7/IQO4wxtcFB4bgpQPRcR3QCvezPcQ==", "license": "ISC"}, "node_modules/zod": {"version": "3.25.67", "resolved": "https://registry.npmjs.org/zod/-/zod-3.25.67.tgz", "integrity": "sha512-idA2YXwpCdqUSKRCACDE6ItZD9TZzy3OZMtpfLoh6oPR47lipysRrJfjzMqFxQ3uJuUPyUeWe1r9vLH33xO/Qw==", "license": "MIT", "funding": {"url": "https://github.com/sponsors/colinhacks"}}, "node_modules/zod-to-json-schema": {"version": "3.24.5", "resolved": "https://registry.npmjs.org/zod-to-json-schema/-/zod-to-json-schema-3.24.5.tgz", "integrity": "sha512-/AuWwMP+YqiPbsJx5D6TfgRTc4kTLjsh5SOcd4bLsfUg2RcEXrFMJl1DGgdHy2aCfsIA/cr/1JM0xcB2GZji8g==", "license": "ISC", "peerDependencies": {"zod": "^3.24.1"}}}}