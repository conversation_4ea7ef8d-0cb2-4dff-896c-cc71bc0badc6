import React from 'react';
import { Navigate, useLocation } from 'react-router-dom';
import { useAuth } from '@/context/auth/SimpleAuthProvider'; // Updated to use unified auth system
import { Permission, UserRole } from '@/types/rbac';

interface RouteGuardProps {
  children: React.ReactNode;
  requiredPermissions?: Permission[];
  requiredRole?: UserRole;
  requireAnyPermission?: boolean; // If true, user needs ANY of the permissions, if false, user needs ALL
  fallbackPath?: string;
  showUnauthorized?: boolean;
}

const RouteGuard: React.FC<RouteGuardProps> = ({
  children,
  requiredPermissions = [],
  requiredRole,
  requireAnyPermission = false,
  fallbackPath = '/landing',
  showUnauthorized = false,
}) => {
  const auth = useAuth(); // Initialize auth context
  const location = useLocation(); // Initialize location context

  if (!auth) {
    console.error('[RouteGuard] Context missing, redirecting to error...');
    return <Navigate to="/error" replace />;
  }

  const { user, hasPermission, hasAnyPermission, hasAllPermissions, isRoleOrHigher, loading } = auth;

  console.log('[RouteGuard] Current state:', { pathname: location.pathname, user, loading });

  // Render loading state
  if (loading) {
    console.log('[RouteGuard] Loading...');
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-16 w-16 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  // Check for unauthenticated users
  if (!user) {
    console.log('[RouteGuard] Redirecting unauthenticated user...');
    return <Navigate to={fallbackPath} replace />;
  }

  // Validate required role
  if (requiredRole && !isRoleOrHigher(requiredRole)) {
    console.log('[RouteGuard] Insufficient role, redirecting...');
    return showUnauthorized ? (
      <UnauthorizedComponent requiredRole={requiredRole} />
    ) : (
      <Navigate to="/unauthorized" replace />
    );
  }

  // Validate required permissions
  if (
    requiredPermissions.length > 0 &&
    !(requireAnyPermission ? hasAnyPermission(requiredPermissions) : hasAllPermissions(requiredPermissions))
  ) {
    console.log('[RouteGuard] Insufficient permissions, redirecting...');
    return showUnauthorized ? (
      <UnauthorizedComponent requiredPermissions={requiredPermissions} />
    ) : (
      <Navigate to="/unauthorized" replace />
    );
  }

  // Render children if all checks pass
  return <>{children}</>;
};

// Unauthorized component to display access denial message
const UnauthorizedComponent: React.FC<{
  requiredPermissions?: Permission[];
  requiredRole?: UserRole;
}> = ({ requiredPermissions, requiredRole }) => {
  const { user } = useAuth();

  return (
    <div className="flex items-center justify-center min-h-screen bg-gray-50">
      <div className="bg-white p-6 rounded shadow-md text-center">
        <h2 className="text-xl font-bold text-red-500">Access Denied</h2>
        <p className="mt-2 text-gray-500">You do not have the required permissions to access this page.</p>

        {requiredRole && (
          <div className="mt-4">
            <p><strong>Required role:</strong> {requiredRole}</p>
            <p><strong>Your role:</strong> {user?.role || 'None'}</p>
          </div>
        )}

        {requiredPermissions && requiredPermissions.length > 0 && (
          <div className="mt-4">
            <p><strong>Required permissions:</strong></p>
            <ul className="list-disc pl-5">
              {requiredPermissions.map((perm) => (
                <li key={perm}>{perm}</li>
              ))}
            </ul>
          </div>
        )}
      </div>
    </div>
  );
};

export default RouteGuard;
