#!/usr/bin/env node

const https = require('https');
const http = require('http');

async function testEndpoint(url, method = 'GET', data = null) {
  return new Promise((resolve, reject) => {
    const urlObj = new URL(url);
    const options = {
      hostname: urlObj.hostname,
      port: urlObj.port || (urlObj.protocol === 'https:' ? 443 : 80),
      path: urlObj.pathname + urlObj.search,
      method: method,
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
        'User-Agent': 'Better-Auth-Test/1.0'
      }
    };

    if (data) {
      const postData = JSON.stringify(data);
      options.headers['Content-Length'] = Buffer.byteLength(postData);
    }

    const client = urlObj.protocol === 'https:' ? https : http;
    const req = client.request(options, (res) => {
      let body = '';
      res.on('data', (chunk) => body += chunk);
      res.on('end', () => {
        resolve({
          status: res.statusCode,
          headers: res.headers,
          body: body,
          url: url
        });
      });
    });

    req.on('error', (error) => {
      reject({ error: error.message, url: url });
    });

    if (data) {
      req.write(JSON.stringify(data));
    }
    req.end();
  });
}

async function runConnectivityTests() {
  console.log('🔍 Testing Better Auth Server Connectivity...\n');

  const baseUrl = 'https://nxtdotx.co.za';
  const tests = [
    { name: 'Health Check', url: `${baseUrl}/health` },
    { name: 'Version Check', url: `${baseUrl}/api/version` },
    { name: 'Auth Session', url: `${baseUrl}/api/auth/session` },
    { 
      name: 'Auth Sign-In', 
      url: `${baseUrl}/api/auth/sign-in`,
      method: 'POST',
      data: {
        email: '<EMAIL>',
        password: 'Admin123!@#'
      }
    }
  ];

  for (const test of tests) {
    try {
      console.log(`Testing: ${test.name}`);
      const result = await testEndpoint(
        test.url, 
        test.method || 'GET', 
        test.data || null
      );
      
      console.log(`✅ ${test.name}: ${result.status}`);
      if (result.status >= 400) {
        console.log(`   Response: ${result.body.substring(0, 200)}...`);
      }
      console.log('');
    } catch (error) {
      console.log(`❌ ${test.name}: ${error.error}`);
      console.log('');
    }
  }
}

runConnectivityTests().catch(console.error);