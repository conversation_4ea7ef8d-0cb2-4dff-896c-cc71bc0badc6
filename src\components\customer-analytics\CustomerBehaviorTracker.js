import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, AreaChart, Area } from 'recharts';
import { Eye, MousePointer, Clock, TrendingUp, TrendingDown } from 'lucide-react';
const CustomerBehaviorTracker = ({ data = [], timeRange = 'last-7-days' }) => {
    // Default sample data if none provided
    const defaultData = [
        { timestamp: '00:00', pageViews: 120, uniqueVisitors: 85, bounceRate: 45, avgSessionDuration: 180, conversionRate: 2.1 },
        { timestamp: '04:00', pageViews: 80, uniqueVisitors: 60, bounceRate: 52, avgSessionDuration: 165, conversionRate: 1.8 },
        { timestamp: '08:00', pageViews: 250, uniqueVisitors: 180, bounceRate: 38, avgSessionDuration: 220, conversionRate: 3.2 },
        { timestamp: '12:00', pageViews: 320, uniqueVisitors: 240, bounceRate: 35, avgSessionDuration: 245, conversionRate: 3.8 },
        { timestamp: '16:00', pageViews: 380, uniqueVisitors: 280, bounceRate: 32, avgSessionDuration: 260, conversionRate: 4.1 },
        { timestamp: '20:00', pageViews: 290, uniqueVisitors: 210, bounceRate: 40, avgSessionDuration: 200, conversionRate: 3.5 },
    ];
    const behaviorData = data.length > 0 ? data : defaultData;
    const behaviorMetrics = [
        {
            title: 'Page Views',
            value: '2,450',
            change: '+12.5%',
            trend: 'up',
            icon: Eye,
            color: 'text-blue-600',
            bgColor: 'bg-blue-100'
        },
        {
            title: 'Unique Visitors',
            value: '1,820',
            change: '+8.3%',
            trend: 'up',
            icon: MousePointer,
            color: 'text-green-600',
            bgColor: 'bg-green-100'
        },
        {
            title: 'Avg Session Duration',
            value: '3m 42s',
            change: '+15s',
            trend: 'up',
            icon: Clock,
            color: 'text-purple-600',
            bgColor: 'bg-purple-100'
        },
        {
            title: 'Bounce Rate',
            value: '38.2%',
            change: '-2.1%',
            trend: 'down',
            icon: TrendingDown,
            color: 'text-orange-600',
            bgColor: 'bg-orange-100'
        }
    ];
    return (_jsxs("div", { className: "space-y-6", children: [_jsx("div", { className: "grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4", children: behaviorMetrics.map((metric, index) => (_jsx(Card, { children: _jsx(CardContent, { className: "p-4", children: _jsxs("div", { className: "flex items-center justify-between", children: [_jsxs("div", { children: [_jsx("p", { className: "text-xs font-medium text-gray-600", children: metric.title }), _jsx("p", { className: "text-lg font-bold text-gray-900", children: metric.value }), _jsxs("div", { className: "flex items-center mt-1", children: [metric.trend === 'up' ? (_jsx(TrendingUp, { className: "h-3 w-3 text-green-500 mr-1" })) : (_jsx(TrendingDown, { className: "h-3 w-3 text-red-500 mr-1" })), _jsx("span", { className: `text-xs ${metric.trend === 'up' ? 'text-green-600' : 'text-red-600'}`, children: metric.change })] })] }), _jsx("div", { className: `p-2 rounded-full ${metric.bgColor}`, children: _jsx(metric.icon, { className: `h-4 w-4 ${metric.color}` }) })] }) }) }, index))) }), _jsxs("div", { className: "grid grid-cols-1 lg:grid-cols-2 gap-6", children: [_jsxs(Card, { children: [_jsx(CardHeader, { children: _jsx(CardTitle, { children: "Page Views & Visitors" }) }), _jsx(CardContent, { children: _jsx(ResponsiveContainer, { width: "100%", height: 300, children: _jsxs(AreaChart, { data: behaviorData, children: [_jsx(CartesianGrid, { strokeDasharray: "3 3" }), _jsx(XAxis, { dataKey: "timestamp" }), _jsx(YAxis, {}), _jsx(Tooltip, {}), _jsx(Area, { type: "monotone", dataKey: "pageViews", stackId: "1", stroke: "#3b82f6", fill: "#3b82f6", fillOpacity: 0.6 }), _jsx(Area, { type: "monotone", dataKey: "uniqueVisitors", stackId: "2", stroke: "#10b981", fill: "#10b981", fillOpacity: 0.6 })] }) }) })] }), _jsxs(Card, { children: [_jsx(CardHeader, { children: _jsx(CardTitle, { children: "Conversion Rate Trends" }) }), _jsx(CardContent, { children: _jsx(ResponsiveContainer, { width: "100%", height: 300, children: _jsxs(LineChart, { data: behaviorData, children: [_jsx(CartesianGrid, { strokeDasharray: "3 3" }), _jsx(XAxis, { dataKey: "timestamp" }), _jsx(YAxis, {}), _jsx(Tooltip, { formatter: (value) => [`${value}%`, 'Conversion Rate'] }), _jsx(Line, { type: "monotone", dataKey: "conversionRate", stroke: "#8b5cf6", strokeWidth: 3 })] }) }) })] })] }), _jsxs(Card, { children: [_jsx(CardHeader, { children: _jsx(CardTitle, { children: "Behavior Insights" }) }), _jsx(CardContent, { children: _jsxs("div", { className: "grid grid-cols-1 md:grid-cols-3 gap-4", children: [_jsxs("div", { className: "p-4 bg-blue-50 rounded-lg", children: [_jsx("h4", { className: "font-semibold text-blue-900 mb-2", children: "Peak Activity" }), _jsx("p", { className: "text-sm text-blue-700", children: "Highest engagement occurs between 4-6 PM with 380 page views and 4.1% conversion rate." }), _jsx(Badge, { className: "mt-2 bg-blue-100 text-blue-800", children: "Optimize for peak hours" })] }), _jsxs("div", { className: "p-4 bg-green-50 rounded-lg", children: [_jsx("h4", { className: "font-semibold text-green-900 mb-2", children: "Low Bounce Rate" }), _jsx("p", { className: "text-sm text-green-700", children: "Bounce rate decreased by 2.1% indicating improved content relevance and user experience." }), _jsx(Badge, { className: "mt-2 bg-green-100 text-green-800", children: "Positive trend" })] }), _jsxs("div", { className: "p-4 bg-purple-50 rounded-lg", children: [_jsx("h4", { className: "font-semibold text-purple-900 mb-2", children: "Session Quality" }), _jsx("p", { className: "text-sm text-purple-700", children: "Average session duration increased by 15 seconds, showing better user engagement." }), _jsx(Badge, { className: "mt-2 bg-purple-100 text-purple-800", children: "Quality improvement" })] })] }) })] })] }));
};
export default CustomerBehaviorTracker;
