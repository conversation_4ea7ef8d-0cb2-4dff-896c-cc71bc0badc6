import { jsx as _jsx } from "react/jsx-runtime";
import { But<PERSON> } from '@/components/ui/button';
import { ChevronLeft, ChevronRight } from 'lucide-react';
export const SidebarToggleButton = ({ collapsed, onClick, open, onToggle }) => {
    // Support both older collapsed/onClick and newer open/onToggle API
    const isOpen = open !== undefined ? open : !collapsed;
    const handleClick = onToggle || onClick || (() => { });
    return (_jsx(Button, { variant: "ghost", size: "icon", className: "fixed bottom-4 left-4 z-30 rounded-full bg-slate-900 text-white hover:bg-slate-800 shadow-lg", onClick: handleClick, children: isOpen ? (_jsx(ChevronLeft, { className: "h-5 w-5" })) : (_jsx(ChevronRight, { className: "h-5 w-5" })) }));
};
