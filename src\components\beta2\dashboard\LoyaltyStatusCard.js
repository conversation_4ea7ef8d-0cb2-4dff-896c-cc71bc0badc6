import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
import React from 'react';
import { <PERSON>, CardHeader, CardContent, CardTitle } from '@/components/ui/card';
import { useLoyaltyAccount } from '@/hooks/use-loyalty';
import { supabase } from "@/integrations/supabase/client";
import { LoadingState } from './states/LoadingState';
import { AuthenticatedState } from './states/AuthenticatedState';
import { UnauthenticatedState } from './states/UnauthenticatedState';
export const LoyaltyStatusCard = () => {
    const { account, loading } = useLoyaltyAccount();
    const [isLoggedIn, setIsLoggedIn] = React.useState(false);
    React.useEffect(() => {
        async function checkAuth() {
            const { data: { session } } = await supabase.auth.getSession();
            setIsLoggedIn(!!session);
        }
        checkAuth();
    }, []);
    return (_jsxs(Card, { className: "bg-gradient-to-r from-purple-600 to-indigo-600 text-white", children: [_jsx(CardHeader, { children: _jsx(CardTitle, { className: "text-xl text-white", children: "Loyalty Program Status" }) }), _jsx(CardContent, { children: loading ? (_jsx(LoadingState, {})) : account ? (_jsx(AuthenticatedState, { account: account })) : (_jsx(UnauthenticatedState, {})) })] }));
};
