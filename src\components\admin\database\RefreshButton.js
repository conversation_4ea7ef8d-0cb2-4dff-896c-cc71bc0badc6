import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { RefreshCw } from "lucide-react";
const RefreshButton = ({ isRefreshing, onRefresh }) => {
    return (_jsxs(Button, { variant: "outline", size: "sm", onClick: onRefresh, disabled: isRefreshing, className: "flex items-center gap-1", children: [_jsx(RefreshCw, { className: `h-4 w-4 ${isRefreshing ? 'animate-spin' : ''}` }), isRefreshing ? 'Refreshing...' : 'Refresh Tables'] }));
};
export default RefreshButton;
