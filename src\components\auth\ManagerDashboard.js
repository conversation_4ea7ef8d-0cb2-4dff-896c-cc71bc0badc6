import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
import { useBetterAuth } from '@/providers/BetterAuthProvider';
import { UserRole } from '@/auth/utils/rbac/permissions';
export function ManagerDashboard() {
    const { isRoleOrHigher, user } = useBetterAuth();
    // Check if user is manager or higher
    if (!isRoleOrHigher(UserRole.MANAGER)) {
        return _jsx("div", { children: "Access denied. Manager privileges required." });
    }
    return (_jsxs("div", { children: [_jsx("h1", { children: "Manager Dashboard" }), _jsxs("p", { children: ["Welcome, ", user?.username || 'Manager', "!"] })] }));
}
