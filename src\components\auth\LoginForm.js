import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
import { useState } from 'react';
import { toast } from 'sonner';
import { useBetterAuth } from '@/providers/BetterAuthProvider';
const LoginForm = () => {
    const { signIn } = useBetterAuth();
    const [email, setEmail] = useState('');
    const [password, setPassword] = useState('');
    const [isLoading, setIsLoading] = useState(false);
    const [errorMsg, setErrorMsg] = useState('');
    const handleSubmit = async (e) => {
        e.preventDefault();
        setIsLoading(true);
        setErrorMsg('');
        try {
            const result = await signIn(email, password);
            if (result.error) {
                setErrorMsg(result.error.message || 'Invalid email or password. Please check your credentials and try again.');
            }
            else {
                toast.success('Login successful!');
            }
        }
        catch (error) {
            const errorMessage = error instanceof Error ? error.message : 'An unexpected error occurred';
            setErrorMsg(errorMessage);
        }
        finally {
            setIsLoading(false);
        }
    };
    return (_jsx("div", { className: "w-full max-w-md", children: _jsxs("form", { onSubmit: handleSubmit, className: "bg-white shadow-md rounded px-8 pt-6 pb-8 mb-4", children: [_jsx("h2", { className: "text-2xl font-bold mb-6 text-center", children: "Sign In" }), errorMsg && (_jsx("div", { className: "mb-4 p-2 bg-red-100 border border-red-400 text-red-700 rounded", children: errorMsg })), _jsxs("div", { className: "mb-4", children: [_jsx("label", { className: "block text-gray-700 text-sm font-bold mb-2", htmlFor: "email", children: "Email" }), _jsx("input", { id: "email", type: "email", value: email, onChange: (e) => setEmail(e.target.value), className: "shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline", placeholder: "Email", required: true })] }), _jsxs("div", { className: "mb-6", children: [_jsx("label", { className: "block text-gray-700 text-sm font-bold mb-2", htmlFor: "password", children: "Password" }), _jsx("input", { id: "password", type: "password", value: password, onChange: (e) => setPassword(e.target.value), className: "shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline", placeholder: "Password", required: true })] }), _jsx("div", { className: "flex items-center justify-between", children: _jsx("button", { type: "submit", disabled: isLoading, className: "bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline w-full", children: isLoading ? 'Signing in...' : 'Sign In' }) })] }) }));
};
export default LoginForm;
