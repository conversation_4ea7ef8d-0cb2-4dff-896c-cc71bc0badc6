import React from 'react';
import { useAuth, UserRole } from '@/auth';

export function ManagerDashboard() {
  const { isRoleOrHigher, user } = useAuth();

  // Check if user is manager or higher
  if (!isRoleOrHigher(UserRole.MANAGER)) {
    return <div>Access denied. Manager privileges required.</div>;
  }

  return (
    <div>
      <h1>Manager Dashboard</h1>
      <p>Welcome, {user?.username || 'Manager'}!</p>
    </div>
  );
}
