import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
import { useState, useEffect } from 'react';
// Cash Management Component
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger, } from '@/components/ui/dialog';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue, } from '@/components/ui/select';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Label } from '@/components/ui/label';
import { Plus, Banknote, TrendingUp, TrendingDown, Building, CreditCard, Loader2, RefreshCw, AlertCircle } from 'lucide-react';
import { CashManagementService } from '../../services/financial';
import { BankAccountType } from '../../types/financial';
const CashManagement = () => {
    const [bankAccounts, setBankAccounts] = useState([]);
    const [cashPosition, setCashPosition] = useState(null);
    const [cashFlowForecast, setCashFlowForecast] = useState([]);
    const [reconciliationHistory, setReconciliationHistory] = useState([]);
    const [loading, setLoading] = useState(true);
    const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
    const [newAccount, setNewAccount] = useState({
        accountName: '',
        accountNumber: '',
        bankName: '',
        bankCode: '',
        currency: 'USD',
        type: BankAccountType.CHECKING,
        balance: 0
    });
    useEffect(() => {
        loadData();
    }, []);
    const loadData = async () => {
        try {
            setLoading(true);
            const [accounts, position, forecast, history] = await Promise.all([
                CashManagementService.getBankAccounts(),
                CashManagementService.getCashPosition(),
                CashManagementService.getCashFlowForecast(30),
                CashManagementService.getReconciliationHistory()
            ]);
            setBankAccounts(accounts);
            setCashPosition(position);
            setCashFlowForecast(forecast);
            setReconciliationHistory(history);
        }
        catch (error) {
            console.error('Error loading cash management data:', error);
        }
        finally {
            setLoading(false);
        }
    };
    const handleCreateAccount = async () => {
        try {
            await CashManagementService.createBankAccount({
                ...newAccount,
                isActive: true
            });
            setIsCreateDialogOpen(false);
            setNewAccount({
                accountName: '',
                accountNumber: '',
                bankName: '',
                bankCode: '',
                currency: 'USD',
                type: BankAccountType.CHECKING,
                balance: 0
            });
            loadData();
        }
        catch (error) {
            console.error('Error creating bank account:', error);
        }
    };
    const formatCurrency = (amount, currency = 'USD') => {
        return new Intl.NumberFormat('en-US', {
            style: 'currency',
            currency: currency
        }).format(amount);
    };
    const formatDate = (date) => {
        const dateObj = typeof date === 'string' ? new Date(date) : date;
        return new Intl.DateTimeFormat('en-US', {
            year: 'numeric',
            month: 'short',
            day: 'numeric'
        }).format(dateObj);
    };
    const getAccountTypeIcon = (type) => {
        switch (type) {
            case BankAccountType.CHECKING:
                return _jsx(Building, { className: "h-4 w-4" });
            case BankAccountType.SAVINGS:
                return _jsx(Banknote, { className: "h-4 w-4" });
            case BankAccountType.CREDIT_CARD:
                return _jsx(CreditCard, { className: "h-4 w-4" });
            default:
                return _jsx(Building, { className: "h-4 w-4" });
        }
    };
    const getAccountTypeColor = (type) => {
        switch (type) {
            case BankAccountType.CHECKING:
                return 'bg-blue-100 text-blue-800';
            case BankAccountType.SAVINGS:
                return 'bg-green-100 text-green-800';
            case BankAccountType.CREDIT_CARD:
                return 'bg-purple-100 text-purple-800';
            case BankAccountType.CASH:
                return 'bg-yellow-100 text-yellow-800';
            default:
                return 'bg-gray-100 text-gray-800';
        }
    };
    if (loading) {
        return (_jsx("div", { className: "flex items-center justify-center h-64", children: _jsxs("div", { className: "flex items-center space-x-2", children: [_jsx(Loader2, { className: "h-6 w-6 animate-spin" }), _jsx("span", { children: "Loading cash management data..." })] }) }));
    }
    return (_jsxs("div", { className: "space-y-6", children: [_jsxs("div", { className: "flex justify-between items-center", children: [_jsxs("div", { children: [_jsx("h1", { className: "text-3xl font-bold tracking-tight", children: "Cash Management" }), _jsx("p", { className: "text-muted-foreground", children: "Monitor cash position, bank accounts, and cash flow forecasting" })] }), _jsxs("div", { className: "flex space-x-2", children: [_jsxs(Button, { variant: "outline", onClick: loadData, children: [_jsx(RefreshCw, { className: "h-4 w-4 mr-2" }), "Refresh"] }), _jsxs(Dialog, { open: isCreateDialogOpen, onOpenChange: setIsCreateDialogOpen, children: [_jsx(DialogTrigger, { asChild: true, children: _jsxs(Button, { children: [_jsx(Plus, { className: "h-4 w-4 mr-2" }), "Add Bank Account"] }) }), _jsxs(DialogContent, { className: "sm:max-w-[425px]", children: [_jsxs(DialogHeader, { children: [_jsx(DialogTitle, { children: "Add Bank Account" }), _jsx(DialogDescription, { children: "Add a new bank account to track cash position." })] }), _jsxs("div", { className: "grid gap-4 py-4", children: [_jsxs("div", { className: "grid grid-cols-4 items-center gap-4", children: [_jsx(Label, { htmlFor: "accountName", className: "text-right", children: "Name" }), _jsx(Input, { id: "accountName", placeholder: "e.g., Main Checking", value: newAccount.accountName, onChange: (e) => setNewAccount({ ...newAccount, accountName: e.target.value }), className: "col-span-3" })] }), _jsxs("div", { className: "grid grid-cols-4 items-center gap-4", children: [_jsx(Label, { htmlFor: "accountNumber", className: "text-right", children: "Number" }), _jsx(Input, { id: "accountNumber", placeholder: "Account number", value: newAccount.accountNumber, onChange: (e) => setNewAccount({ ...newAccount, accountNumber: e.target.value }), className: "col-span-3" })] }), _jsxs("div", { className: "grid grid-cols-4 items-center gap-4", children: [_jsx(Label, { htmlFor: "bankName", className: "text-right", children: "Bank" }), _jsx(Input, { id: "bankName", placeholder: "Bank name", value: newAccount.bankName, onChange: (e) => setNewAccount({ ...newAccount, bankName: e.target.value }), className: "col-span-3" })] }), _jsxs("div", { className: "grid grid-cols-4 items-center gap-4", children: [_jsx(Label, { htmlFor: "type", className: "text-right", children: "Type" }), _jsxs(Select, { value: newAccount.type, onValueChange: (value) => setNewAccount({ ...newAccount, type: value }), children: [_jsx(SelectTrigger, { className: "col-span-3", children: _jsx(SelectValue, { placeholder: "Select type" }) }), _jsxs(SelectContent, { children: [_jsx(SelectItem, { value: BankAccountType.CHECKING, children: "Checking" }), _jsx(SelectItem, { value: BankAccountType.SAVINGS, children: "Savings" }), _jsx(SelectItem, { value: BankAccountType.CREDIT_CARD, children: "Credit Card" }), _jsx(SelectItem, { value: BankAccountType.CASH, children: "Cash" })] })] })] }), _jsxs("div", { className: "grid grid-cols-4 items-center gap-4", children: [_jsx(Label, { htmlFor: "balance", className: "text-right", children: "Balance" }), _jsx(Input, { id: "balance", type: "number", placeholder: "0.00", value: newAccount.balance, onChange: (e) => setNewAccount({ ...newAccount, balance: parseFloat(e.target.value) || 0 }), className: "col-span-3" })] })] }), _jsx(DialogFooter, { children: _jsx(Button, { onClick: handleCreateAccount, children: "Add Account" }) })] })] })] })] }), _jsxs("div", { className: "grid grid-cols-1 md:grid-cols-3 gap-4", children: [_jsxs(Card, { children: [_jsxs(CardHeader, { className: "flex flex-row items-center justify-between space-y-0 pb-2", children: [_jsx(CardTitle, { className: "text-sm font-medium", children: "Total Cash" }), _jsx(Banknote, { className: "h-4 w-4 text-muted-foreground" })] }), _jsxs(CardContent, { children: [_jsx("div", { className: "text-2xl font-bold", children: formatCurrency(cashPosition?.totalCash || 0) }), _jsxs("p", { className: "text-xs text-muted-foreground", children: ["Across ", bankAccounts.length, " accounts"] })] })] }), _jsxs(Card, { children: [_jsxs(CardHeader, { className: "flex flex-row items-center justify-between space-y-0 pb-2", children: [_jsx(CardTitle, { className: "text-sm font-medium", children: "Available Balance" }), _jsx(TrendingUp, { className: "h-4 w-4 text-green-500" })] }), _jsxs(CardContent, { children: [_jsx("div", { className: "text-2xl font-bold text-green-600", children: formatCurrency(cashPosition?.totalCash || 0) }), _jsx("p", { className: "text-xs text-muted-foreground", children: "Ready for use" })] })] }), _jsxs(Card, { children: [_jsxs(CardHeader, { className: "flex flex-row items-center justify-between space-y-0 pb-2", children: [_jsx(CardTitle, { className: "text-sm font-medium", children: "30-Day Forecast" }), _jsx(TrendingDown, { className: "h-4 w-4 text-blue-500" })] }), _jsxs(CardContent, { children: [_jsx("div", { className: "text-2xl font-bold", children: formatCurrency(cashPosition?.totalCash || 0) }), _jsx("p", { className: "text-xs text-muted-foreground", children: "Projected balance" })] })] })] }), _jsxs(Tabs, { defaultValue: "accounts", className: "space-y-4", children: [_jsxs(TabsList, { children: [_jsx(TabsTrigger, { value: "accounts", children: "Bank Accounts" }), _jsx(TabsTrigger, { value: "forecast", children: "Cash Flow Forecast" }), _jsx(TabsTrigger, { value: "reconciliation", children: "Reconciliation" })] }), _jsx(TabsContent, { value: "accounts", className: "space-y-4", children: _jsxs(Card, { children: [_jsxs(CardHeader, { children: [_jsx(CardTitle, { children: "Bank Accounts" }), _jsx(CardDescription, { children: "Manage your bank accounts and monitor balances" })] }), _jsx(CardContent, { children: _jsxs(Table, { children: [_jsx(TableHeader, { children: _jsxs(TableRow, { children: [_jsx(TableHead, { children: "Account Name" }), _jsx(TableHead, { children: "Bank" }), _jsx(TableHead, { children: "Account Number" }), _jsx(TableHead, { children: "Type" }), _jsx(TableHead, { className: "text-right", children: "Balance" }), _jsx(TableHead, { children: "Status" }), _jsx(TableHead, { children: "Last Reconciled" })] }) }), _jsx(TableBody, { children: bankAccounts.map((account) => (_jsxs(TableRow, { children: [_jsx(TableCell, { className: "font-medium", children: account.accountName }), _jsx(TableCell, { children: account.bankName }), _jsxs(TableCell, { children: ["****", account.accountNumber.slice(-4)] }), _jsx(TableCell, { children: _jsx(Badge, { className: getAccountTypeColor(account.type), children: _jsxs("div", { className: "flex items-center space-x-1", children: [getAccountTypeIcon(account.type), _jsx("span", { children: account.type })] }) }) }), _jsx(TableCell, { className: "text-right font-medium", children: formatCurrency(account.balance, account.currency) }), _jsx(TableCell, { children: _jsx(Badge, { variant: account.isActive ? "default" : "secondary", children: account.isActive ? 'Active' : 'Inactive' }) }), _jsx(TableCell, { children: account.lastReconciled ? formatDate(account.lastReconciled) : 'Never' })] }, account.id))) })] }) })] }) }), _jsx(TabsContent, { value: "forecast", className: "space-y-4", children: _jsxs(Card, { children: [_jsxs(CardHeader, { children: [_jsx(CardTitle, { children: "Cash Flow Forecast" }), _jsx(CardDescription, { children: "30-day cash flow projection based on scheduled transactions" })] }), _jsx(CardContent, { children: _jsxs("div", { className: "text-center py-8 text-muted-foreground", children: [_jsx(AlertCircle, { className: "h-12 w-12 mx-auto mb-4 opacity-50" }), _jsx("p", { children: "Cash flow forecast will be displayed here" }), _jsx("p", { className: "text-sm", children: "Based on scheduled payments and receipts" })] }) })] }) }), _jsx(TabsContent, { value: "reconciliation", className: "space-y-4", children: _jsxs(Card, { children: [_jsxs(CardHeader, { children: [_jsx(CardTitle, { children: "Bank Reconciliation History" }), _jsx(CardDescription, { children: "Track reconciliation status for all bank accounts" })] }), _jsx(CardContent, { children: _jsxs("div", { className: "text-center py-8 text-muted-foreground", children: [_jsx(AlertCircle, { className: "h-12 w-12 mx-auto mb-4 opacity-50" }), _jsx("p", { children: "Reconciliation history will be displayed here" }), _jsx("p", { className: "text-sm", children: "Start a new reconciliation to begin tracking" })] }) })] }) })] })] }));
};
export default CashManagement;
