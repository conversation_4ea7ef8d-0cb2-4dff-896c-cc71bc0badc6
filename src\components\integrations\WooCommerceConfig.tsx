import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge';
import { CheckCircle, XCircle, Loader2, Eye, EyeOff, Download, Database } from 'lucide-react';

interface WooCommerceConfig {
  storeUrl: string;
  consumerKey: string;
  consumerSecret: string;
}

interface StagingData {
  customers: any[];
  products: any[];
  orders: any[];
  lastSync: Date;
}

export function WooCommerceConfig() {
  const [config, setConfig] = useState<WooCommerceConfig>({
    storeUrl: 'https://nxtleveltech.co.za/',
    consumerKey: 'ck_ad4701db790c558f2318f8c0dc3170854edb764b',
    consumerSecret: ''
  });

  const [isLoading, setIsLoading] = useState(false);
  const [connectionStatus, setConnectionStatus] = useState<'idle' | 'connected' | 'error'>('idle');
  const [stagingData, setStagingData] = useState<StagingData | null>(null);
  const [showSecret, setShowSecret] = useState(false);
  const [currentStep, setCurrentStep] = useState<'connect' | 'download' | 'review'>('connect');

  const handleInputChange = (field: keyof WooCommerceConfig, value: string) => {
    setConfig(prev => ({ ...prev, [field]: value }));
  };

  // STEP 1: CONNECT VIA API
  const connectToAPI = async () => {
    setIsLoading(true);

    try {
      const auth = btoa(`${config.consumerKey}:${config.consumerSecret}`);
      const url = `${config.storeUrl.replace(/\/$/, '')}/wp-json/wc/v3/system_status`;

      const response = await fetch(url, {
        method: 'GET',
        headers: {
          'Authorization': `Basic ${auth}`,
          'Content-Type': 'application/json',
        },
      });

      if (response.ok) {
        setConnectionStatus('connected');
        setCurrentStep('download');
      } else {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }
    } catch (error) {
      setConnectionStatus('error');
      alert('Connection failed: ' + (error as Error).message);
    } finally {
      setIsLoading(false);
    }
  };

  // STEP 2: DOWNLOAD DATA TO STAGING
  const downloadDataToStaging = async () => {
    setIsLoading(true);

    try {
      const auth = btoa(`${config.consumerKey}:${config.consumerSecret}`);
      const baseUrl = `${config.storeUrl.replace(/\/$/, '')}/wp-json/wc/v3`;

      // Download all data in parallel
      const [customersRes, productsRes, ordersRes] = await Promise.all([
        fetch(`${baseUrl}/customers?per_page=100`, {
          headers: { 'Authorization': `Basic ${auth}` }
        }),
        fetch(`${baseUrl}/products?per_page=100`, {
          headers: { 'Authorization': `Basic ${auth}` }
        }),
        fetch(`${baseUrl}/orders?per_page=100`, {
          headers: { 'Authorization': `Basic ${auth}` }
        })
      ]);

      const [customers, products, orders] = await Promise.all([
        customersRes.json(),
        productsRes.json(),
        ordersRes.json()
      ]);

      const staging: StagingData = {
        customers,
        products,
        orders,
        lastSync: new Date()
      };

      setStagingData(staging);
      setCurrentStep('review');

    } catch (error) {
      alert('Download failed: ' + (error as Error).message);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            WooCommerce Integration
            {connectionStatus === 'connected' && <Badge variant="default" className="bg-green-500">Connected</Badge>}
            {connectionStatus === 'error' && <Badge variant="destructive">Error</Badge>}
          </CardTitle>
          <CardDescription>
            Simple 3-step WooCommerce integration workflow
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">

          {/* STEP 1: CONNECT VIA API */}
          {currentStep === 'connect' && (
            <div className="space-y-4">
              <h3 className="text-lg font-semibold">Step 1: Connect via API</h3>

              <div className="space-y-2">
                <Label htmlFor="storeUrl">Store URL</Label>
                <Input
                  id="storeUrl"
                  value={config.storeUrl}
                  onChange={(e) => handleInputChange('storeUrl', e.target.value)}
                  placeholder="https://your-store.com/"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="consumerKey">Consumer Key</Label>
                <Input
                  id="consumerKey"
                  value={config.consumerKey}
                  onChange={(e) => handleInputChange('consumerKey', e.target.value)}
                  placeholder="ck_..."
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="consumerSecret">Consumer Secret</Label>
                <div className="relative">
                  <Input
                    id="consumerSecret"
                    type={showSecret ? 'text' : 'password'}
                    value={config.consumerSecret}
                    onChange={(e) => handleInputChange('consumerSecret', e.target.value)}
                    placeholder="cs_..."
                  />
                  <Button
                    type="button"
                    variant="ghost"
                    size="sm"
                    className="absolute right-2 top-1/2 -translate-y-1/2"
                    onClick={() => setShowSecret(!showSecret)}
                  >
                    {showSecret ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                  </Button>
                </div>
              </div>

              <Button onClick={connectToAPI} disabled={isLoading} className="w-full">
                {isLoading ? <Loader2 className="h-4 w-4 animate-spin mr-2" /> : <CheckCircle className="h-4 w-4 mr-2" />}
                Connect to WooCommerce API
              </Button>
            </div>
          )}

          {/* STEP 2: DOWNLOAD DATA TO STAGING */}
          {currentStep === 'download' && (
            <div className="space-y-4">
              <h3 className="text-lg font-semibold">Step 2: Download Data to Staging</h3>
              <p className="text-muted-foreground">Download all WooCommerce data to local staging area for review.</p>

              <Button onClick={downloadDataToStaging} disabled={isLoading} className="w-full">
                {isLoading ? <Loader2 className="h-4 w-4 animate-spin mr-2" /> : <Download className="h-4 w-4 mr-2" />}
                Download All Data to Staging
              </Button>
            </div>
          )}

          {/* STEP 3: PRESENT STAGING DATA FOR REVIEW */}
          {currentStep === 'review' && stagingData && (
            <div className="space-y-4">
              <h3 className="text-lg font-semibold">Step 3: Review Staging Data</h3>
              <p className="text-muted-foreground">
                Data downloaded on: {stagingData.lastSync.toLocaleString()}
              </p>

              <div className="grid grid-cols-3 gap-4">
                <Card>
                  <CardHeader className="pb-2">
                    <CardTitle className="text-sm">Customers</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">{stagingData.customers.length}</div>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader className="pb-2">
                    <CardTitle className="text-sm">Products</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">{stagingData.products.length}</div>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader className="pb-2">
                    <CardTitle className="text-sm">Orders</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">{stagingData.orders.length}</div>
                  </CardContent>
                </Card>
              </div>

              <div className="flex gap-2">
                <Button onClick={() => setCurrentStep('download')} variant="outline">
                  <Download className="h-4 w-4 mr-2" />
                  Re-download Data
                </Button>
                <Button onClick={() => setCurrentStep('connect')} variant="outline">
                  <Database className="h-4 w-4 mr-2" />
                  Change Connection
                </Button>
              </div>
            </div>
          )}

        </CardContent>
      </Card>
    </div>
  );
}
