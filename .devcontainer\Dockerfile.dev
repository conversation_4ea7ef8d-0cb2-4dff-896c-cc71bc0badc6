# Development container for NXT-WEB-DEV-X with MCP support
FROM node:20-bullseye

# Install system dependencies
RUN apt-get update && apt-get install -y \
    git \
    curl \
    wget \
    vim \
    nano \
    htop \
    tree \
    jq \
    python3 \
    python3-pip \
    build-essential \
    ca-certificates \
    gnupg \
    lsb-release \
    && rm -rf /var/lib/apt/lists/*

# Install Docker CLI
RUN mkdir -p /etc/apt/keyrings \
    && curl -fsSL https://download.docker.com/linux/debian/gpg | gpg --dearmor -o /etc/apt/keyrings/docker.gpg \
    && echo "deb [arch=$(dpkg --print-architecture) signed-by=/etc/apt/keyrings/docker.gpg] https://download.docker.com/linux/debian $(lsb_release -cs) stable" | tee /etc/apt/sources.list.d/docker.list > /dev/null \
    && apt-get update \
    && apt-get install -y docker-ce-cli \
    && rm -rf /var/lib/apt/lists/*

# Install global npm packages
RUN npm install -g \
    typescript \
    ts-node \
    nodemon \
    @types/node \
    prettier \
    eslint \
    vite \
    @modelcontextprotocol/sdk

# Create workspace directory
WORKDIR /workspace

# Create MCP data directory
RUN mkdir -p /mcp-data && chmod 755 /mcp-data

# Set up user
RUN useradd -m -s /bin/bash vscode \
    && usermod -aG docker vscode \
    && echo "vscode ALL=(ALL) NOPASSWD:ALL" >> /etc/sudoers

# Switch to vscode user
USER vscode

# Set environment variables
ENV NODE_ENV=development
ENV PATH="/workspace/node_modules/.bin:$PATH"

# Default command
CMD ["sleep", "infinity"]
