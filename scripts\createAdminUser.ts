import { supabaseAdmin } from "../src/integrations/supabase/client";

// Top-level await for ESM, or wrap in async IIFE for Node compatibility
(async function createNewAdminUser() {
  try {
    const email = "<EMAIL>"; // Provided email
    const firstName = "Admin";
    const lastName = "Two";
    const temporaryPassword = Math.random().toString(36).slice(-8);

    // 1) Create user in Supabase Auth with admin client
    const { data: authData, error: authError } = await supabaseAdmin.auth.admin.createUser({
      email,
      password: temporaryPassword,
      email_confirm: true
    });
    if (authError) {
      throw new Error("Error creating user in Supabase Auth: " + authError.message);
    }

    // 2) Create enhanced user profile with role set to 'admin'
    // (optional, can comment if not needed)
    try {
      await supabaseAdmin.rpc("create_enhanced_user_with_role", {
        user_email: email,
        user_username: firstName.toLowerCase() + "." + lastName.toLowerCase(),
        user_first_name: firstName,
        user_last_name: lastName,
        user_role: "admin",
        is_active_user: true
      });
      console.log("Enhanced profile created for admin user.");
    } catch (err) {
      // If profile creation fails, show but don't abort the workflow
      if (typeof err === "object" && err !== null && "message" in err) {
        // @ts-ignore
        console.error("Admin base user created, but enhanced profile call failed: " + (err as any).message);
      } else {
        console.error("Admin base user created, but enhanced profile call failed. Unknown error.");
      }
    }

    console.log("Admin user created successfully:", email);
    console.log("Temporary Password:", temporaryPassword);
  } catch (err) {
    if (typeof err === "object" && err !== null && "message" in err) {
      // @ts-ignore
      console.error("Failed to create admin user:", (err as any).message);
    } else {
      console.error("Failed to create admin user: Unknown error.");
    }
    process.exit(1);
  }
})();