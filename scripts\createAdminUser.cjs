const { createClient } = require("@supabase/supabase-js");

// Load from process.env or hardcoded
const supabaseUrl = process.env.SUPABASE_URL || "https://utxxvdztmbbjcwdkqxcc.supabase.co";
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY || "eyJhbGciOiJIUzDummyServiceKey";

const supabaseAdmin = createClient(supabaseUrl, supabaseServiceKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false,
    detectSessionInUrl: false,
  },
  db: {
    schema: 'public'
  },
  global: {
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${supabaseServiceKey}`,
      'apikey': supabaseServiceKey,
    }
  }
});

(async function createNewAdminUser() {
  try {
    const email = "<EMAIL>"; // Provided email
    const firstName = "Admin";
    const lastName = "Two";
    const temporaryPassword = Math.random().toString(36).slice(-8);

    // 1) Create user in Supabase Auth with admin client
    const { data: authData, error: authError } = await supabaseAdmin.auth.admin.createUser({
      email,
      password: temporaryPassword,
      email_confirm: true
    });
    if (authError) {
      throw new Error("Error creating user in Supabase Auth: " + authError.message);
    }

    // 2) Optionally create enhanced user profile with role set to 'admin'
    try {
      await supabaseAdmin.rpc("create_enhanced_user_with_role", {
        user_email: email,
        user_username: firstName.toLowerCase() + "." + lastName.toLowerCase(),
        user_first_name: firstName,
        user_last_name: lastName,
        user_role: "admin",
        is_active_user: true
      });
      console.log("Enhanced profile created for admin user.");
    } catch (err) {
      if (err && typeof err === "object" && "message" in err) {
        console.error("Admin base user created, but enhanced profile call failed: " + err.message);
      } else {
        console.error("Admin base user created, but enhanced profile call failed. Unknown error.");
      }
    }

    console.log("Admin user created successfully:", email);
    console.log("Temporary Password:", temporaryPassword);
  } catch (err) {
    if (err && typeof err === "object" && "message" in err) {
      console.error("Failed to create admin user:", err.message);
    } else {
      console.error("Failed to create admin user: Unknown error.");
    }
    process.exit(1);
  }
})();