import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
import { cn } from '@/lib/utils';
import { NavLink } from 'react-router-dom';
import { ChevronDown, ChevronRight } from 'lucide-react';
export const SidebarNavItem = ({ item, isExpanded, onToggleExpand, textColor, textHoverColor, activeBgColor, activeTextColor, hoverBgColor }) => {
    if (item.children) {
        return (_jsxs("li", { children: [_jsxs("div", { className: cn("flex items-center justify-between px-3 py-2 rounded-lg transition-all duration-300 cursor-pointer text-sm", textColor, textHoverColor, hoverBgColor), onClick: () => onToggleExpand(item.label), children: [_jsxs("div", { className: "flex items-center gap-3", children: [_jsx(item.icon, { className: "h-5 w-5" }), _jsx("span", { className: "font-medium uppercase", children: item.label })] }), isExpanded ?
                            _jsx(ChevronDown, { className: "h-4 w-4" }) :
                            _jsx(ChevronRight, { className: "h-4 w-4" })] }), isExpanded && (_jsx("ul", { className: "ml-8 space-y-1 mt-1", children: item.children.map(child => (_jsx("li", { children: _jsxs(NavLink, { to: child.path, end: true, className: ({ isActive }) => cn('flex items-center gap-3 px-3 py-2 rounded-lg transition-all duration-300 text-sm', isActive
                                ? `${activeBgColor} ${activeTextColor} shadow-md shadow-blue-900/30`
                                : `${textColor} ${textHoverColor} ${hoverBgColor}`), children: [_jsx(child.icon, { className: "h-4 w-4" }), _jsx("span", { className: "font-medium uppercase", children: child.label })] }) }, child.path))) }))] }, item.path));
    }
    return (_jsx("li", { className: "my-1", children: _jsxs(NavLink, { to: item.path, end: true, className: ({ isActive }) => cn('flex items-center gap-3 px-3 py-2 rounded-lg transition-all duration-300 text-sm', isActive
                ? `${activeBgColor} ${activeTextColor} shadow-md shadow-indigo-900/50`
                : `${textColor} ${textHoverColor} ${hoverBgColor}`), children: [_jsx(item.icon, { className: "h-5 w-5" }), _jsx("span", { className: "font-medium uppercase", children: item.label })] }) }, item.path));
};
