import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger, } from '@/components/ui/dialog';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue, } from '@/components/ui/select';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Plus, TrendingUp, DollarSign, Target, Loader2, RefreshCw, BarChart3, PieChart } from 'lucide-react';
import { BudgetService } from '../../services/financial';
import { BudgetType, BudgetStatus } from '../../types/financial';
const BudgetForecast = () => {
    const [budgets, setBudgets] = useState([]);
    const [activeBudget, setActiveBudget] = useState(null);
    const [varianceAnalysis, setVarianceAnalysis] = useState(null);
    const [forecast, setForecast] = useState([]);
    const [loading, setLoading] = useState(true);
    const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
    const [newBudget, setNewBudget] = useState({
        name: '',
        type: BudgetType.OPERATING,
        periodId: '',
        notes: '',
        totalAmount: 0
    });
    useEffect(() => {
        loadData();
    }, []);
    const loadData = async () => {
        try {
            setLoading(true);
            const [budgetList, variance, forecastData] = await Promise.all([
                BudgetService.getBudgets(),
                BudgetService.getBudgetVarianceAnalysis('current-period'),
                BudgetService.generateForecast(12, 'monthly')
            ]);
            setBudgets(budgetList);
            setVarianceAnalysis(variance);
            setForecast(forecastData);
            // Set active budget to the first active one
            const active = budgetList.find(b => b.status === BudgetStatus.ACTIVE);
            setActiveBudget(active || null);
        }
        catch (error) {
            console.error('Error loading budget data:', error);
        }
        finally {
            setLoading(false);
        }
    };
    const handleCreateBudget = async () => {
        try {
            await BudgetService.createBudget({
                ...newBudget,
                status: BudgetStatus.DRAFT,
                lines: [],
                createdBy: 'current-user' // This should come from auth context
            });
            setIsCreateDialogOpen(false);
            setNewBudget({
                name: '',
                type: BudgetType.OPERATING,
                periodId: '',
                notes: '',
                totalAmount: 0
            });
            loadData();
        }
        catch (error) {
            console.error('Error creating budget:', error);
        }
    };
    const formatCurrency = (amount) => {
        return new Intl.NumberFormat('en-US', {
            style: 'currency',
            currency: 'USD'
        }).format(amount);
    };
    const formatDate = (date) => {
        const dateObj = typeof date === 'string' ? new Date(date) : date;
        return new Intl.DateTimeFormat('en-US', {
            year: 'numeric',
            month: 'short',
            day: 'numeric'
        }).format(dateObj);
    };
    const formatPercentage = (value) => {
        return `${value.toFixed(1)}%`;
    };
    const getBudgetStatusColor = (status) => {
        switch (status) {
            case BudgetStatus.ACTIVE:
                return 'bg-green-100 text-green-800';
            case BudgetStatus.DRAFT:
                return 'bg-gray-100 text-gray-800';
            case BudgetStatus.APPROVED:
                return 'bg-blue-100 text-blue-800';
            case BudgetStatus.SUBMITTED:
                return 'bg-yellow-100 text-yellow-800';
            case BudgetStatus.CLOSED:
                return 'bg-red-100 text-red-800';
            default:
                return 'bg-gray-100 text-gray-800';
        }
    };
    const getVarianceColor = (variance) => {
        if (variance > 10)
            return 'text-red-600';
        if (variance < -10)
            return 'text-green-600';
        return 'text-gray-600';
    };
    if (loading) {
        return (_jsx("div", { className: "flex items-center justify-center h-64", children: _jsxs("div", { className: "flex items-center space-x-2", children: [_jsx(Loader2, { className: "h-6 w-6 animate-spin" }), _jsx("span", { children: "Loading budget and forecast data..." })] }) }));
    }
    return (_jsxs("div", { className: "space-y-6", children: [_jsxs("div", { className: "flex justify-between items-center", children: [_jsxs("div", { children: [_jsx("h1", { className: "text-3xl font-bold tracking-tight", children: "Budget & Forecast" }), _jsx("p", { className: "text-muted-foreground", children: "Create budgets, track performance, and generate financial forecasts" })] }), _jsxs("div", { className: "flex space-x-2", children: [_jsxs(Button, { variant: "outline", onClick: loadData, children: [_jsx(RefreshCw, { className: "h-4 w-4 mr-2" }), "Refresh"] }), _jsxs(Dialog, { open: isCreateDialogOpen, onOpenChange: setIsCreateDialogOpen, children: [_jsx(DialogTrigger, { asChild: true, children: _jsxs(Button, { children: [_jsx(Plus, { className: "h-4 w-4 mr-2" }), "Create Budget"] }) }), _jsxs(DialogContent, { className: "sm:max-w-[425px]", children: [_jsxs(DialogHeader, { children: [_jsx(DialogTitle, { children: "Create New Budget" }), _jsx(DialogDescription, { children: "Set up a new budget for tracking financial performance." })] }), _jsxs("div", { className: "grid gap-4 py-4", children: [_jsxs("div", { className: "grid grid-cols-4 items-center gap-4", children: [_jsx(Label, { htmlFor: "name", className: "text-right", children: "Name" }), _jsx(Input, { id: "name", placeholder: "e.g., 2024 Annual Budget", value: newBudget.name, onChange: (e) => setNewBudget({ ...newBudget, name: e.target.value }), className: "col-span-3" })] }), _jsxs("div", { className: "grid grid-cols-4 items-center gap-4", children: [_jsx(Label, { htmlFor: "type", className: "text-right", children: "Type" }), _jsxs(Select, { value: newBudget.type, onValueChange: (value) => setNewBudget({ ...newBudget, type: value }), children: [_jsx(SelectTrigger, { className: "col-span-3", children: _jsx(SelectValue, { placeholder: "Select type" }) }), _jsxs(SelectContent, { children: [_jsx(SelectItem, { value: BudgetType.OPERATING, children: "Operating" }), _jsx(SelectItem, { value: BudgetType.CAPITAL, children: "Capital" }), _jsx(SelectItem, { value: BudgetType.PROJECT, children: "Project" }), _jsx(SelectItem, { value: BudgetType.DEPARTMENT, children: "Department" })] })] })] }), _jsxs("div", { className: "grid grid-cols-4 items-center gap-4", children: [_jsx(Label, { htmlFor: "periodId", className: "text-right", children: "Period ID" }), _jsx(Input, { id: "periodId", placeholder: "e.g., 2024-Q1", value: newBudget.periodId, onChange: (e) => setNewBudget({ ...newBudget, periodId: e.target.value }), className: "col-span-3" })] }), _jsxs("div", { className: "grid grid-cols-4 items-center gap-4", children: [_jsx(Label, { htmlFor: "totalAmount", className: "text-right", children: "Total Amount" }), _jsx(Input, { id: "totalAmount", type: "number", placeholder: "0.00", value: newBudget.totalAmount, onChange: (e) => setNewBudget({ ...newBudget, totalAmount: parseFloat(e.target.value) || 0 }), className: "col-span-3" })] }), _jsxs("div", { className: "grid grid-cols-4 items-center gap-4", children: [_jsx(Label, { htmlFor: "notes", className: "text-right", children: "Notes" }), _jsx(Textarea, { id: "notes", placeholder: "Budget notes", value: newBudget.notes, onChange: (e) => setNewBudget({ ...newBudget, notes: e.target.value }), className: "col-span-3" })] })] }), _jsx(DialogFooter, { children: _jsx(Button, { onClick: handleCreateBudget, children: "Create Budget" }) })] })] })] })] }), _jsxs("div", { className: "grid grid-cols-1 md:grid-cols-4 gap-4", children: [_jsxs(Card, { children: [_jsxs(CardHeader, { className: "flex flex-row items-center justify-between space-y-0 pb-2", children: [_jsx(CardTitle, { className: "text-sm font-medium", children: "Active Budgets" }), _jsx(Target, { className: "h-4 w-4 text-muted-foreground" })] }), _jsxs(CardContent, { children: [_jsx("div", { className: "text-2xl font-bold", children: budgets.filter(b => b.status === BudgetStatus.ACTIVE).length }), _jsx("p", { className: "text-xs text-muted-foreground", children: "Currently tracking" })] })] }), _jsxs(Card, { children: [_jsxs(CardHeader, { className: "flex flex-row items-center justify-between space-y-0 pb-2", children: [_jsx(CardTitle, { className: "text-sm font-medium", children: "Budget Variance" }), _jsx(BarChart3, { className: "h-4 w-4 text-muted-foreground" })] }), _jsxs(CardContent, { children: [_jsx("div", { className: `text-2xl font-bold ${getVarianceColor(varianceAnalysis?.totalVariancePercent || 0)}`, children: formatPercentage(varianceAnalysis?.totalVariancePercent || 0) }), _jsx("p", { className: "text-xs text-muted-foreground", children: "vs. planned budget" })] })] }), _jsxs(Card, { children: [_jsxs(CardHeader, { className: "flex flex-row items-center justify-between space-y-0 pb-2", children: [_jsx(CardTitle, { className: "text-sm font-medium", children: "YTD Actual" }), _jsx(DollarSign, { className: "h-4 w-4 text-muted-foreground" })] }), _jsxs(CardContent, { children: [_jsx("div", { className: "text-2xl font-bold", children: formatCurrency(varianceAnalysis?.totalActual || 0) }), _jsx("p", { className: "text-xs text-muted-foreground", children: "Year to date spending" })] })] }), _jsxs(Card, { children: [_jsxs(CardHeader, { className: "flex flex-row items-center justify-between space-y-0 pb-2", children: [_jsx(CardTitle, { className: "text-sm font-medium", children: "Forecast" }), _jsx(TrendingUp, { className: "h-4 w-4 text-muted-foreground" })] }), _jsxs(CardContent, { children: [_jsx("div", { className: "text-2xl font-bold", children: formatCurrency(varianceAnalysis?.totalBudget || 0) }), _jsx("p", { className: "text-xs text-muted-foreground", children: "12-month projection" })] })] })] }), _jsxs(Tabs, { defaultValue: "budgets", className: "space-y-4", children: [_jsxs(TabsList, { children: [_jsx(TabsTrigger, { value: "budgets", children: "Budgets" }), _jsx(TabsTrigger, { value: "variance", children: "Variance Analysis" }), _jsx(TabsTrigger, { value: "forecast", children: "Forecast" })] }), _jsx(TabsContent, { value: "budgets", className: "space-y-4", children: _jsxs(Card, { children: [_jsxs(CardHeader, { children: [_jsx(CardTitle, { children: "Budget Management" }), _jsx(CardDescription, { children: "View and manage all budgets" })] }), _jsx(CardContent, { children: _jsxs(Table, { children: [_jsx(TableHeader, { children: _jsxs(TableRow, { children: [_jsx(TableHead, { children: "Budget Name" }), _jsx(TableHead, { children: "Type" }), _jsx(TableHead, { children: "Period ID" }), _jsx(TableHead, { className: "text-right", children: "Total Amount" }), _jsx(TableHead, { children: "Status" }), _jsx(TableHead, { children: "Created" }), _jsx(TableHead, { children: "Actions" })] }) }), _jsx(TableBody, { children: budgets.map((budget) => (_jsxs(TableRow, { children: [_jsx(TableCell, { className: "font-medium", children: budget.name }), _jsx(TableCell, { children: budget.type }), _jsx(TableCell, { children: budget.periodId }), _jsx(TableCell, { className: "text-right font-medium", children: formatCurrency(budget.totalAmount) }), _jsx(TableCell, { children: _jsx(Badge, { className: getBudgetStatusColor(budget.status), children: budget.status }) }), _jsx(TableCell, { children: formatDate(budget.createdAt) }), _jsx(TableCell, { children: _jsx(Button, { variant: "outline", size: "sm", children: "Edit" }) })] }, budget.id))) })] }) })] }) }), _jsx(TabsContent, { value: "variance", className: "space-y-4", children: _jsxs(Card, { children: [_jsxs(CardHeader, { children: [_jsx(CardTitle, { children: "Budget vs Actual Analysis" }), _jsx(CardDescription, { children: "Compare budgeted amounts with actual spending" })] }), _jsx(CardContent, { children: _jsxs("div", { className: "text-center py-8 text-muted-foreground", children: [_jsx(PieChart, { className: "h-12 w-12 mx-auto mb-4 opacity-50" }), _jsx("p", { children: "Variance analysis will be displayed here" }), _jsx("p", { className: "text-sm", children: "Budget vs actual spending by category" })] }) })] }) }), _jsx(TabsContent, { value: "forecast", className: "space-y-4", children: _jsxs(Card, { children: [_jsxs(CardHeader, { children: [_jsx(CardTitle, { children: "Financial Forecast" }), _jsx(CardDescription, { children: "12-month financial projection based on historical data and trends" })] }), _jsx(CardContent, { children: _jsxs("div", { className: "text-center py-8 text-muted-foreground", children: [_jsx(TrendingUp, { className: "h-12 w-12 mx-auto mb-4 opacity-50" }), _jsx("p", { children: "Financial forecast will be displayed here" }), _jsx("p", { className: "text-sm", children: "Revenue and expense projections" })] }) })] }) })] })] }));
};
export default BudgetForecast;
