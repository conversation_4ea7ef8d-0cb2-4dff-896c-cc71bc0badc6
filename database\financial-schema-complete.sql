-- =====================================================
-- 🚀 COMPLETE FINANCIAL MODULE DATABASE SCHEMA
-- =====================================================
-- This creates all tables needed for the Financial Dashboard
-- Run this in Supabase SQL Editor to fix the "relation does not exist" errors

-- Enable RLS
ALTER DATABASE postgres SET row_security = on;

-- =====================================================
-- 1. CHART OF ACCOUNTS
-- =====================================================
CREATE TABLE IF NOT EXISTS public.chart_of_accounts (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    organization_id UUID NOT NULL,
    account_code VARCHAR(20) NOT NULL,
    account_name VARCHAR(255) NOT NULL,
    account_type VARCHAR(50) NOT NULL CHECK (account_type IN ('ASSET', 'LIABILITY', 'EQUITY', 'REVENUE', 'EXPENSE')),
    parent_account_id UUID REFERENCES public.chart_of_accounts(id),
    is_active BOOLEAN DEFAULT true,
    description TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(organization_id, account_code)
);

-- =====================================================
-- 2. JOURNAL ENTRIES
-- =====================================================
CREATE TABLE IF NOT EXISTS public.journal_entries (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    organization_id UUID NOT NULL,
    entry_number VARCHAR(50) NOT NULL,
    entry_date DATE NOT NULL,
    description TEXT NOT NULL,
    reference_type VARCHAR(50),
    reference_id UUID,
    total_debit DECIMAL(15,2) NOT NULL DEFAULT 0,
    total_credit DECIMAL(15,2) NOT NULL DEFAULT 0,
    status VARCHAR(20) DEFAULT 'DRAFT' CHECK (status IN ('DRAFT', 'POSTED', 'REVERSED')),
    created_by UUID,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(organization_id, entry_number)
);

-- =====================================================
-- 3. JOURNAL ENTRY LINES
-- =====================================================
CREATE TABLE IF NOT EXISTS public.journal_entry_lines (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    journal_entry_id UUID NOT NULL REFERENCES public.journal_entries(id) ON DELETE CASCADE,
    account_id UUID NOT NULL REFERENCES public.chart_of_accounts(id),
    description TEXT,
    debit_amount DECIMAL(15,2) DEFAULT 0,
    credit_amount DECIMAL(15,2) DEFAULT 0,
    line_number INTEGER NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    CONSTRAINT valid_amounts CHECK (
        (debit_amount > 0 AND credit_amount = 0) OR 
        (credit_amount > 0 AND debit_amount = 0)
    )
);

-- =====================================================
-- 4. INVOICES (ACCOUNTS RECEIVABLE)
-- =====================================================
CREATE TABLE IF NOT EXISTS public.invoices (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    organization_id UUID NOT NULL,
    invoice_number VARCHAR(50) NOT NULL,
    customer_id UUID,
    customer_name VARCHAR(255) NOT NULL,
    invoice_date DATE NOT NULL,
    due_date DATE NOT NULL,
    subtotal DECIMAL(15,2) NOT NULL DEFAULT 0,
    tax_amount DECIMAL(15,2) NOT NULL DEFAULT 0,
    total_amount DECIMAL(15,2) NOT NULL DEFAULT 0,
    paid_amount DECIMAL(15,2) NOT NULL DEFAULT 0,
    balance_due DECIMAL(15,2) GENERATED ALWAYS AS (total_amount - paid_amount) STORED,
    status VARCHAR(20) DEFAULT 'DRAFT' CHECK (status IN ('DRAFT', 'SENT', 'PAID', 'OVERDUE', 'CANCELLED')),
    notes TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(organization_id, invoice_number)
);

-- =====================================================
-- 5. BILLS (ACCOUNTS PAYABLE)
-- =====================================================
CREATE TABLE IF NOT EXISTS public.bills (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    organization_id UUID NOT NULL,
    bill_number VARCHAR(50) NOT NULL,
    vendor_id UUID,
    vendor_name VARCHAR(255) NOT NULL,
    bill_date DATE NOT NULL,
    due_date DATE NOT NULL,
    subtotal DECIMAL(15,2) NOT NULL DEFAULT 0,
    tax_amount DECIMAL(15,2) NOT NULL DEFAULT 0,
    total_amount DECIMAL(15,2) NOT NULL DEFAULT 0,
    paid_amount DECIMAL(15,2) NOT NULL DEFAULT 0,
    balance_due DECIMAL(15,2) GENERATED ALWAYS AS (total_amount - paid_amount) STORED,
    status VARCHAR(20) DEFAULT 'DRAFT' CHECK (status IN ('DRAFT', 'RECEIVED', 'PAID', 'OVERDUE', 'CANCELLED')),
    notes TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(organization_id, bill_number)
);

-- =====================================================
-- 6. CASH ACCOUNTS
-- =====================================================
CREATE TABLE IF NOT EXISTS public.cash_accounts (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    organization_id UUID NOT NULL,
    account_name VARCHAR(255) NOT NULL,
    account_type VARCHAR(50) NOT NULL CHECK (account_type IN ('CHECKING', 'SAVINGS', 'CASH', 'CREDIT_CARD')),
    bank_name VARCHAR(255),
    account_number VARCHAR(50),
    current_balance DECIMAL(15,2) NOT NULL DEFAULT 0,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- =====================================================
-- 7. GENERAL LEDGER ENTRIES (COMPUTED VIEW)
-- =====================================================
CREATE TABLE IF NOT EXISTS public.general_ledger_entries (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    organization_id UUID NOT NULL,
    account_id UUID NOT NULL REFERENCES public.chart_of_accounts(id),
    account_code VARCHAR(20) NOT NULL,
    account_name VARCHAR(255) NOT NULL,
    transaction_date DATE NOT NULL,
    description TEXT NOT NULL,
    debit_amount DECIMAL(15,2) DEFAULT 0,
    credit_amount DECIMAL(15,2) DEFAULT 0,
    running_balance DECIMAL(15,2) DEFAULT 0,
    reference_type VARCHAR(50),
    reference_id UUID,
    journal_entry_id UUID REFERENCES public.journal_entries(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- =====================================================
-- 8. INSERT SAMPLE DATA FOR TESTING
-- =====================================================

-- Sample organization (use your actual organization ID)
DO $$
DECLARE
    org_id UUID := '********-0000-0000-0000-************';
    cash_account_id UUID;
    revenue_account_id UUID;
    expense_account_id UUID;
    ar_account_id UUID;
    ap_account_id UUID;
BEGIN
    -- Insert sample chart of accounts
    INSERT INTO public.chart_of_accounts (id, organization_id, account_code, account_name, account_type, description) VALUES
    (gen_random_uuid(), org_id, '1000', 'Cash and Cash Equivalents', 'ASSET', 'Primary cash account'),
    (gen_random_uuid(), org_id, '1200', 'Accounts Receivable', 'ASSET', 'Money owed by customers'),
    (gen_random_uuid(), org_id, '2000', 'Accounts Payable', 'LIABILITY', 'Money owed to vendors'),
    (gen_random_uuid(), org_id, '4000', 'Sales Revenue', 'REVENUE', 'Revenue from sales'),
    (gen_random_uuid(), org_id, '5000', 'Operating Expenses', 'EXPENSE', 'General operating expenses')
    ON CONFLICT (organization_id, account_code) DO NOTHING;

    -- Get account IDs for sample data
    SELECT id INTO cash_account_id FROM public.chart_of_accounts WHERE organization_id = org_id AND account_code = '1000';
    SELECT id INTO ar_account_id FROM public.chart_of_accounts WHERE organization_id = org_id AND account_code = '1200';
    SELECT id INTO ap_account_id FROM public.chart_of_accounts WHERE organization_id = org_id AND account_code = '2000';
    SELECT id INTO revenue_account_id FROM public.chart_of_accounts WHERE organization_id = org_id AND account_code = '4000';
    SELECT id INTO expense_account_id FROM public.chart_of_accounts WHERE organization_id = org_id AND account_code = '5000';

    -- Insert sample cash account
    INSERT INTO public.cash_accounts (organization_id, account_name, account_type, current_balance) VALUES
    (org_id, 'Main Checking Account', 'CHECKING', 125000.00)
    ON CONFLICT DO NOTHING;

    -- Insert sample invoices
    INSERT INTO public.invoices (organization_id, invoice_number, customer_name, invoice_date, due_date, total_amount, paid_amount, status) VALUES
    (org_id, 'INV-001', 'Acme Corporation', CURRENT_DATE - INTERVAL '30 days', CURRENT_DATE - INTERVAL '0 days', 15000.00, 15000.00, 'PAID'),
    (org_id, 'INV-002', 'Tech Solutions Ltd', CURRENT_DATE - INTERVAL '15 days', CURRENT_DATE + INTERVAL '15 days', 8500.00, 0.00, 'SENT'),
    (org_id, 'INV-003', 'Global Industries', CURRENT_DATE - INTERVAL '45 days', CURRENT_DATE - INTERVAL '15 days', 12000.00, 0.00, 'OVERDUE'),
    (org_id, 'INV-004', 'StartUp Inc', CURRENT_DATE - INTERVAL '5 days', CURRENT_DATE + INTERVAL '25 days', 6750.00, 0.00, 'SENT')
    ON CONFLICT (organization_id, invoice_number) DO NOTHING;

    -- Insert sample bills
    INSERT INTO public.bills (organization_id, bill_number, vendor_name, bill_date, due_date, total_amount, paid_amount, status) VALUES
    (org_id, 'BILL-001', 'Office Supplies Co', CURRENT_DATE - INTERVAL '20 days', CURRENT_DATE + INTERVAL '10 days', 2500.00, 0.00, 'RECEIVED'),
    (org_id, 'BILL-002', 'Software Licensing', CURRENT_DATE - INTERVAL '10 days', CURRENT_DATE + INTERVAL '20 days', 5000.00, 0.00, 'RECEIVED'),
    (org_id, 'BILL-003', 'Utilities Company', CURRENT_DATE - INTERVAL '25 days', CURRENT_DATE + INTERVAL '5 days', 1200.00, 1200.00, 'PAID')
    ON CONFLICT (organization_id, bill_number) DO NOTHING;

    -- Insert sample journal entries
    INSERT INTO public.journal_entries (organization_id, entry_number, entry_date, description, total_debit, total_credit, status) VALUES
    (org_id, 'JE-001', CURRENT_DATE - INTERVAL '30 days', 'Sales Revenue - Acme Corp', 15000.00, 15000.00, 'POSTED'),
    (org_id, 'JE-002', CURRENT_DATE - INTERVAL '20 days', 'Office Supplies Purchase', 2500.00, 2500.00, 'POSTED'),
    (org_id, 'JE-003', CURRENT_DATE - INTERVAL '15 days', 'Sales Revenue - Tech Solutions', 8500.00, 8500.00, 'POSTED')
    ON CONFLICT (organization_id, entry_number) DO NOTHING;

END $$;

-- =====================================================
-- 9. ENABLE ROW LEVEL SECURITY
-- =====================================================
ALTER TABLE public.chart_of_accounts ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.journal_entries ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.journal_entry_lines ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.invoices ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.bills ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.cash_accounts ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.general_ledger_entries ENABLE ROW LEVEL SECURITY;

-- =====================================================
-- 10. CREATE RLS POLICIES (BASIC - EXPAND AS NEEDED)
-- =====================================================
CREATE POLICY "Users can access their organization's financial data" ON public.chart_of_accounts
    FOR ALL USING (organization_id::text = current_setting('app.current_organization_id', true));

CREATE POLICY "Users can access their organization's journal entries" ON public.journal_entries
    FOR ALL USING (organization_id::text = current_setting('app.current_organization_id', true));

CREATE POLICY "Users can access their organization's journal entry lines" ON public.journal_entry_lines
    FOR ALL USING (
        journal_entry_id IN (
            SELECT id FROM public.journal_entries 
            WHERE organization_id::text = current_setting('app.current_organization_id', true)
        )
    );

CREATE POLICY "Users can access their organization's invoices" ON public.invoices
    FOR ALL USING (organization_id::text = current_setting('app.current_organization_id', true));

CREATE POLICY "Users can access their organization's bills" ON public.bills
    FOR ALL USING (organization_id::text = current_setting('app.current_organization_id', true));

CREATE POLICY "Users can access their organization's cash accounts" ON public.cash_accounts
    FOR ALL USING (organization_id::text = current_setting('app.current_organization_id', true));

CREATE POLICY "Users can access their organization's general ledger" ON public.general_ledger_entries
    FOR ALL USING (organization_id::text = current_setting('app.current_organization_id', true));

-- =====================================================
-- 11. CREATE INDEXES FOR PERFORMANCE
-- =====================================================
CREATE INDEX IF NOT EXISTS idx_chart_of_accounts_org_code ON public.chart_of_accounts(organization_id, account_code);
CREATE INDEX IF NOT EXISTS idx_journal_entries_org_date ON public.journal_entries(organization_id, entry_date);
CREATE INDEX IF NOT EXISTS idx_invoices_org_status ON public.invoices(organization_id, status);
CREATE INDEX IF NOT EXISTS idx_bills_org_status ON public.bills(organization_id, status);
CREATE INDEX IF NOT EXISTS idx_general_ledger_org_account_date ON public.general_ledger_entries(organization_id, account_id, transaction_date);

-- =====================================================
-- SUCCESS MESSAGE
-- =====================================================
DO $$
BEGIN
    RAISE NOTICE '🚀 FINANCIAL SCHEMA CREATED SUCCESSFULLY!';
    RAISE NOTICE '✅ All tables created with sample data';
    RAISE NOTICE '✅ RLS policies enabled';
    RAISE NOTICE '✅ Performance indexes created';
    RAISE NOTICE '🎯 Financial Dashboard should now work!';
END $$;
