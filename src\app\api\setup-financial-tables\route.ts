// import { NextRequest, NextResponse } from 'next/server'; // Commented out due to non-Next.js environment
import { supabaseAdmin as supabase } from '@/integrations/supabase'; // Import and alias the admin client

// Note: supabaseUrl and supabaseServiceKey are still needed for direct fetch calls if those are kept.
// Consider refactoring direct fetch calls to use the supabase client if possible.
const supabaseUrl = 'https://yunrxkbvbwyyhkxuhror.supabase.co'; // Keep for direct fetch
const supabaseServiceKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.******************************************************************************************************************************************.b9tAVUvcrKd5QMe3AFXsoe-k2DJ46eLh2VAAec0-tc4'; // Keep for direct fetch

const createTablesSQL = `
-- Chart of Accounts
CREATE TABLE IF NOT EXISTS chart_of_accounts (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    account_code VARCHAR(20) UNIQUE NOT NULL,
    account_name VARCHAR(255) NOT NULL,
    account_type VARCHAR(50) NOT NULL CHECK (account_type IN ('Asset', 'Liability', 'Equity', 'Revenue', 'Expense')),
    account_subtype VARCHAR(100),
    parent_account_id UUID REFERENCES chart_of_accounts(id),
    description TEXT,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- General Ledger
CREATE TABLE IF NOT EXISTS general_ledger (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    account_id UUID NOT NULL REFERENCES chart_of_accounts(id),
    transaction_date DATE NOT NULL,
    description TEXT NOT NULL,
    reference_number VARCHAR(100),
    reference_type VARCHAR(50),
    debit_amount DECIMAL(15,2) DEFAULT 0,
    credit_amount DECIMAL(15,2) DEFAULT 0,
    running_balance DECIMAL(15,2),
    created_by UUID,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    CONSTRAINT valid_amounts CHECK (
        (debit_amount > 0 AND credit_amount = 0) OR 
        (credit_amount > 0 AND debit_amount = 0)
    )
);

-- Journal Entries
CREATE TABLE IF NOT EXISTS journal_entries (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    entry_number VARCHAR(50) UNIQUE NOT NULL,
    entry_date DATE NOT NULL,
    description TEXT NOT NULL,
    reference_number VARCHAR(100),
    total_debits DECIMAL(15,2) NOT NULL,
    total_credits DECIMAL(15,2) NOT NULL,
    status VARCHAR(20) DEFAULT 'Draft' CHECK (status IN ('Draft', 'Posted', 'Reversed')),
    created_by UUID,
    posted_by UUID,
    posted_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    CONSTRAINT balanced_entry CHECK (total_debits = total_credits)
);

-- Journal Entry Lines
CREATE TABLE IF NOT EXISTS journal_entry_lines (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    journal_entry_id UUID NOT NULL REFERENCES journal_entries(id) ON DELETE CASCADE,
    account_id UUID NOT NULL REFERENCES chart_of_accounts(id),
    description TEXT,
    debit_amount DECIMAL(15,2) DEFAULT 0,
    credit_amount DECIMAL(15,2) DEFAULT 0,
    line_number INTEGER NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    CONSTRAINT valid_line_amounts CHECK (
        (debit_amount > 0 AND credit_amount = 0) OR 
        (credit_amount > 0 AND debit_amount = 0)
    )
);

-- Accounts Receivable
CREATE TABLE IF NOT EXISTS accounts_receivable (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    invoice_number VARCHAR(100) UNIQUE NOT NULL,
    customer_id UUID NOT NULL,
    invoice_date DATE NOT NULL,
    due_date DATE NOT NULL,
    amount DECIMAL(15,2) NOT NULL,
    amount_due DECIMAL(15,2) NOT NULL,
    status VARCHAR(20) DEFAULT 'Outstanding' CHECK (status IN ('Outstanding', 'Partial', 'Paid', 'Overdue', 'Written Off')),
    terms VARCHAR(50),
    description TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Accounts Payable
CREATE TABLE IF NOT EXISTS accounts_payable (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    invoice_number VARCHAR(100) NOT NULL,
    vendor_id UUID NOT NULL,
    invoice_date DATE NOT NULL,
    due_date DATE NOT NULL,
    amount DECIMAL(15,2) NOT NULL,
    amount_due DECIMAL(15,2) NOT NULL,
    status VARCHAR(20) DEFAULT 'Outstanding' CHECK (status IN ('Outstanding', 'Partial', 'Paid', 'Overdue')),
    terms VARCHAR(50),
    description TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Financial Reports
CREATE TABLE IF NOT EXISTS financial_reports (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(255) NOT NULL,
    type VARCHAR(50) NOT NULL,
    period_start DATE NOT NULL,
    period_end DATE NOT NULL,
    generated_at TIMESTAMP WITH TIME ZONE NOT NULL,
    generated_by UUID,
    data JSONB NOT NULL,
    format VARCHAR(20) DEFAULT 'JSON',
    status VARCHAR(20) DEFAULT 'Generated',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes
CREATE INDEX IF NOT EXISTS idx_general_ledger_account_date ON general_ledger(account_id, transaction_date);
CREATE INDEX IF NOT EXISTS idx_general_ledger_date ON general_ledger(transaction_date);
CREATE INDEX IF NOT EXISTS idx_accounts_receivable_customer ON accounts_receivable(customer_id);
CREATE INDEX IF NOT EXISTS idx_accounts_receivable_status ON accounts_receivable(status);
CREATE INDEX IF NOT EXISTS idx_accounts_payable_vendor ON accounts_payable(vendor_id);
CREATE INDEX IF NOT EXISTS idx_accounts_payable_status ON accounts_payable(status);
CREATE INDEX IF NOT EXISTS idx_chart_accounts_type ON chart_of_accounts(account_type);
CREATE INDEX IF NOT EXISTS idx_chart_accounts_code ON chart_of_accounts(account_code);
`;

const sampleAccountsSQL = `
INSERT INTO chart_of_accounts (account_code, account_name, account_type, account_subtype, description) VALUES
('1000', 'Cash', 'Asset', 'Current Asset', 'Cash on hand and in bank accounts'),
('1100', 'Accounts Receivable', 'Asset', 'Current Asset', 'Money owed by customers'),
('1200', 'Inventory', 'Asset', 'Current Asset', 'Goods held for sale'),
('1500', 'Equipment', 'Asset', 'Fixed Asset', 'Business equipment and machinery'),
('2000', 'Accounts Payable', 'Liability', 'Current Liability', 'Money owed to vendors'),
('2100', 'Accrued Expenses', 'Liability', 'Current Liability', 'Expenses incurred but not yet paid'),
('3000', 'Owner''s Equity', 'Equity', 'Owner''s Equity', 'Owner''s investment in the business'),
('3100', 'Retained Earnings', 'Equity', 'Retained Earnings', 'Accumulated profits retained in business'),
('4000', 'Sales Revenue', 'Revenue', 'Operating Revenue', 'Revenue from primary business operations'),
('4100', 'Service Revenue', 'Revenue', 'Operating Revenue', 'Revenue from services provided'),
('5000', 'Cost of Goods Sold', 'Expense', 'Cost of Goods Sold', 'Direct costs of producing goods sold'),
('6000', 'Operating Expenses', 'Expense', 'Operating Expense', 'General business operating expenses')
ON CONFLICT (account_code) DO NOTHING;
`;

export async function POST(request: any) { // Changed NextRequest to any
  try {
    console.log('Starting financial tables setup...');

    // Execute table creation SQL using raw query
    const { error: createError } = await supabase.rpc('exec', {
      sql: createTablesSQL
    });

    if (createError) {
      console.error('Error creating tables:', createError);
      // Try alternative approach - direct SQL execution
      const response = await fetch(`${supabaseUrl}/rest/v1/rpc/exec`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${supabaseServiceKey}`,
          'apikey': supabaseServiceKey
        },
        body: JSON.stringify({ sql: createTablesSQL })
      });

      if (!response.ok) {
        throw new Error(`Failed to create tables: ${response.statusText}`);
      }
    }

    console.log('Tables created successfully');

    // Insert sample accounts
    const { error: insertError } = await supabase.rpc('exec', {
      sql: sampleAccountsSQL
    });

    if (insertError) {
      console.error('Error inserting sample data:', insertError);
      // Try direct insertion
      const response = await fetch(`${supabaseUrl}/rest/v1/rpc/exec`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${supabaseServiceKey}`,
          'apikey': supabaseServiceKey
        },
        body: JSON.stringify({ sql: sampleAccountsSQL })
      });

      if (!response.ok) {
        console.warn('Failed to insert sample data, but tables created');
      }
    }

    // Verify setup by querying chart_of_accounts
    const { data: accounts, error: queryError } = await supabase
      .from('chart_of_accounts')
      .select('account_code, account_name, account_type')
      .limit(5);

    if (queryError) {
      throw new Error(`Setup verification failed: ${queryError.message}`);
    }

    return Response.json({ // Changed NextResponse to Response
      success: true,
      message: 'Financial tables created successfully',
      sampleAccounts: accounts,
      tablesCreated: [
        'chart_of_accounts',
        'general_ledger', 
        'journal_entries',
        'journal_entry_lines',
        'accounts_receivable',
        'accounts_payable',
        'financial_reports'
      ]
    });

  } catch (error) {
    console.error('Setup failed:', error);
    return Response.json({ // Changed NextResponse to Response
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error occurred'
    }, { status: 500 });
  }
}

export async function GET() {
  try {
    // Check if tables exist by querying chart_of_accounts
    const { data, error } = await supabase
      .from('chart_of_accounts')
      .select('count(*)')
      .limit(1);

    if (error) {
      return Response.json({ // Changed NextResponse to Response
        tablesExist: false,
        error: error.message
      });
    }

    return Response.json({ // Changed NextResponse to Response
      tablesExist: true,
      message: 'Financial tables are already set up'
    });

  } catch (error) {
    return Response.json({ // Changed NextResponse to Response
      tablesExist: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
}
