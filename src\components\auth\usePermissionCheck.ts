import { useBetterAuth } from '@/providers/BetterAuthProvider';
import { Permission, UserRole } from '@/auth/utils/rbac/permissions';

// Hook for checking permissions in components
export const usePermissionCheck = () => {
  const { 
    user, 
    hasPermission: contextHasPermission, 
    hasAnyPermission: contextHasAnyPermission,
    isRole: contextIsRole,
    isRoleOrHigher: contextIsRoleOrHigher
  } = useBetterAuth();

  const hasAnyPermission = (permissions: Permission[]): boolean => {
    if (!user) return false;
    return contextHasAnyPermission(permissions);
  };

  const hasAllPermissions = (permissions: Permission[]): boolean => {
    if (!user) return false;
    return permissions.every(permission => contextHasPermission(permission));
  };

  const isRole = (role: UserRole): boolean => {
    return contextIsRole(role);
  };

  const isRoleOrHigher = (role: UserRole): boolean => {
    return contextIsRoleOrHigher(role);
  };

  return {
    user, // Expose user if needed by consumers of this hook
    hasPermission: contextHasPermission, // Expose the original hasPermission from context
    hasAnyPermission,
    hasAllPermissions,
    isRole,
    isRoleOrHigher,
    checkAccess: (permissions: Permission[], requireAll = false) => {
      return requireAll ? hasAllPermissions(permissions) : hasAnyPermission(permissions);
    }
  };
};

// Export for backward compatibility
export default usePermissionCheck;
