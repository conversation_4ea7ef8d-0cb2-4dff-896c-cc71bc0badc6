FROM node:20-alpine

WORKDIR /app

# Install dependencies
RUN npm install -g @modelcontextprotocol/sdk @supabase/mcp-server-supabase

# Copy server files
COPY package.json ./
COPY server.js ./

# Install project dependencies
RUN npm install

# Create data directory
RUN mkdir -p /data && chmod 755 /data

# Expose port
EXPOSE 8087

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:8087/health || exit 1

# Start server
CMD ["node", "server.js"]
