#!/usr/bin/env node

const { Client } = require('pg');
require('dotenv').config();

async function verifyDatabaseUsers() {
  const client = new Client({
    connectionString: process.env.DATABASE_URL,
    ssl: process.env.NODE_ENV === 'production' ? { rejectUnauthorized: false } : false
  });

  try {
    await client.connect();
    console.log('✅ Connected to database');

    // Check if Better Auth tables exist
    const tablesQuery = `
      SELECT table_name 
      FROM information_schema.tables 
      WHERE table_schema = 'public' 
      AND table_name IN ('user', 'account', 'session', 'verification')
      ORDER BY table_name;
    `;
    
    const tablesResult = await client.query(tablesQuery);
    console.log('\n🔍 Better Auth Tables:');
    tablesResult.rows.forEach(row => {
      console.log(`   ✅ ${row.table_name}`);
    });

    if (tablesResult.rows.length < 4) {
      console.log('❌ Missing Better Auth tables!');
      return;
    }

    // Check test users
    const usersQuery = `
      SELECT u.id, u.email, u."emailVerified", u."createdAt",
             COUNT(a.id) as account_count,
             BOOL_OR(a.password IS NOT NULL) as has_password
      FROM "user" u
      LEFT JOIN "account" a ON u.id = a."userId"
      WHERE u.email IN ($1, $2, $3)
      GROUP BY u.id, u.email, u."emailVerified", u."createdAt"
      ORDER BY u.email;
    `;

    const testEmails = [
      '<EMAIL>',
      '<EMAIL>', 
      '<EMAIL>'
    ];

    const usersResult = await client.query(usersQuery, testEmails);
    
    console.log('\n🔍 Test Users Status:');
    if (usersResult.rows.length === 0) {
      console.log('❌ No test users found in database!');
      console.log('   Expected users:', testEmails.join(', '));
    } else {
      usersResult.rows.forEach(user => {
        console.log(`   📧 ${user.email}`);
        console.log(`      ID: ${user.id}`);
        console.log(`      Verified: ${user.emailVerified}`);
        console.log(`      Has Password: ${user.has_password}`);
        console.log(`      Accounts: ${user.account_count}`);
        console.log(`      Created: ${user.createdAt}`);
        console.log('');
      });
    }

    // Check account details for credential provider
    const accountsQuery = `
      SELECT u.email, a."providerId", a.password IS NOT NULL as has_password,
             LENGTH(a.password) as password_length, a."createdAt"
      FROM "account" a
      JOIN "user" u ON a."userId" = u.id
      WHERE u.email IN ($1, $2, $3)
      AND a."providerId" = 'credential'
      ORDER BY u.email;
    `;

    const accountsResult = await client.query(accountsQuery, testEmails);
    
    console.log('🔍 Password Accounts:');
    if (accountsResult.rows.length === 0) {
      console.log('❌ No password accounts found!');
    } else {
      accountsResult.rows.forEach(account => {
        console.log(`   📧 ${account.email}`);
        console.log(`      Provider: ${account.providerId}`);
        console.log(`      Has Password: ${account.has_password}`);
        console.log(`      Password Length: ${account.password_length || 'N/A'}`);
        console.log(`      Created: ${account.createdAt}`);
        console.log('');
      });
    }

  } catch (error) {
    console.error('❌ Database error:', error.message);
  } finally {
    await client.end();
  }
}

verifyDatabaseUsers().catch(console.error);