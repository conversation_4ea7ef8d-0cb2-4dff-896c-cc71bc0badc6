import React, { useMemo, useCallback, useEffect } from 'react';
import { NavCategory, NavItem } from './types';
import { SidebarItem } from './SidebarItem';
import { cn } from '@/lib/utils';
import process from "node:process";

interface SidebarNavListProps {
  categories?: NavCategory[];
  items?: NavItem[];
  activeItemKey?: string;
  onItemClick?: (key: string) => void;
  isCollapsed?: boolean;
  textColor?: string;
  textHoverColor?: string;
  activeBgColor?: string;
  activeTextColor?: string;
  hoverBgColor?: string;
  expandedCategories?: string[];
  onCategoryToggle?: (categoryName: string) => void;
  userRole?: string;
  expandedItems?: string[];
  onToggleExpand?: (label: string) => void;
}

export function SidebarNavList({
  categories = [],
  items = [],
  activeItemKey,
  onItemClick,
  isCollapsed,
  textColor = "text-gray-600",
  textHoverColor = "hover:text-gray-900",
  activeBgColor = "bg-gray-100",
  activeTextColor = "text-gray-900",
  hoverBgColor = "hover:bg-gray-50",
  expandedCategories = [],
  onCategoryToggle,
  userRole,
  expandedItems,
  onToggleExpand
}: SidebarNavListProps) {

  // Memoized function to check if an item or any of its children has the active path
  const isItemActive = useCallback((item: NavItem): boolean => {
    // Get current location from window if activeItemKey isn't provided
    const currentPath = activeItemKey || globalThis.location.pathname;
    
    if (currentPath === item.path || currentPath === item.href) {
      return true;
    }

    // Check children if they exist
    if (item.children && item.children.length > 0) {
      return item.children.some(child => isItemActive(child));
    }

    return false;
  }, [activeItemKey]);

  // Memoized function to filter items by role
  const filterItemsByRole = useCallback((items: NavItem[], role?: string): NavItem[] => {
    if (!role) return items;
    
    return items.filter(item => {
      // If no roles specified, everyone can see it
      if (!item.roles || item.roles.length === 0) return true;
      
      // Otherwise, check if user role is in the allowed roles
      return item.roles.includes(role);
    });
  }, []);

  // Memoized computation of all categories
  const allCategories = useMemo(() => {
    return categories.length > 0 ? categories :
      items.length > 0 ? [{
        name: 'default',
        label: 'Navigation',
        items: items
      }] : [];
  }, [categories, items]);
  
  // Memoized processing of categories - SHOW ALL ITEMS REGARDLESS OF PERMISSIONS
  const processedCategories = useMemo(() => {
    // Return all categories and items without filtering by role
    return allCategories;
  }, [allCategories]);
  
  // Auto-expand categories that contain active items
  useEffect(() => {
    if (!onCategoryToggle || expandedCategories.length > 0) return;
    
    // Find categories with active items and expand them
    processedCategories.forEach(category => {
      if (category.items && category.items.some(item => isItemActive(item))) {
        if (category.name) {
          onCategoryToggle(category.name);
        }
      }
    });
  }, [processedCategories, isItemActive, onCategoryToggle, expandedCategories]);
  
  // Debug logging
  console.log('SidebarNavList - processedCategories:', processedCategories);
  console.log('SidebarNavList - categories length:', processedCategories.length);
  
  if (processedCategories.length === 0) {
    console.log('SidebarNavList - No categories to render');
    return null;
  }
  
  return (
    <div className={cn("space-y-4", isCollapsed && "items-center")}>
      {processedCategories.map((category) => {
        const isExpanded = expandedCategories.includes(category.name || '') || 
                          (expandedItems && category.label && expandedItems.includes(category.label));
        
        return (
          <div key={category.name || category.label} className="space-y-1">
            {!isCollapsed && (
              <h3 
                className={cn(
                  "flex items-center justify-between text-sm font-medium px-3 py-1.5 rounded-md cursor-pointer",
                  textColor, 
                  textHoverColor
                )}
                onClick={() => {
                  if (onCategoryToggle && category.name) {
                    onCategoryToggle(category.name);
                  } else if (onToggleExpand && category.label) {
                    onToggleExpand(category.label);
                  }
                }}
              >
                {category.label || category.name}
                {category.items && category.items.length > 0 && (
                  <span className={cn("transform transition-transform", isExpanded ? "rotate-180" : "")}>
                    ▼
                  </span>
                )}
              </h3>
            )}
            
            {(!isCollapsed || !onCategoryToggle) && (isExpanded || !onCategoryToggle) && category.items && (
              <div className="pt-1 pl-1">
                {/* Add debug info */}
                {import.meta.env.DEV && false && (
                  <div className="text-xs text-gray-400 italic">Debug: {category.items.length} items</div>
                )}
                {category.items.map((item) => {
                  const isActive = isItemActive(item);
                  
                  // Determine if this item has an active child
                  const hasActiveChild = item.children?.some(child => isItemActive(child)) || false;
                  
                  return (
                    <SidebarItem
                      key={item.label}
                      item={item}
                      isActive={isActive || hasActiveChild}
                      textColor={textColor}
                      textHoverColor={textHoverColor}
                      activeBgColor={activeBgColor}
                      activeTextColor={activeTextColor}
                      hoverBgColor={hoverBgColor}
                      onClick={() => onItemClick && onItemClick(item.path || item.href || item.label)}
                    />
                  );
                })}
              </div>
            )}
          </div>
        );
      })}
    </div>
  );
}
