# Complete MCP servers setup script
set -e

echo "🚀 Completing MCP servers setup for NXT-WEB-DEV-X..."

# Create remaining server directories
echo "📁 Creating remaining server directories..."
bash .devcontainer/scripts/create-remaining-servers.sh

# Create simplified server implementations for the remaining servers
echo "💻 Creating server implementations..."

# Desktop Commander Server
cat > .devcontainer/mcp-servers/desktop-commander/server.js << 'EOF'
#!/usr/bin/env node
const { Server } = require('@modelcontextprotocol/sdk/server/index.js');
const { StdioServerTransport } = require('@modelcontextprotocol/sdk/server/stdio.js');
const { CallToolRequestSchema, ListToolsRequestSchema } = require('@modelcontextprotocol/sdk/types.js');
const express = require('express');
const { exec } = require('child_process');

class DesktopCommanderServer {
  constructor() {
    this.server = new Server({ name: 'desktop-commander-server', version: '1.0.0' }, { capabilities: { tools: {} } });
    this.port = process.env.MCP_PORT || 8085;
    this.setupToolHandlers();
    this.setupHealthCheck();
  }

  setupToolHandlers() {
    this.server.setRequestHandler(ListToolsRequestSchema, async () => ({
      tools: [
        {
          name: 'execute_command',
          description: 'Execute system commands safely',
          inputSchema: {
            type: 'object',
            properties: {
              command: { type: 'string', description: 'Command to execute' },
              safe_mode: { type: 'boolean', description: 'Enable safe mode', default: true }
            },
            required: ['command']
          }
        }
      ]
    }));

    this.server.setRequestHandler(CallToolRequestSchema, async (request) => {
      const { name, arguments: args } = request.params;
      if (name === 'execute_command') {
        return new Promise((resolve, reject) => {
          const safeCommands = ['ls', 'pwd', 'date', 'whoami', 'echo'];
          const cmd = args.command.split(' ')[0];
          
          if (args.safe_mode && !safeCommands.includes(cmd)) {
            reject(new Error(`Command '${cmd}' not allowed in safe mode`));
            return;
          }

          exec(args.command, (error, stdout, stderr) => {
            resolve({
              content: [{
                type: 'text',
                text: JSON.stringify({ stdout, stderr, error: error?.message }, null, 2)
              }]
            });
          });
        });
      }
      throw new Error(`Unknown tool: ${name}`);
    });
  }

  setupHealthCheck() {
    const app = express();
    app.get('/health', (req, res) => {
      res.json({ status: 'healthy', service: 'desktop-commander-mcp', timestamp: new Date().toISOString() });
    });
    app.listen(this.port, () => console.log(`Desktop Commander MCP Server health check running on port ${this.port}`));
  }

  async run() {
    const transport = new StdioServerTransport();
    await this.server.connect(transport);
    console.error('Desktop Commander MCP server running on stdio');
  }
}

const server = new DesktopCommanderServer();
server.run().catch(console.error);
EOF

# Taskmaster Server
cat > .devcontainer/mcp-servers/taskmaster/server.js << 'EOF'
#!/usr/bin/env node
const { Server } = require('@modelcontextprotocol/sdk/server/index.js');
const { StdioServerTransport } = require('@modelcontextprotocol/sdk/server/stdio.js');
const { CallToolRequestSchema, ListToolsRequestSchema } = require('@modelcontextprotocol/sdk/types.js');
const express = require('express');
const fs = require('fs').promises;
const path = require('path');

class TaskmasterServer {
  constructor() {
    this.server = new Server({ name: 'taskmaster-server', version: '1.0.0' }, { capabilities: { tools: {} } });
    this.port = process.env.MCP_PORT || 8086;
    this.tasksFile = '/data/tasks.json';
    this.setupToolHandlers();
    this.setupHealthCheck();
  }

  async loadTasks() {
    try {
      const data = await fs.readFile(this.tasksFile, 'utf8');
      return JSON.parse(data);
    } catch {
      return [];
    }
  }

  async saveTasks(tasks) {
    await fs.writeFile(this.tasksFile, JSON.stringify(tasks, null, 2));
  }

  setupToolHandlers() {
    this.server.setRequestHandler(ListToolsRequestSchema, async () => ({
      tools: [
        {
          name: 'create_task',
          description: 'Create a new task',
          inputSchema: {
            type: 'object',
            properties: {
              title: { type: 'string', description: 'Task title' },
              description: { type: 'string', description: 'Task description' },
              priority: { type: 'string', enum: ['low', 'medium', 'high'], default: 'medium' },
              due_date: { type: 'string', description: 'Due date (ISO format)' }
            },
            required: ['title']
          }
        },
        {
          name: 'list_tasks',
          description: 'List all tasks',
          inputSchema: {
            type: 'object',
            properties: {
              status: { type: 'string', enum: ['pending', 'completed', 'all'], default: 'all' }
            }
          }
        },
        {
          name: 'complete_task',
          description: 'Mark a task as completed',
          inputSchema: {
            type: 'object',
            properties: { id: { type: 'string', description: 'Task ID' } },
            required: ['id']
          }
        }
      ]
    }));

    this.server.setRequestHandler(CallToolRequestSchema, async (request) => {
      const { name, arguments: args } = request.params;
      
      if (name === 'create_task') {
        const tasks = await this.loadTasks();
        const newTask = {
          id: Date.now().toString(),
          title: args.title,
          description: args.description || '',
          priority: args.priority || 'medium',
          due_date: args.due_date || null,
          status: 'pending',
          created_at: new Date().toISOString()
        };
        tasks.push(newTask);
        await this.saveTasks(tasks);
        return { content: [{ type: 'text', text: JSON.stringify({ success: true, task: newTask }, null, 2) }] };
      }
      
      if (name === 'list_tasks') {
        const tasks = await this.loadTasks();
        const filtered = args.status === 'all' ? tasks : tasks.filter(t => t.status === args.status);
        return { content: [{ type: 'text', text: JSON.stringify({ tasks: filtered }, null, 2) }] };
      }
      
      if (name === 'complete_task') {
        const tasks = await this.loadTasks();
        const task = tasks.find(t => t.id === args.id);
        if (task) {
          task.status = 'completed';
          task.completed_at = new Date().toISOString();
          await this.saveTasks(tasks);
          return { content: [{ type: 'text', text: JSON.stringify({ success: true, task }, null, 2) }] };
        }
        throw new Error('Task not found');
      }
      
      throw new Error(`Unknown tool: ${name}`);
    });
  }

  setupHealthCheck() {
    const app = express();
    app.get('/health', (req, res) => {
      res.json({ status: 'healthy', service: 'taskmaster-mcp', timestamp: new Date().toISOString() });
    });
    app.listen(this.port, () => console.log(`Taskmaster MCP Server health check running on port ${this.port}`));
  }

  async run() {
    const transport = new StdioServerTransport();
    await this.server.connect(transport);
    console.error('Taskmaster MCP server running on stdio');
  }
}

const server = new TaskmasterServer();
server.run().catch(console.error);
EOF

# Supabase Server (wrapper)
cat > .devcontainer/mcp-servers/supabase/server.js << 'EOF'
#!/usr/bin/env node
const express = require('express');
const { spawn } = require('child_process');

class SupabaseMcpWrapper {
  constructor() {
    this.port = process.env.MCP_PORT || 8087;
    this.setupHealthCheck();
    this.startSupabaseServer();
  }

  startSupabaseServer() {
    const args = [
      '@supabase/mcp-server-supabase@latest',
      '--access-token', process.env.SUPABASE_ACCESS_TOKEN || '',
      '--project-id', process.env.SUPABASE_PROJECT_ID || ''
    ].filter(arg => arg !== '');

    this.supabaseProcess = spawn('npx', args, {
      stdio: ['pipe', 'pipe', 'pipe']
    });

    this.supabaseProcess.stdout.on('data', (data) => {
      console.log(`Supabase MCP: ${data}`);
    });

    this.supabaseProcess.stderr.on('data', (data) => {
      console.error(`Supabase MCP Error: ${data}`);
    });
  }

  setupHealthCheck() {
    const app = express();
    app.get('/health', (req, res) => {
      res.json({ 
        status: 'healthy', 
        service: 'supabase-mcp',
        timestamp: new Date().toISOString(),
        access_token_configured: !!process.env.SUPABASE_ACCESS_TOKEN
      });
    });
    app.listen(this.port, () => console.log(`Supabase MCP Server health check running on port ${this.port}`));
  }
}

new SupabaseMcpWrapper();
EOF

echo "✅ All MCP servers setup completed!"
echo ""
echo "🎯 Next Steps:"
echo "1. Update your .env file with actual API keys"
echo "2. Build all servers: bash .devcontainer/scripts/build-all-servers.sh"
echo "3. Start containers: docker-compose -f .devcontainer/docker-compose.yml up -d"
echo "4. Check health: node mcp-config/health-check.js"
echo ""
echo "📖 See MCP-SERVERS-README.md for detailed usage instructions"
