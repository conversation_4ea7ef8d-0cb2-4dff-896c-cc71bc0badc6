import React from 'react';
import RouteGuard from './RouteGuard'; // Default import
import { Permission, UserRole } from '@/types/rbac';

// Define props for RouteGuard specifically for the HOC
interface RouteGuardHocProps {
  requiredPermissions?: Permission[];
  requiredRole?: UserRole;
  requireAnyPermission?: boolean;
  fallbackPath?: string;
  showUnauthorized?: boolean;
}

/**
 * Higher-order component (HOC) for route protection
 * Wraps a component with RouteGuard to enforce authentication and permission checks
 * 
 * @param Component The component to wrap
 * @param guardProps RouteGuard configuration options
 */
export const withRouteGuard = <P extends object>(
  Component: React.ComponentType<P>,
  guardProps: RouteGuardHocProps
) => {
  // Return a new component that wraps the original with RouteGuard
  const WithRouteGuard: React.FC<P> = (props) => (
    <RouteGuard {...guardProps}>
      <Component {...props} />
    </RouteGuard>
  );

  // Set display name for debugging
  const componentName = Component.displayName || Component.name || 'Component';
  WithRouteGuard.displayName = `withRouteGuard(${componentName})`;

  return WithRouteGuard;
};

export default withRouteGuard;
