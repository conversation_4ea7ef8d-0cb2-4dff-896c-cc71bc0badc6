
import React, { ReactNode } from 'react';

interface LandingBackgroundProps {
  children: ReactNode;
  backgroundImageUrl: string;
}

export const LandingBackground: React.FC<LandingBackgroundProps> = ({ children, backgroundImageUrl }) => {
  return (
    <div className="h-screen w-full flex flex-col items-center justify-center relative overflow-hidden">
      {/* Full-screen background image */}
      <div
        className="absolute inset-0 z-0 bg-gradient-to-br from-blue-900 via-purple-900 to-indigo-900"
        style={{
          backgroundImage: `url('${backgroundImageUrl}')`,
          backgroundSize: 'cover',
          backgroundPosition: 'center',
          backgroundRepeat: 'no-repeat',
          backgroundAttachment: 'fixed'
        }}
      />
      
      {/* Gradient overlay for better visual appeal */}
      <div className="absolute inset-0 z-0 bg-gradient-to-br from-black/40 via-transparent to-black/60" />
      
      {/* Subtle animated background pattern */}
      <div className="absolute inset-0 z-0 opacity-10">
        <div className="absolute top-0 left-0 w-full h-full bg-gradient-to-r from-transparent via-white/5 to-transparent animate-pulse" />
      </div>

      {/* Content positioned on top of background */}
      <div className="relative z-10 flex flex-col items-center justify-center min-h-screen px-4">
        {children}
      </div>
      
    </div>
  );
};
