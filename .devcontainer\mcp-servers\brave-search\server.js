#!/usr/bin/env node

const { Server } = require('@modelcontextprotocol/sdk/server/index.js');
const { StdioServerTransport } = require('@modelcontextprotocol/sdk/server/stdio.js');
const { CallToolRequestSchema, ListToolsRequestSchema } = require('@modelcontextprotocol/sdk/types.js');
const axios = require('axios');
const express = require('express');

class BraveSearchServer {
  constructor() {
    this.server = new Server(
      {
        name: 'brave-search-server',
        version: '1.0.0',
      },
      {
        capabilities: {
          tools: {},
        },
      }
    );

    this.apiKey = process.env.BRAVE_API_KEY;
    this.port = process.env.MCP_PORT || 8080;
    
    this.setupToolHandlers();
    this.setupHealthCheck();
  }

  setupToolHandlers() {
    this.server.setRequestHandler(ListToolsRequestSchema, async () => {
      return {
        tools: [
          {
            name: 'brave_search',
            description: 'Search the web using Brave Search API',
            inputSchema: {
              type: 'object',
              properties: {
                query: {
                  type: 'string',
                  description: 'Search query',
                },
                count: {
                  type: 'number',
                  description: 'Number of results to return (default: 10)',
                  default: 10,
                },
                offset: {
                  type: 'number',
                  description: 'Offset for pagination (default: 0)',
                  default: 0,
                },
                country: {
                  type: 'string',
                  description: 'Country code for localized results (default: US)',
                  default: 'US',
                },
                search_lang: {
                  type: 'string',
                  description: 'Language for search results (default: en)',
                  default: 'en',
                },
                ui_lang: {
                  type: 'string',
                  description: 'UI language (default: en-US)',
                  default: 'en-US',
                },
                safesearch: {
                  type: 'string',
                  description: 'Safe search setting (off, moderate, strict)',
                  enum: ['off', 'moderate', 'strict'],
                  default: 'moderate',
                },
                freshness: {
                  type: 'string',
                  description: 'Freshness of results (pd, pw, pm, py)',
                  enum: ['pd', 'pw', 'pm', 'py'],
                },
                text_decorations: {
                  type: 'boolean',
                  description: 'Include text decorations in results',
                  default: false,
                },
                spellcheck: {
                  type: 'boolean',
                  description: 'Enable spellcheck',
                  default: true,
                },
              },
              required: ['query'],
            },
          },
        ],
      };
    });

    this.server.setRequestHandler(CallToolRequestSchema, async (request) => {
      const { name, arguments: args } = request.params;

      if (name === 'brave_search') {
        return await this.handleBraveSearch(args);
      }

      throw new Error(`Unknown tool: ${name}`);
    });
  }

  async handleBraveSearch(args) {
    if (!this.apiKey) {
      throw new Error('BRAVE_API_KEY environment variable is required');
    }

    try {
      const params = {
        q: args.query,
        count: args.count || 10,
        offset: args.offset || 0,
        country: args.country || 'US',
        search_lang: args.search_lang || 'en',
        ui_lang: args.ui_lang || 'en-US',
        safesearch: args.safesearch || 'moderate',
        text_decorations: args.text_decorations || false,
        spellcheck: args.spellcheck !== false,
      };

      if (args.freshness) {
        params.freshness = args.freshness;
      }

      const response = await axios.get('https://api.search.brave.com/res/v1/web/search', {
        headers: {
          'Accept': 'application/json',
          'Accept-Encoding': 'gzip',
          'X-Subscription-Token': this.apiKey,
        },
        params,
      });

      const results = response.data;
      
      return {
        content: [
          {
            type: 'text',
            text: JSON.stringify({
              query: args.query,
              total_results: results.web?.total || 0,
              results: results.web?.results || [],
              news: results.news?.results || [],
              videos: results.videos?.results || [],
              images: results.images?.results || [],
              locations: results.locations?.results || [],
              mixed: results.mixed?.main || [],
              query_context: results.query || {},
            }, null, 2),
          },
        ],
      };
    } catch (error) {
      throw new Error(`Brave Search API error: ${error.message}`);
    }
  }

  setupHealthCheck() {
    const app = express();
    
    app.get('/health', (req, res) => {
      res.json({ 
        status: 'healthy', 
        service: 'brave-search-mcp',
        timestamp: new Date().toISOString(),
        api_key_configured: !!this.apiKey
      });
    });

    app.listen(this.port, () => {
      console.log(`Brave Search MCP Server health check running on port ${this.port}`);
    });
  }

  async run() {
    const transport = new StdioServerTransport();
    await this.server.connect(transport);
    console.error('Brave Search MCP server running on stdio');
  }
}

const server = new BraveSearchServer();
server.run().catch(console.error);
