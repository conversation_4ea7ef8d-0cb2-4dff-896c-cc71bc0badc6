# Development to Production Workflow

This document outlines the workflow for developing in the development container and deploying to production.

## Overview

The workflow consists of:
1. Local development using the development container
2. Building the application in a consistent environment
3. Committing and pushing changes to version control
4. Deploying to production

## Development Setup

### 1. Start the Development Container

```bash
docker-compose -f docker-compose.dev.yml up -d
```

This starts a development container with:
- Source code mounted as a volume for live editing
- Development server running (configured in docker-compose.dev.yml)
- Access to the production database (be careful!)

### 2. Make Changes

Edit your code on your host machine. Changes are immediately reflected in the container due to the volume mount.

### 3. View Changes

Access the development server at http://localhost:3001

### 4. Run Tests (if applicable)

```bash
docker-compose -f docker-compose.dev.yml exec nxt-platform-dev npm test
```

## Building for Production

### 1. Build Inside the Development Container

This ensures a consistent build environment:

```bash
docker-compose -f docker-compose.dev.yml exec nxt-platform-dev npm run build
```

### 2. Verify the Build

Check that the build completed successfully and all files are generated in the `dist` directory.

## Deployment to Production

### 1. Comm<PERSON> and Push Changes

```bash
git add .
git commit -m "Your commit message"
git push origin main
```

### 2. On the Production Server

Pull the latest changes:

```bash
git pull origin main
```

### 3. Build the Production Image

```bash
docker build -t ghcr.io/nxtleveltech1/dev-xxx:latest .
```

### 4. Push to Container Registry

```bash
docker push ghcr.io/nxtleveltech1/dev-xxx:latest
```

### 5. Update Production Container

```bash
docker-compose pull
docker-compose up -d
```

## Best Practices

1. **Never use the development container in production** - It includes development dependencies and mounts source code as volumes.

2. **Test builds locally** - Always build in the development container before pushing to ensure the build will succeed in production.

3. **Use environment variables** - Keep sensitive configuration in environment variables, not in code.

4. **Database migrations** - Run any necessary database migrations before deploying new code.

5. **Health checks** - Monitor the health endpoint after deployment to ensure the service is running correctly.

## Rollback Procedure

If something goes wrong:

1. Stop the current container:
   ```bash
   docker-compose down
   ```

2. Pull the previous working image:
   ```bash
   docker pull ghcr.io/nxtleveltech1/dev-xxx:previous-tag
   ```

3. Update docker-compose.yml to use the previous image tag

4. Start the container:
   ```bash
   docker-compose up -d
   ```

## Environment Differences

### Development Container (docker-compose.dev.yml)
- Mounts source code as volume
- Runs development server
- Includes all dependencies (including devDependencies)
- Port 3001

### Production Container (docker-compose.yml)
- Uses built code from Docker image
- Runs production server
- Only production dependencies
- Port 3000
- Health checks enabled
- Runs as non-root user for security