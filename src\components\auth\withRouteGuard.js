import { jsx as _jsx } from "react/jsx-runtime";
import RouteGuard from './RouteGuard'; // Default import
/**
 * Higher-order component (HOC) for route protection
 * Wraps a component with RouteGuard to enforce authentication and permission checks
 *
 * @param Component The component to wrap
 * @param guardProps RouteGuard configuration options
 */
export const withRouteGuard = (Component, guardProps) => {
    // Return a new component that wraps the original with RouteGuard
    const WithRouteGuard = (props) => (_jsx(RouteGuard, { ...guardProps, children: _jsx(Component, { ...props }) }));
    // Set display name for debugging
    const componentName = Component.displayName || Component.name || 'Component';
    WithRouteGuard.displayName = `withRouteGuard(${componentName})`;
    return WithRouteGuard;
};
export default withRouteGuard;
