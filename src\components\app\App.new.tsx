import { Suspense } from "react";
import { BrowserRouter, Routes, Route, Navigate } from "react-router-dom";
import { AuthProvider } from "@/auth"; // New consolidated auth
import { ThemeProvider } from "@/context/ThemeContext";
import { ModulesProvider } from "@/context/ModulesContext";
import { Toaster } from "sonner";
import { DashboardLayout } from "@/components/layout/DashboardLayout";
import MasterDash from "@/pages/MasterDash.tsx";
// If the file is named differently or in a different location, update the path accordingly, e.g.:
// import MasterDash from "../pages/MasterDash";
// or
// import MasterDash from "@/pages/MasterDash/index";
import { supabase } from "@/integrations/supabase";

export default function App() {
  return (
    <AuthProvider supabaseClient={supabase}>
      <ThemeProvider>
        <ModulesProvider>
          <BrowserRouter>
            <Suspense fallback={<p className="p-4" aria-label="Loading application">Loading…</p>}>
              <Toaster 
                position="top-right" 
                richColors 
                closeButton
                toastOptions={{
                  className: "toast"
                }}
              />
              <Routes>
                <Route path="/" element={<Navigate to="/master" />} />
                <Route path="/master" element={
                  <DashboardLayout>
                    <MasterDash />
                  </DashboardLayout>
                } />
                {/* Add the missing landing route */}
                <Route path="/landing" element={<Navigate to="/master" />} />
              </Routes>
            </Suspense>
          </BrowserRouter>
        </ModulesProvider>
      </ThemeProvider>
    </AuthProvider>
  );
}
