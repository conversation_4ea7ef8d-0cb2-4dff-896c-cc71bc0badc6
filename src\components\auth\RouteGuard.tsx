import React from 'react';
import { Navigate, useLocation } from 'react-router-dom';
import { useBetterAuth} from '@/providers/BetterAuthProvider';

interface RouteGuardProps {
  children: React.ReactNode;
  requiredPermissions?: Permission[];
  requiredRole?: UserRole;
  requireAnyPermission?: boolean; // If true, user needs ANY of the permissions, if false, user needs ALL
  fallbackPath?: string;
  showUnauthorized?: boolean;
}

const RouteGuard: React.FC<RouteGuardProps> = ({
  children,
  requiredPermissions = [],
  requiredRole,
  requireAnyPermission = false,
  fallbackPath = '/landing',
  showUnauthorized = false
}) => {
  const { user, hasPermission, hasAnyPermission, hasAllPermissions, isRoleOrHigher, loading } = useAuth();
  const location = useLocation();

  console.log('[RouteGuard] Current state:', {
    pathname: location.pathname,
    hasUser: !!user,
    loading,
    userEmail: user?.email,
    fallbackPath
  });

  // Show loading state while auth is being determined
  if (loading) {
    console.log('[RouteGuard] Still loading auth...');
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  // Production security checks:
  
  // Check if user is authenticated
  if (!user) {
    console.log('[RouteGuard] No user found, redirecting to:', fallbackPath);
    return <Navigate to={fallbackPath} state={{ from: location }} replace />;
  }

  // Check role requirement
  if (requiredRole && !isRoleOrHigher(requiredRole)) {
    if (showUnauthorized) {
      return <UnauthorizedComponent requiredRole={requiredRole} />;
    }
    return <Navigate to="/unauthorized" replace />;
  }

  // Check permission requirements
  if (requiredPermissions.length > 0) {
    const hasRequiredPermissions = requireAnyPermission
      ? hasAnyPermission(requiredPermissions)
      : hasAllPermissions(requiredPermissions);

    if (!hasRequiredPermissions) {
      if (showUnauthorized) {
        return <UnauthorizedComponent requiredPermissions={requiredPermissions} />;
      }
      return <Navigate to="/unauthorized" replace />;
    }
  }

  // All checks passed, render children
  return <>{children}</>;
};

// Unauthorized component to show when access is denied
const UnauthorizedComponent: React.FC<{
  requiredPermissions?: Permission[];
  requiredRole?: UserRole;
}> = ({ requiredPermissions, requiredRole }) => {
  const { user } = useAuth();

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50">
      <div className="max-w-md w-full bg-white shadow-lg rounded-lg p-6">
        <div className="text-center">
          <div className="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-red-100">
            <svg
              className="h-6 w-6 text-red-600"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"
              />
            </svg>
          </div>
          <h3 className="mt-2 text-sm font-medium text-gray-900">Access Denied</h3>
          <p className="mt-1 text-sm text-gray-500">
            You don't have permission to access this resource.
          </p>
          
          {requiredRole && (
            <div className="mt-4 p-3 bg-gray-50 rounded-md">
              <p className="text-xs text-gray-600">
                <strong>Required Role:</strong> {requiredRole}
              </p>
              <p className="text-xs text-gray-600">
                <strong>Your Role:</strong> {user?.role || 'None'}
              </p>
            </div>
          )}
          
          {requiredPermissions && requiredPermissions.length > 0 && (
            <div className="mt-4 p-3 bg-gray-50 rounded-md">
              <p className="text-xs text-gray-600 mb-2">
                <strong>Required Permissions:</strong>
              </p>
              <ul className="text-xs text-gray-500 space-y-1">
                {requiredPermissions.map(permission => (
                  <li key={permission} className="flex items-center">
                    <span className="w-2 h-2 bg-red-400 rounded-full mr-2"></span>
                    {permission}
                  </li>
                ))}
              </ul>
            </div>
          )}
          
          <div className="mt-6">
            <button
              onClick={() => globalThis.history.back()}
              className="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-blue-600 text-base font-medium text-white hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 sm:text-sm"
            >
              Go Back
            </button>
          </div>
          
          <div className="mt-3">
            <p className="text-xs text-gray-500">
              Contact your administrator if you believe this is an error.
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};

// withRouteGuard HOC has been moved to withRouteGuard.tsx
// usePermissionCheck hook has been moved to usePermissionCheck.ts

export default RouteGuard;


