import React from 'react';
import { useLocation } from 'react-router-dom';
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>Lef<PERSON>, Settings } from 'lucide-react';

interface PlaceholderPageProps {
  title?: string;
  description?: string;
  icon?: React.ComponentType<any>;
}

const PlaceholderPage: React.FC<PlaceholderPageProps> = ({ 
  title, 
  description, 
  icon: Icon = Settings 
}) => {
  const location = useLocation();
  
  // Extract module name from path if title not provided
  const moduleName = title || location.pathname
    .split('/')
    .filter(Boolean)
    .map(segment => 
      segment.split('-')
        .map(word => word.charAt(0).toUpperCase() + word.slice(1))
        .join(' ')
    )
    .join(' > ');

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      <div className="max-w-4xl mx-auto px-4 py-8">
        {/* Header */}
        <div className="mb-8">
          <button
            onClick={() => globalThis.history.back()}
            className="flex items-center text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-gray-100 mb-4"
          >
            <ArrowLeft className="w-4 h-4 mr-2" />
            Back
          </button>
          
          <div className="flex items-center mb-4">
            <Icon className="w-8 h-8 text-blue-600 dark:text-blue-400 mr-3" />
            <h1 className="text-3xl font-bold text-gray-900 dark:text-gray-100">
              {moduleName}
            </h1>
          </div>
          
          {description && (
            <p className="text-gray-600 dark:text-gray-400 text-lg">
              {description}
            </p>
          )}
        </div>

        {/* Main Content */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-8">
          <div className="text-center">
            <div className="mx-auto flex items-center justify-center h-16 w-16 rounded-full bg-yellow-100 dark:bg-yellow-900/20 mb-6">
              <AlertTriangle className="h-8 w-8 text-yellow-600 dark:text-yellow-400" />
            </div>
            
            <h2 className="text-2xl font-semibold text-gray-900 dark:text-gray-100 mb-4">
              Module Under Development
            </h2>
            
            <p className="text-gray-600 dark:text-gray-400 mb-6 max-w-md mx-auto">
              This module is currently being developed and will be available in a future release. 
              Please check back later for updates.
            </p>
            
            <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4 mb-6">
              <h3 className="text-sm font-medium text-blue-900 dark:text-blue-100 mb-2">
                Current Path
              </h3>
              <code className="text-sm text-blue-700 dark:text-blue-300 bg-blue-100 dark:bg-blue-900/40 px-2 py-1 rounded">
                {location.pathname}
              </code>
            </div>
            
            <div className="flex justify-center space-x-4">
              <button
                onClick={() => globalThis.location.href = '/'}
                className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-lg transition-colors"
              >
                Return to Dashboard
              </button>
              
              <button
                onClick={() => globalThis.history.back()}
                className="bg-gray-200 dark:bg-gray-700 hover:bg-gray-300 dark:hover:bg-gray-600 text-gray-900 dark:text-gray-100 px-6 py-2 rounded-lg transition-colors"
              >
                Go Back
              </button>
            </div>
          </div>
        </div>

        {/* Additional Info */}
        <div className="mt-8 grid grid-cols-1 md:grid-cols-3 gap-6">
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-2">
              Development Status
            </h3>
            <p className="text-gray-600 dark:text-gray-400 text-sm">
              This module is in the planning phase and will be implemented based on user requirements and priority.
            </p>
          </div>
          
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-2">
              Expected Features
            </h3>
            <p className="text-gray-600 dark:text-gray-400 text-sm">
              Full CRUD operations, dashboard analytics, reporting capabilities, and integration with other platform modules.
            </p>
          </div>
          
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-2">
              Need This Module?
            </h3>
            <p className="text-gray-600 dark:text-gray-400 text-sm">
              Contact the development team to prioritize this module or request specific features for your use case.
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default PlaceholderPage;
