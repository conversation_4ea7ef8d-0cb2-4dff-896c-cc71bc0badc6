import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";
import { Checkbox } from "@/components/ui/checkbox";
import { Edit, Trash2, Search, Upload, ArrowLeft, Save, UserPlus } from "lucide-react";
const ContactManager = ({ onBack, onCreateSegment }) => {
    const [activeTab, setActiveTab] = useState('contacts');
    const [contacts, setContacts] = useState([]);
    const [segments, setSegments] = useState([]);
    const [searchTerm, setSearchTerm] = useState('');
    const [selectedContacts, setSelectedContacts] = useState([]);
    const [filterSegment, setFilterSegment] = useState('all');
    const [isCreating, setIsCreating] = useState(false);
    const [editingContact, setEditingContact] = useState(null);
    const [loading, setLoading] = useState(false);
    // Form state for creating/editing contacts
    const [contactForm, setContactForm] = useState({
        name: '',
        email: '',
        phone: '',
        tags: [],
        segments: [],
        preferences: {
            email: true,
            sms: true,
            marketing: true,
            notifications: true
        }
    });
    useEffect(() => {
        loadContactsAndSegments();
    }, []);
    const loadContactsAndSegments = async () => {
        try {
            setLoading(true);
            // Mock data - replace with actual API calls
            const mockContacts = [
                {
                    id: 'contact_1',
                    name: 'John Doe',
                    email: '<EMAIL>',
                    phone: '******-0123',
                    customerId: 'customer_1',
                    tags: ['vip', 'enterprise'],
                    segments: ['vip-customers', 'active-customers'],
                    preferences: {
                        email: true,
                        sms: true,
                        marketing: true,
                        notifications: true
                    },
                    createdAt: '2025-05-20T10:00:00Z',
                    updatedAt: '2025-05-25T14:30:00Z'
                },
                {
                    id: 'contact_2',
                    name: 'Sarah Johnson',
                    email: '<EMAIL>',
                    phone: '******-0124',
                    customerId: 'customer_2',
                    tags: ['new-customer'],
                    segments: ['new-customers'],
                    preferences: {
                        email: true,
                        sms: false,
                        marketing: true,
                        notifications: true
                    },
                    createdAt: '2025-05-25T09:15:00Z',
                    updatedAt: '2025-05-25T09:15:00Z'
                },
                {
                    id: 'contact_3',
                    name: 'Michael Chen',
                    email: '<EMAIL>',
                    phone: '******-0125',
                    customerId: 'customer_3',
                    tags: ['premium', 'loyal'],
                    segments: ['vip-customers', 'active-customers'],
                    preferences: {
                        email: true,
                        sms: true,
                        marketing: false,
                        notifications: true
                    },
                    createdAt: '2025-04-15T16:20:00Z',
                    updatedAt: '2025-05-24T11:45:00Z'
                }
            ];
            const mockSegments = [
                {
                    id: 'new-customers',
                    name: 'New Customers',
                    description: 'Customers who joined in the last 30 days',
                    criteria: [
                        { field: 'createdAt', operator: 'greater_than', value: '2025-04-26' }
                    ],
                    contactCount: 245,
                    createdAt: '2025-05-01T10:00:00Z',
                    updatedAt: '2025-05-01T10:00:00Z'
                },
                {
                    id: 'vip-customers',
                    name: 'VIP Customers',
                    description: 'High-value customers with premium status',
                    criteria: [
                        { field: 'tags', operator: 'in', value: ['vip', 'premium', 'enterprise'] }
                    ],
                    contactCount: 156,
                    createdAt: '2025-05-01T10:00:00Z',
                    updatedAt: '2025-05-01T10:00:00Z'
                }
            ];
            setContacts(mockContacts);
            setSegments(mockSegments);
        }
        catch (error) {
            console.error('Failed to load contacts and segments:', error);
        }
        finally {
            setLoading(false);
        }
    };
    const filteredContacts = () => {
        let filtered = contacts;
        if (searchTerm) {
            filtered = filtered.filter(contact => contact.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                contact.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
                contact.tags.some(tag => tag.toLowerCase().includes(searchTerm.toLowerCase())));
        }
        if (filterSegment !== 'all') {
            filtered = filtered.filter(contact => contact.segments.includes(filterSegment));
        }
        return filtered;
    };
    const handleCreateContact = () => {
        setContactForm({
            name: '',
            email: '',
            phone: '',
            tags: [],
            segments: [],
            preferences: {
                email: true,
                sms: true,
                marketing: true,
                notifications: true
            }
        });
        setEditingContact(null);
        setIsCreating(true);
    };
    const handleSaveContact = async () => {
        if (!contactForm.name.trim() || !contactForm.email.trim()) {
            alert('Please fill in required fields (name and email)');
            return;
        }
        try {
            setLoading(true);
            const contactData = {
                id: editingContact?.id || `contact_${Date.now()}`,
                name: contactForm.name,
                email: contactForm.email,
                phone: contactForm.phone || undefined,
                tags: contactForm.tags,
                segments: contactForm.segments,
                preferences: contactForm.preferences,
                createdAt: editingContact?.createdAt || new Date().toISOString(),
                updatedAt: new Date().toISOString()
            };
            if (editingContact) {
                setContacts(prev => prev.map(c => c.id === contactData.id ? contactData : c));
            }
            else {
                setContacts(prev => [...prev, contactData]);
            }
            setIsCreating(false);
            setEditingContact(null);
        }
        catch (error) {
            console.error('Failed to save contact:', error);
            alert('Failed to save contact. Please try again.');
        }
        finally {
            setLoading(false);
        }
    };
    if (isCreating) {
        return (_jsxs("div", { className: "space-y-6", children: [_jsxs("div", { className: "flex items-center justify-between", children: [_jsxs("div", { className: "flex items-center gap-4", children: [_jsxs(Button, { variant: "ghost", onClick: () => setIsCreating(false), children: [_jsx(ArrowLeft, { className: "h-4 w-4 mr-2" }), "Back to Contacts"] }), _jsx("div", { children: _jsxs("h1", { className: "text-3xl font-bold tracking-tight", children: [editingContact ? 'Edit' : 'Create', " Contact"] }) })] }), _jsxs(Button, { onClick: handleSaveContact, disabled: loading, children: [_jsx(Save, { className: "h-4 w-4 mr-2" }), "Save Contact"] })] }), _jsxs(Card, { children: [_jsxs(CardHeader, { children: [_jsx(CardTitle, { children: "Contact Information" }), _jsx(CardDescription, { children: "Basic contact details and preferences" })] }), _jsxs(CardContent, { className: "space-y-4", children: [_jsxs("div", { className: "grid gap-4 md:grid-cols-2", children: [_jsxs("div", { children: [_jsx(Label, { htmlFor: "contact-name", children: "Name *" }), _jsx(Input, { id: "contact-name", value: contactForm.name, onChange: (e) => setContactForm(prev => ({ ...prev, name: e.target.value })), placeholder: "Enter contact name" })] }), _jsxs("div", { children: [_jsx(Label, { htmlFor: "contact-email", children: "Email *" }), _jsx(Input, { id: "contact-email", type: "email", value: contactForm.email, onChange: (e) => setContactForm(prev => ({ ...prev, email: e.target.value })), placeholder: "Enter email address" })] })] }), _jsxs("div", { children: [_jsx(Label, { htmlFor: "contact-phone", children: "Phone" }), _jsx(Input, { id: "contact-phone", value: contactForm.phone, onChange: (e) => setContactForm(prev => ({ ...prev, phone: e.target.value })), placeholder: "Enter phone number" })] })] })] })] }));
    }
    return (_jsxs("div", { className: "space-y-6", children: [_jsxs("div", { className: "flex items-center justify-between", children: [_jsxs("div", { className: "flex items-center gap-4", children: [onBack && (_jsxs(Button, { variant: "ghost", onClick: onBack, children: [_jsx(ArrowLeft, { className: "h-4 w-4 mr-2" }), "Back"] })), _jsxs("div", { children: [_jsx("h1", { className: "text-3xl font-bold tracking-tight", children: "Contact Management" }), _jsx("p", { className: "text-gray-600", children: "Manage contacts and customer segments" })] })] }), _jsxs("div", { className: "flex gap-2", children: [_jsxs(Button, { variant: "outline", children: [_jsx(Upload, { className: "h-4 w-4 mr-2" }), "Import"] }), _jsxs(Button, { onClick: handleCreateContact, children: [_jsx(UserPlus, { className: "h-4 w-4 mr-2" }), "Add Contact"] })] })] }), _jsxs(Tabs, { value: activeTab, onValueChange: (value) => setActiveTab(value), children: [_jsxs(TabsList, { children: [_jsx(TabsTrigger, { value: "contacts", children: "Contacts" }), _jsx(TabsTrigger, { value: "segments", children: "Segments" })] }), _jsxs(TabsContent, { value: "contacts", className: "space-y-4", children: [_jsxs("div", { className: "flex items-center gap-4", children: [_jsx("div", { className: "flex-1 max-w-md", children: _jsxs("div", { className: "relative", children: [_jsx(Search, { className: "absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" }), _jsx(Input, { placeholder: "Search contacts...", value: searchTerm, onChange: (e) => setSearchTerm(e.target.value), className: "pl-10" })] }) }), _jsxs(Select, { value: filterSegment, onValueChange: setFilterSegment, children: [_jsx(SelectTrigger, { className: "w-48", children: _jsx(SelectValue, { placeholder: "Filter by segment" }) }), _jsxs(SelectContent, { children: [_jsx(SelectItem, { value: "all", children: "All Segments" }), segments.map((segment) => (_jsx(SelectItem, { value: segment.id, children: segment.name }, segment.id)))] })] })] }), _jsx(Card, { children: _jsx(CardContent, { className: "p-0", children: _jsx("div", { className: "overflow-x-auto", children: _jsxs("table", { className: "w-full", children: [_jsx("thead", { className: "border-b", children: _jsxs("tr", { children: [_jsx("th", { className: "text-left p-4", children: _jsx(Checkbox, { checked: selectedContacts.length === filteredContacts().length && filteredContacts().length > 0, onCheckedChange: () => {
                                                                        const filtered = filteredContacts();
                                                                        if (selectedContacts.length === filtered.length) {
                                                                            setSelectedContacts([]);
                                                                        }
                                                                        else {
                                                                            setSelectedContacts(filtered.map(c => c.id));
                                                                        }
                                                                    } }) }), _jsx("th", { className: "text-left p-4", children: "Name" }), _jsx("th", { className: "text-left p-4", children: "Email" }), _jsx("th", { className: "text-left p-4", children: "Phone" }), _jsx("th", { className: "text-left p-4", children: "Tags" }), _jsx("th", { className: "text-left p-4", children: "Segments" }), _jsx("th", { className: "text-left p-4", children: "Actions" })] }) }), _jsx("tbody", { children: filteredContacts().map((contact) => (_jsxs("tr", { className: "border-b hover:bg-gray-50", children: [_jsx("td", { className: "p-4", children: _jsx(Checkbox, { checked: selectedContacts.includes(contact.id), onCheckedChange: () => {
                                                                        setSelectedContacts(prev => prev.includes(contact.id)
                                                                            ? prev.filter(id => id !== contact.id)
                                                                            : [...prev, contact.id]);
                                                                    } }) }), _jsx("td", { className: "p-4 font-medium", children: contact.name }), _jsx("td", { className: "p-4", children: contact.email }), _jsx("td", { className: "p-4", children: contact.phone || '-' }), _jsx("td", { className: "p-4", children: _jsxs("div", { className: "flex flex-wrap gap-1", children: [contact.tags.slice(0, 2).map((tag) => (_jsx(Badge, { variant: "secondary", className: "text-xs", children: tag }, tag))), contact.tags.length > 2 && (_jsxs(Badge, { variant: "outline", className: "text-xs", children: ["+", contact.tags.length - 2] }))] }) }), _jsx("td", { className: "p-4", children: _jsxs("div", { className: "flex flex-wrap gap-1", children: [contact.segments.slice(0, 2).map((segmentId) => {
                                                                            const segment = segments.find(s => s.id === segmentId);
                                                                            return segment ? (_jsx(Badge, { variant: "outline", className: "text-xs", children: segment.name }, segmentId)) : null;
                                                                        }), contact.segments.length > 2 && (_jsxs(Badge, { variant: "outline", className: "text-xs", children: ["+", contact.segments.length - 2] }))] }) }), _jsx("td", { className: "p-4", children: _jsxs("div", { className: "flex gap-1", children: [_jsx(Button, { variant: "ghost", size: "sm", onClick: () => {
                                                                                setContactForm({
                                                                                    name: contact.name,
                                                                                    email: contact.email,
                                                                                    phone: contact.phone || '',
                                                                                    tags: contact.tags,
                                                                                    segments: contact.segments,
                                                                                    preferences: contact.preferences
                                                                                });
                                                                                setEditingContact(contact);
                                                                                setIsCreating(true);
                                                                            }, children: _jsx(Edit, { className: "h-4 w-4" }) }), _jsx(Button, { variant: "ghost", size: "sm", onClick: () => {
                                                                                const confirmed = globalThis.confirm('Are you sure you want to delete this contact?');
                                                                                if (confirmed) {
                                                                                    setContacts(prev => prev.filter(c => c.id !== contact.id));
                                                                                }
                                                                            }, children: _jsx(Trash2, { className: "h-4 w-4" }) })] }) })] }, contact.id))) })] }) }) }) })] }), _jsx(TabsContent, { value: "segments", className: "space-y-4", children: _jsx("div", { className: "grid gap-4 md:grid-cols-2 lg:grid-cols-3", children: segments.map((segment) => (_jsxs(Card, { children: [_jsxs(CardHeader, { children: [_jsx(CardTitle, { className: "text-lg", children: segment.name }), _jsx(CardDescription, { children: segment.description })] }), _jsx(CardContent, { children: _jsxs("div", { className: "space-y-3", children: [_jsxs("div", { className: "flex items-center justify-between", children: [_jsx("span", { className: "text-sm text-gray-600", children: "Contacts:" }), _jsx(Badge, { variant: "secondary", children: segment.contactCount.toLocaleString() })] }), _jsxs("div", { className: "flex gap-2", children: [_jsxs(Button, { variant: "outline", size: "sm", className: "flex-1", children: [_jsx(Edit, { className: "h-4 w-4 mr-2" }), "Edit"] }), _jsx(Button, { variant: "outline", size: "sm", children: _jsx(Trash2, { className: "h-4 w-4" }) })] })] }) })] }, segment.id))) }) })] })] }));
};
export default ContactManager;
