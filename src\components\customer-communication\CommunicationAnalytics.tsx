import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Ta<PERSON>, Ta<PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { 
  BarChart3, 
  TrendingUp, 
  Mail, 
  MessageSquare, 
  Users, 
  Eye,
  MousePointer,
  AlertTriangle,
  Download,
  Calendar,
  ArrowLeft
} from "lucide-react";
import { 
  CustomerCommunicationService, 
  CommunicationMetrics,
  Campaign,
  CommunicationLog
} from "@/services/customer-communication";

interface CommunicationAnalyticsProps {
  onBack?: () => void;
}

const CommunicationAnalytics: React.FC<CommunicationAnalyticsProps> = ({
  onBack
}) => {
  const [metrics, setMetrics] = useState<CommunicationMetrics | null>(null);
  const [campaigns, setCampaigns] = useState<Campaign[]>([]);
  const [dateRange, setDateRange] = useState('30d');
  const [loading, setLoading] = useState(false);
  const [reportData, setReportData] = useState<any>(null);

  useEffect(() => {
    loadAnalyticsData();
  }, [dateRange]);

  const loadAnalyticsData = async () => {
    try {
      setLoading(true);
      
      // Mock data - replace with actual API calls
      const mockMetrics: CommunicationMetrics = {
        totalCampaigns: 24,
        activeCampaigns: 3,
        totalContacts: 2847,
        emailMetrics: {
          sent: 15420,
          delivered: 14892,
          opened: 7446,
          clicked: 1489,
          bounced: 528,
          deliveryRate: 96.6,
          openRate: 50.0,
          clickRate: 20.0,
          bounceRate: 3.4
        },
        smsMetrics: {
          sent: 8340,
          delivered: 8173,
          clicked: 817,
          failed: 167,
          deliveryRate: 98.0,
          clickRate: 10.0
        }
      };

      const mockCampaigns: Campaign[] = [
        {
          id: '1',
          name: 'Welcome Series - New Customers',
          type: 'email',
          status: 'sent',
          templateId: 'welcome-template',
          targetSegments: ['new-customers'],
          contactIds: [],
          metrics: {
            sent: 245,
            delivered: 238,
            opened: 119,
            clicked: 24,
            bounced: 7,
            unsubscribed: 2
          },
          createdAt: '2025-05-25T10:00:00Z',
          updatedAt: '2025-05-26T08:30:00Z'
        },
        {
          id: '2',
          name: 'Product Update Announcement',
          type: 'email',
          status: 'sent',
          templateId: 'product-update',
          targetSegments: ['active-customers'],
          contactIds: [],
          metrics: {
            sent: 1823,
            delivered: 1756,
            opened: 878,
            clicked: 175,
            bounced: 67,
            unsubscribed: 12
          },
          createdAt: '2025-05-24T14:00:00Z',
          updatedAt: '2025-05-24T16:45:00Z'
        },
        {
          id: '3',
          name: 'Flash Sale Alert',
          type: 'sms',
          status: 'sent',
          templateId: 'flash-sale-sms',
          targetSegments: ['vip-customers'],
          contactIds: [],
          metrics: {
            sent: 156,
            delivered: 153,
            opened: 0,
            clicked: 15,
            bounced: 3,
            unsubscribed: 1
          },
          createdAt: '2025-05-26T09:00:00Z',
          updatedAt: '2025-05-26T09:30:00Z'
        }
      ];

      // Generate mock report data
      const mockReportData = CustomerCommunicationService.generateCommunicationReport(
        mockCampaigns,
        [], // Mock logs would go here
        {
          start: getDateRangeStart(dateRange),
          end: new Date().toISOString()
        }
      );

      setMetrics(mockMetrics);
      setCampaigns(mockCampaigns);
      setReportData(mockReportData);
    } catch (error) {
      console.error('Failed to load analytics data:', error);
    } finally {
      setLoading(false);
    }
  };

  const getDateRangeStart = (range: string): string => {
    const now = new Date();
    switch (range) {
      case '7d':
        return new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000).toISOString();
      case '30d':
        return new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000).toISOString();
      case '90d':
        return new Date(now.getTime() - 90 * 24 * 60 * 60 * 1000).toISOString();
      default:
        return new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000).toISOString();
    }
  };

  const formatPercentage = (value: number) => `${value.toFixed(1)}%`;
  const formatNumber = (value: number) => value.toLocaleString();

  const getPerformanceColor = (rate: number, type: 'good' | 'bad') => {
    if (type === 'good') {
      return rate >= 40 ? 'text-green-600' : rate >= 20 ? 'text-yellow-600' : 'text-red-600';
    } else {
      return rate <= 5 ? 'text-green-600' : rate <= 10 ? 'text-yellow-600' : 'text-red-600';
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-2 text-gray-600">Loading analytics...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          {onBack && (
            <Button variant="ghost" onClick={onBack}>
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back
            </Button>
          )}
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Communication Analytics</h1>
            <p className="text-gray-600">Performance metrics and insights for your campaigns</p>
          </div>
        </div>
        <div className="flex gap-2">
          <Select value={dateRange} onValueChange={setDateRange}>
            <SelectTrigger className="w-32">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="7d">Last 7 days</SelectItem>
              <SelectItem value="30d">Last 30 days</SelectItem>
              <SelectItem value="90d">Last 90 days</SelectItem>
            </SelectContent>
          </Select>
          <Button variant="outline">
            <Download className="h-4 w-4 mr-2" />
            Export
          </Button>
        </div>
      </div>

      {/* Overview Metrics */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Campaigns</CardTitle>
            <BarChart3 className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{metrics?.totalCampaigns || 0}</div>
            <p className="text-xs text-muted-foreground">
              {metrics?.activeCampaigns || 0} currently active
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Messages Sent</CardTitle>
            <Mail className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {formatNumber((metrics?.emailMetrics.sent || 0) + (metrics?.smsMetrics.sent || 0))}
            </div>
            <p className="text-xs text-muted-foreground">
              {formatNumber(metrics?.emailMetrics.sent || 0)} emails, {formatNumber(metrics?.smsMetrics.sent || 0)} SMS
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Avg. Open Rate</CardTitle>
            <Eye className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className={`text-2xl font-bold ${getPerformanceColor(metrics?.emailMetrics.openRate || 0, 'good')}`}>
              {formatPercentage(metrics?.emailMetrics.openRate || 0)}
            </div>
            <p className="text-xs text-muted-foreground">
              {formatNumber(metrics?.emailMetrics.opened || 0)} emails opened
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Avg. Click Rate</CardTitle>
            <MousePointer className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className={`text-2xl font-bold ${getPerformanceColor(metrics?.emailMetrics.clickRate || 0, 'good')}`}>
              {formatPercentage(metrics?.emailMetrics.clickRate || 0)}
            </div>
            <p className="text-xs text-muted-foreground">
              {formatNumber(metrics?.emailMetrics.clicked || 0)} clicks total
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Channel Performance */}
      <div className="grid gap-6 md:grid-cols-2">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Mail className="h-5 w-5" />
              Email Performance
            </CardTitle>
            <CardDescription>Email campaign metrics and trends</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div className="text-center p-3 bg-blue-50 rounded">
                  <div className="text-2xl font-bold text-blue-600">
                    {formatPercentage(metrics?.emailMetrics.deliveryRate || 0)}
                  </div>
                  <div className="text-sm text-gray-600">Delivery Rate</div>
                </div>
                <div className="text-center p-3 bg-green-50 rounded">
                  <div className="text-2xl font-bold text-green-600">
                    {formatPercentage(metrics?.emailMetrics.openRate || 0)}
                  </div>
                  <div className="text-sm text-gray-600">Open Rate</div>
                </div>
              </div>
              
              <div className="grid grid-cols-2 gap-4">
                <div className="text-center p-3 bg-purple-50 rounded">
                  <div className="text-2xl font-bold text-purple-600">
                    {formatPercentage(metrics?.emailMetrics.clickRate || 0)}
                  </div>
                  <div className="text-sm text-gray-600">Click Rate</div>
                </div>
                <div className="text-center p-3 bg-red-50 rounded">
                  <div className="text-2xl font-bold text-red-600">
                    {formatPercentage(metrics?.emailMetrics.bounceRate || 0)}
                  </div>
                  <div className="text-sm text-gray-600">Bounce Rate</div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <MessageSquare className="h-5 w-5" />
              SMS Performance
            </CardTitle>
            <CardDescription>SMS campaign metrics and trends</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div className="text-center p-3 bg-blue-50 rounded">
                  <div className="text-2xl font-bold text-blue-600">
                    {formatPercentage(metrics?.smsMetrics.deliveryRate || 0)}
                  </div>
                  <div className="text-sm text-gray-600">Delivery Rate</div>
                </div>
                <div className="text-center p-3 bg-green-50 rounded">
                  <div className="text-2xl font-bold text-green-600">
                    {formatPercentage(metrics?.smsMetrics.clickRate || 0)}
                  </div>
                  <div className="text-sm text-gray-600">Click Rate</div>
                </div>
              </div>
              
              <div className="grid grid-cols-2 gap-4">
                <div className="text-center p-3 bg-gray-50 rounded">
                  <div className="text-2xl font-bold text-gray-600">
                    {formatNumber(metrics?.smsMetrics.sent || 0)}
                  </div>
                  <div className="text-sm text-gray-600">Total Sent</div>
                </div>
                <div className="text-center p-3 bg-red-50 rounded">
                  <div className="text-2xl font-bold text-red-600">
                    {formatNumber(metrics?.smsMetrics.failed || 0)}
                  </div>
                  <div className="text-sm text-gray-600">Failed</div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Campaign Performance */}
      <Card>
        <CardHeader>
          <CardTitle>Campaign Performance</CardTitle>
          <CardDescription>Individual campaign metrics and results</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead className="border-b">
                <tr>
                  <th className="text-left p-3">Campaign</th>
                  <th className="text-left p-3">Type</th>
                  <th className="text-left p-3">Sent</th>
                  <th className="text-left p-3">Delivered</th>
                  <th className="text-left p-3">Opened</th>
                  <th className="text-left p-3">Clicked</th>
                  <th className="text-left p-3">Open Rate</th>
                  <th className="text-left p-3">Click Rate</th>
                </tr>
              </thead>
              <tbody>
                {campaigns.map((campaign) => {
                  const openRate = campaign.metrics.delivered > 0 
                    ? (campaign.metrics.opened / campaign.metrics.delivered) * 100 
                    : 0;
                  const clickRate = campaign.metrics.opened > 0 
                    ? (campaign.metrics.clicked / campaign.metrics.opened) * 100 
                    : 0;

                  return (
                    <tr key={campaign.id} className="border-b hover:bg-gray-50">
                      <td className="p-3">
                        <div>
                          <div className="font-medium">{campaign.name}</div>
                          <div className="text-sm text-gray-500">
                            {new Date(campaign.createdAt).toLocaleDateString()}
                          </div>
                        </div>
                      </td>
                      <td className="p-3">
                        <div className="flex items-center gap-2">
                          {campaign.type === 'email' ? (
                            <Mail className="h-4 w-4" />
                          ) : (
                            <MessageSquare className="h-4 w-4" />
                          )}
                          {campaign.type.toUpperCase()}
                        </div>
                      </td>
                      <td className="p-3">{formatNumber(campaign.metrics.sent)}</td>
                      <td className="p-3">{formatNumber(campaign.metrics.delivered)}</td>
                      <td className="p-3">{formatNumber(campaign.metrics.opened)}</td>
                      <td className="p-3">{formatNumber(campaign.metrics.clicked)}</td>
                      <td className="p-3">
                        <span className={getPerformanceColor(openRate, 'good')}>
                          {formatPercentage(openRate)}
                        </span>
                      </td>
                      <td className="p-3">
                        <span className={getPerformanceColor(clickRate, 'good')}>
                          {formatPercentage(clickRate)}
                        </span>
                      </td>
                    </tr>
                  );
                })}
              </tbody>
            </table>
          </div>
        </CardContent>
      </Card>

      {/* Insights and Recommendations */}
      <div className="grid gap-6 md:grid-cols-2">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <TrendingUp className="h-5 w-5" />
              Key Insights
            </CardTitle>
            <CardDescription>Performance insights from your campaigns</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              <div className="flex items-start gap-3">
                <div className="w-2 h-2 bg-green-500 rounded-full mt-2"></div>
                <div>
                  <p className="text-sm font-medium">Strong Email Performance</p>
                  <p className="text-xs text-gray-600">
                    Your email open rate of {formatPercentage(metrics?.emailMetrics.openRate || 0)} is above industry average
                  </p>
                </div>
              </div>
              <div className="flex items-start gap-3">
                <div className="w-2 h-2 bg-blue-500 rounded-full mt-2"></div>
                <div>
                  <p className="text-sm font-medium">High SMS Delivery</p>
                  <p className="text-xs text-gray-600">
                    SMS delivery rate of {formatPercentage(metrics?.smsMetrics.deliveryRate || 0)} shows excellent reach
                  </p>
                </div>
              </div>
              <div className="flex items-start gap-3">
                <div className="w-2 h-2 bg-yellow-500 rounded-full mt-2"></div>
                <div>
                  <p className="text-sm font-medium">Growing Audience</p>
                  <p className="text-xs text-gray-600">
                    Total contacts reached: {formatNumber(metrics?.totalContacts || 0)}
                  </p>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <AlertTriangle className="h-5 w-5" />
              Recommendations
            </CardTitle>
            <CardDescription>Suggestions to improve performance</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              <div className="flex items-start gap-3">
                <div className="w-2 h-2 bg-orange-500 rounded-full mt-2"></div>
                <div>
                  <p className="text-sm font-medium">Optimize Subject Lines</p>
                  <p className="text-xs text-gray-600">
                    A/B test different subject lines to improve open rates further
                  </p>
                </div>
              </div>
              <div className="flex items-start gap-3">
                <div className="w-2 h-2 bg-purple-500 rounded-full mt-2"></div>
                <div>
                  <p className="text-sm font-medium">Segment Targeting</p>
                  <p className="text-xs text-gray-600">
                    Create more specific segments for better personalization
                  </p>
                </div>
              </div>
              <div className="flex items-start gap-3">
                <div className="w-2 h-2 bg-red-500 rounded-full mt-2"></div>
                <div>
                  <p className="text-sm font-medium">Reduce Bounce Rate</p>
                  <p className="text-xs text-gray-600">
                    Clean your email list to improve deliverability
                  </p>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default CommunicationAnalytics;
