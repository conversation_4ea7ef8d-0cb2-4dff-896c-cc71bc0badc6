import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Badge } from '@/components/ui/badge';
import { Search } from 'lucide-react';
import { toast } from 'sonner';
import { useBetterAuth } from '@/providers/BetterAuthProvider';
import { Permission } from '@/utils/rbac/permissions'; // Import Permission enum
import AddUserDialog from './AddUserDialog';
import { useUserManagement } from '@/context/UserManagementContext';
const UsersTab = () => {
    const { hasPermission } = useBetterAuth();
    const { users, roles: rolesData, addUser } = useUserManagement();
    const [searchTerm, setSearchTerm] = useState('');
    const [dialogOpen, setDialogOpen] = useState(false);
    // Filter users based on search term
    const filteredUsers = users.filter(user => user.username.toLowerCase().includes(searchTerm.toLowerCase()) ||
        user.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
        (user.roles && user.roles.length > 0 ? user.roles.map(r => r.name).join(' ') : '').toLowerCase().includes(searchTerm.toLowerCase()));
    const handleAddUser = (newUser) => {
        addUser(newUser);
        toast.success(`User ${newUser.username} created successfully`);
        setDialogOpen(false);
    };
    return (_jsxs(Card, { children: [_jsxs(CardHeader, { className: "pb-2", children: [_jsxs("div", { className: "flex justify-between items-center", children: [_jsx(CardTitle, { children: "All Users" }), _jsxs("div", { className: "relative w-64", children: [_jsx(Search, { className: "absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" }), _jsx(Input, { placeholder: "Search users...", className: "pl-8", value: searchTerm, onChange: (e) => setSearchTerm(e.target.value) })] })] }), _jsx(CardDescription, { children: "Manage user accounts and access permissions." })] }), _jsxs(CardContent, { children: [hasPermission(Permission.USER_MANAGEMENT_CREATE) && (_jsx("div", { className: "mb-4 flex justify-end", children: _jsx(AddUserDialog, { open: dialogOpen, onOpenChange: setDialogOpen, onAddUser: handleAddUser, rolesData: rolesData }) })), _jsxs(Table, { children: [_jsx(TableHeader, { children: _jsxs(TableRow, { children: [_jsx(TableHead, { children: "Username" }), _jsx(TableHead, { children: "Email" }), _jsx(TableHead, { children: "Role" }), _jsx(TableHead, { children: "Status" }), _jsx(TableHead, { children: "Created" }), _jsx(TableHead, { className: "text-right", children: "Actions" })] }) }), _jsx(TableBody, { children: filteredUsers.map((user) => (_jsxs(TableRow, { children: [_jsx(TableCell, { className: "font-medium", children: user.username }), _jsx(TableCell, { children: user.email }), _jsx(TableCell, { children: _jsx(Badge, { variant: user.roles && user.roles[0]?.name === 'admin' ? 'destructive' : user.roles && user.roles[0]?.name === 'manager' ? 'default' : 'secondary', children: user.roles && user.roles.length > 0 ? user.roles[0].name : 'N/A' }) }), _jsx(TableCell, { children: _jsx(Badge, { variant: user.is_active ? 'default' : 'outline', children: user.is_active ? 'active' : 'inactive' }) }), _jsx(TableCell, { children: new Date(user.created_at).toLocaleDateString() }), _jsx(TableCell, { className: "text-right", children: _jsx(Button, { size: "sm", variant: "outline", onClick: () => toast.info("Edit feature coming soon"), children: "Edit" }) })] }, user.id))) })] })] })] }));
};
export default UsersTab;
