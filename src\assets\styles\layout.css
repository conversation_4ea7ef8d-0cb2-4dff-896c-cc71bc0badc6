/* Main layout styles */
.app-container {
  display: flex;
  min-height: 100vh;
  background-color: #f5f7fa;
}

/* Navigation sidebar */
.main-navigation {
  width: 260px;
  background-color: #202a3c;
  color: #ffffff;
  display: flex;
  flex-direction: column;
  height: 100vh;
  position: sticky;
  top: 0;
  overflow-y: auto;
  box-shadow: 2px 0 10px rgba(0, 0, 0, 0.1);
}

.navigation-header {
  padding: 1.5rem 1rem;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.app-logo {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  font-size: 1.25rem;
  font-weight: 700;
}

.app-logo img {
  height: 32px;
  width: auto;
}

.nav-list {
  list-style: none;
  padding: 1rem 0;
  margin: 0;
}

.nav-item {
  margin-bottom: 0.25rem;
}

.nav-link, .nav-group-header {
  display: flex;
  align-items: center;
  padding: 0.75rem 1.5rem;
  color: rgba(255, 255, 255, 0.8);
  text-decoration: none;
  transition: all 0.2s ease;
  cursor: pointer;
}

.nav-link:hover, .nav-group-header:hover {
  background-color: rgba(255, 255, 255, 0.1);
  color: #ffffff;
}

.nav-item.active > .nav-link {
  background-color: rgba(59, 130, 246, 0.8);
  color: #ffffff;
  font-weight: 500;
}

.nav-icon {
  margin-right: 0.75rem;
  font-size: 1.25rem;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 1.5rem;
}

.expand-icon {
  margin-left: auto;
  font-size: 0.75rem;
  transition: transform 0.2s ease;
}

.expand-icon.expanded {
  transform: rotate(180deg);
}

.nav-children {
  list-style: none;
  padding: 0;
  margin: 0;
  background-color: rgba(0, 0, 0, 0.2);
}

.nav-children .nav-link, .nav-children .nav-group-header {
  padding-left: 3rem;
}

.navigation-footer {
  margin-top: auto;
  padding: 1rem;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.user-info {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.user-avatar img {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  object-fit: cover;
}

.user-name {
  font-weight: 500;
}

.user-role {
  font-size: 0.75rem;
  opacity: 0.7;
}

/* Content area */
.content-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow-x: hidden;
}

.content-header {
  background-color: #ffffff;
  padding: 1rem 1.5rem;
  border-bottom: 1px solid #e5e7eb;
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 64px;
  position: sticky;
  top: 0;
  z-index: 10;
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.search-bar {
  position: relative;
  width: 240px;
}

.search-bar input {
  width: 100%;
  padding: 0.5rem 0.75rem;
  padding-right: 2.5rem;
  border: 1px solid #e5e7eb;
  border-radius: 0.375rem;
  outline: none;
  transition: border-color 0.2s ease;
}

.search-bar input:focus {
  border-color: #3b82f6;
}

.search-button {
  position: absolute;
  right: 0.5rem;
  top: 50%;
  transform: translateY(-50%);
  background: none;
  border: none;
  cursor: pointer;
  color: #64748b;
}

.notification-button {
  background: none;
  border: none;
  font-size: 1.25rem;
  cursor: pointer;
  color: #64748b;
  transition: color 0.2s ease;
}

.notification-button:hover {
  color: #3b82f6;
}

.content-main {
  flex: 1;
  padding: 1.5rem;
}

.content-footer {
  padding: 1rem 1.5rem;
  border-top: 1px solid #e5e7eb;
  display: flex;
  justify-content: space-between;
  color: #64748b;
  font-size: 0.875rem;
}

/* Dashboard styles */
.dashboard-container {
  padding: 1rem;
}

.dashboard-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 1.5rem;
  margin-top: 1.5rem;
}

.dashboard-card {
  background-color: #ffffff;
  border-radius: 0.5rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  padding: 1.5rem;
}

.dashboard-card h2 {
  margin-top: 0;
  margin-bottom: 1rem;
  color: #334155;
  font-size: 1.25rem;
}

.status-indicators {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.status-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-bottom: 0.5rem;
  border-bottom: 1px solid #f1f5f9;
}

.status-value {
  font-weight: 500;
  border-radius: 9999px;
  padding: 0.25rem 0.75rem;
  font-size: 0.875rem;
}

.status-value.green {
  background-color: #dcfce7;
  color: #166534;
}

.status-value.amber {
  background-color: #fef3c7;
  color: #92400e;
}

.status-value.red {
  background-color: #fee2e2;
  color: #b91c1c;
}

.quick-access-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 1rem;
}

.quick-link {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-decoration: none;
  color: #334155;
  background-color: #f8fafc;
  border-radius: 0.5rem;
  padding: 1rem;
  transition: all 0.2s ease;
}

.quick-link:hover {
  background-color: #f1f5f9;
  transform: translateY(-2px);
}

.quick-link .icon {
  font-size: 1.5rem;
  margin-bottom: 0.5rem;
}

.notification-list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.notification-item {
  padding: 0.75rem 0;
  border-bottom: 1px solid #f1f5f9;
  display: flex;
  flex-direction: column;
}

.notification-time {
  font-size: 0.75rem;
  color: #64748b;
  margin-bottom: 0.25rem;
}

.notification-message {
  color: #334155;
}

/* Error pages */
.error-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 80vh;
  text-align: center;
}

.error-content {
  max-width: 32rem;
  padding: 2rem;
}

.error-icon {
  font-size: 5rem;
  margin: 1.5rem 0;
}

.error-actions {
  margin-top: 2rem;
}

.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  border-radius: 0.375rem;
  font-weight: 500;
  padding: 0.625rem 1.25rem;
  transition: all 0.2s ease;
  cursor: pointer;
  text-decoration: none;
}

.btn-primary {
  background-color: #3b82f6;
  color: white;
  border: 1px solid transparent;
}

.btn-primary:hover {
  background-color: #2563eb;
}

/* Loading states */
.loading-spinner {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 200px;
}

.loading-app {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100vh;
  font-size: 1.25rem;
  color: #64748b;
}