import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Textarea } from '@/components/ui/textarea';
import { 
  Table, 
  TableBody, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from '@/components/ui/table';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Label } from '@/components/ui/label';
import { 
  Plus, 
  Search, 
  Edit, 
  Trash2, 
  Check,
  X,
  FileText,
  Calendar,
  DollarSign,
  Eye
} from 'lucide-react';

interface JournalLine {
  id: string;
  accountId: string;
  debit: number;
  credit: number;
  description: string;
}

interface JournalEntry {
  id: string;
  entryNumber: string;
  date: Date;
  description: string;
  reference: string;
  status: string;
  lines: JournalLine[];
  createdBy: string;
  totalAmount: number;
}

interface Account {
  id: string;
  code: string;
  name: string;
  type: string;
}

export const JournalEntries: React.FC = () => {
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedStatus, setSelectedStatus] = useState<string>('ALL');
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
  const [viewingEntry, setViewingEntry] = useState<JournalEntry | null>(null);

  // Mock data for demonstration
  const mockAccounts: Account[] = [
    { id: '1', code: '1000', name: 'Cash', type: 'ASSET' },
    { id: '2', code: '1200', name: 'Accounts Receivable', type: 'ASSET' },
    { id: '3', code: '2000', name: 'Accounts Payable', type: 'LIABILITY' },
    { id: '4', code: '4000', name: 'Sales Revenue', type: 'REVENUE' },
    { id: '5', code: '5000', name: 'Office Expenses', type: 'EXPENSE' }
  ];

  const mockEntries: JournalEntry[] = [
    {
      id: '1',
      entryNumber: 'JE-2024-001',
      date: new Date('2024-01-15'),
      description: 'Initial cash investment',
      reference: 'INV-001',
      status: 'POSTED',
      createdBy: 'admin',
      totalAmount: 50000,
      lines: [
        {
          id: '1',
          accountId: '1',
          debit: 50000,
          credit: 0,
          description: 'Cash received from investor'
        },
        {
          id: '2',
          accountId: '4',
          debit: 0,
          credit: 50000,
          description: 'Owner equity contribution'
        }
      ]
    },
    {
      id: '2',
      entryNumber: 'JE-2024-002',
      date: new Date('2024-01-20'),
      description: 'Office supplies purchase',
      reference: 'PO-001',
      status: 'APPROVED',
      createdBy: 'admin',
      totalAmount: 1500,
      lines: [
        {
          id: '3',
          accountId: '5',
          debit: 1500,
          credit: 0,
          description: 'Office supplies expense'
        },
        {
          id: '4',
          accountId: '1',
          debit: 0,
          credit: 1500,
          description: 'Cash payment for supplies'
        }
      ]
    },
    {
      id: '3',
      entryNumber: 'JE-2024-003',
      date: new Date('2024-01-25'),
      description: 'Service revenue earned',
      reference: 'INV-002',
      status: 'DRAFT',
      createdBy: 'admin',
      totalAmount: 5000,
      lines: [
        {
          id: '5',
          accountId: '2',
          debit: 5000,
          credit: 0,
          description: 'Accounts receivable for services'
        },
        {
          id: '6',
          accountId: '4',
          debit: 0,
          credit: 5000,
          description: 'Service revenue earned'
        }
      ]
    }
  ];

  const [newEntry, setNewEntry] = useState({
    entryNumber: '',
    date: new Date().toISOString().split('T')[0],
    description: '',
    reference: '',
    status: 'DRAFT',
    lines: [] as JournalLine[],
    createdBy: 'current-user'
  });

  const [newLine, setNewLine] = useState({
    accountId: '',
    debit: 0,
    credit: 0,
    description: ''
  });

  const handleCreateEntry = () => {
    if (newEntry.lines.length === 0) {
      alert('Please add at least one journal line');
      return;
    }

    const totalDebits = newEntry.lines.reduce((sum, line) => sum + line.debit, 0);
    const totalCredits = newEntry.lines.reduce((sum, line) => sum + line.credit, 0);

    if (Math.abs(totalDebits - totalCredits) > 0.01) {
      alert('Journal entry must be balanced (debits must equal credits)');
      return;
    }

    setIsCreateDialogOpen(false);
    resetNewEntry();
    alert('Journal entry created successfully!');
  };

  const resetNewEntry = () => {
    setNewEntry({
      entryNumber: '',
      date: new Date().toISOString().split('T')[0],
      description: '',
      reference: '',
      status: 'DRAFT',
      lines: [],
      createdBy: 'current-user'
    });
    setNewLine({
      accountId: '',
      debit: 0,
      credit: 0,
      description: ''
    });
  };

  const addLineToEntry = () => {
    if (!newLine.accountId) {
      alert('Please select an account');
      return;
    }

    if (newLine.debit === 0 && newLine.credit === 0) {
      alert('Please enter either a debit or credit amount');
      return;
    }

    if (newLine.debit > 0 && newLine.credit > 0) {
      alert('A line cannot have both debit and credit amounts');
      return;
    }

    const line: JournalLine = {
      id: `temp-${Date.now()}`,
      accountId: newLine.accountId,
      debit: newLine.debit,
      credit: newLine.credit,
      description: newLine.description
    };

    setNewEntry({
      ...newEntry,
      lines: [...newEntry.lines, line]
    });

    setNewLine({
      accountId: '',
      debit: 0,
      credit: 0,
      description: ''
    });
  };

  const removeLineFromEntry = (index: number) => {
    const updatedLines = newEntry.lines.filter((_, i) => i !== index);
    setNewEntry({
      ...newEntry,
      lines: updatedLines
    });
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'DRAFT':
        return 'bg-gray-100 text-gray-800';
      case 'PENDING_APPROVAL':
        return 'bg-yellow-100 text-yellow-800';
      case 'APPROVED':
        return 'bg-blue-100 text-blue-800';
      case 'POSTED':
        return 'bg-green-100 text-green-800';
      case 'REVERSED':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(amount);
  };

  const formatDate = (date: Date) => {
    return new Date(date).toLocaleDateString();
  };

  const getAccountName = (accountId: string) => {
    const account = mockAccounts.find(a => a.id === accountId);
    return account ? `${account.code} - ${account.name}` : 'Unknown Account';
  };

  const filteredEntries = mockEntries.filter(entry => {
    const matchesSearch = entry.entryNumber.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         entry.description.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesStatus = selectedStatus === 'ALL' || entry.status === selectedStatus;
    return matchesSearch && matchesStatus;
  });

  const calculateTotals = (lines: JournalLine[]) => {
    const totalDebits = lines.reduce((sum, line) => sum + line.debit, 0);
    const totalCredits = lines.reduce((sum, line) => sum + line.credit, 0);
    return { totalDebits, totalCredits };
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Journal Entries</h1>
          <p className="text-muted-foreground">
            Manage general ledger transactions and postings
          </p>
        </div>
        <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
          <DialogTrigger asChild>
            <Button>
              <Plus className="h-4 w-4 mr-2" />
              New Entry
            </Button>
          </DialogTrigger>
          <DialogContent className="max-w-4xl">
            <DialogHeader>
              <DialogTitle>Create Journal Entry</DialogTitle>
              <DialogDescription>
                Create a new journal entry with balanced debits and credits.
              </DialogDescription>
            </DialogHeader>
            <div className="space-y-6">
              {/* Entry Header */}
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="entry-number">Entry Number</Label>
                  <Input
                    id="entry-number"
                    value={newEntry.entryNumber}
                    onChange={(e) => setNewEntry({...newEntry, entryNumber: e.target.value})}
                    placeholder="Auto-generated if empty"
                  />
                </div>
                <div>
                  <Label htmlFor="entry-date">Date</Label>
                  <Input
                    id="entry-date"
                    type="date"
                    value={newEntry.date}
                    onChange={(e) => setNewEntry({...newEntry, date: e.target.value})}
                  />
                </div>
              </div>
              <div>
                <Label htmlFor="description">Description</Label>
                <Textarea
                  id="description"
                  value={newEntry.description}
                  onChange={(e) => setNewEntry({...newEntry, description: e.target.value})}
                  placeholder="Enter journal entry description"
                />
              </div>
              <div>
                <Label htmlFor="reference">Reference</Label>
                <Input
                  id="reference"
                  value={newEntry.reference}
                  onChange={(e) => setNewEntry({...newEntry, reference: e.target.value})}
                  placeholder="Optional reference number"
                />
              </div>

              {/* Add Line Section */}
              <Card>
                <CardHeader>
                  <CardTitle>Add Journal Line</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-5 gap-4">
                    <div>
                      <Label>Account</Label>
                      <Select
                        value={newLine.accountId}
                        onValueChange={(value) => setNewLine({...newLine, accountId: value})}
                      >
                        <SelectTrigger>
                          <SelectValue placeholder="Select account" />
                        </SelectTrigger>
                        <SelectContent>
                          {mockAccounts.map(account => (
                            <SelectItem key={account.id} value={account.id}>
                              {account.code} - {account.name}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>
                    <div>
                      <Label>Debit</Label>
                      <Input
                        type="number"
                        step="0.01"
                        value={newLine.debit || ''}
                        onChange={(e) => setNewLine({...newLine, debit: parseFloat(e.target.value) || 0})}
                        placeholder="0.00"
                      />
                    </div>
                    <div>
                      <Label>Credit</Label>
                      <Input
                        type="number"
                        step="0.01"
                        value={newLine.credit || ''}
                        onChange={(e) => setNewLine({...newLine, credit: parseFloat(e.target.value) || 0})}
                        placeholder="0.00"
                      />
                    </div>
                    <div>
                      <Label>Description</Label>
                      <Input
                        value={newLine.description}
                        onChange={(e) => setNewLine({...newLine, description: e.target.value})}
                        placeholder="Line description"
                      />
                    </div>
                    <div className="flex items-end">
                      <Button onClick={addLineToEntry} className="w-full">
                        <Plus className="h-4 w-4 mr-2" />
                        Add
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Journal Lines */}
              {newEntry.lines.length > 0 && (
                <Card>
                  <CardHeader>
                    <CardTitle>Journal Lines</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <Table>
                      <TableHeader>
                        <TableRow>
                          <TableHead>Account</TableHead>
                          <TableHead>Description</TableHead>
                          <TableHead className="text-right">Debit</TableHead>
                          <TableHead className="text-right">Credit</TableHead>
                          <TableHead></TableHead>
                        </TableRow>
                      </TableHeader>
                      <TableBody>
                        {newEntry.lines.map((line, index) => (
                          <TableRow key={index}>
                            <TableCell>{getAccountName(line.accountId)}</TableCell>
                            <TableCell>{line.description}</TableCell>
                            <TableCell className="text-right">
                              {line.debit > 0 ? formatCurrency(line.debit) : '-'}
                            </TableCell>
                            <TableCell className="text-right">
                              {line.credit > 0 ? formatCurrency(line.credit) : '-'}
                            </TableCell>
                            <TableCell>
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={() => removeLineFromEntry(index)}
                              >
                                <X className="h-4 w-4" />
                              </Button>
                            </TableCell>
                          </TableRow>
                        ))}
                        <TableRow className="font-bold">
                          <TableCell colSpan={2}>Totals</TableCell>
                          <TableCell className="text-right">
                            {formatCurrency(calculateTotals(newEntry.lines).totalDebits)}
                          </TableCell>
                          <TableCell className="text-right">
                            {formatCurrency(calculateTotals(newEntry.lines).totalCredits)}
                          </TableCell>
                          <TableCell></TableCell>
                        </TableRow>
                      </TableBody>
                    </Table>
                  </CardContent>
                </Card>
              )}
            </div>
            <DialogFooter>
              <Button variant="outline" onClick={() => setIsCreateDialogOpen(false)}>
                Cancel
              </Button>
              <Button onClick={handleCreateEntry}>
                Create Entry
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </div>

      {/* Filters */}
      <Card>
        <CardHeader>
          <CardTitle>Filters</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex space-x-4">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="Search entries..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-8"
                />
              </div>
            </div>
            <Select value={selectedStatus} onValueChange={setSelectedStatus}>
              <SelectTrigger className="w-[180px]">
                <SelectValue placeholder="Status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="ALL">All Status</SelectItem>
                <SelectItem value="DRAFT">Draft</SelectItem>
                <SelectItem value="PENDING_APPROVAL">Pending Approval</SelectItem>
                <SelectItem value="APPROVED">Approved</SelectItem>
                <SelectItem value="POSTED">Posted</SelectItem>
                <SelectItem value="REVERSED">Reversed</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </CardContent>
      </Card>

      {/* Entries Table */}
      <Card>
        <CardHeader>
          <CardTitle>Journal Entries</CardTitle>
          <CardDescription>
            {filteredEntries.length} entries found
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Entry #</TableHead>
                <TableHead>Date</TableHead>
                <TableHead>Description</TableHead>
                <TableHead>Reference</TableHead>
                <TableHead>Status</TableHead>
                <TableHead className="text-right">Amount</TableHead>
                <TableHead className="text-right">Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {filteredEntries.map((entry) => (
                <TableRow key={entry.id}>
                  <TableCell className="font-medium">{entry.entryNumber}</TableCell>
                  <TableCell>{formatDate(entry.date)}</TableCell>
                  <TableCell>{entry.description}</TableCell>
                  <TableCell>{entry.reference}</TableCell>
                  <TableCell>
                    <Badge className={getStatusColor(entry.status)}>
                      {entry.status}
                    </Badge>
                  </TableCell>
                  <TableCell className="text-right font-medium">
                    {formatCurrency(entry.totalAmount)}
                  </TableCell>
                  <TableCell className="text-right">
                    <div className="flex justify-end space-x-2">
                      <Button 
                        variant="ghost" 
                        size="sm"
                        onClick={() => setViewingEntry(entry)}
                      >
                        <Eye className="h-4 w-4" />
                      </Button>
                      <Button variant="ghost" size="sm">
                        <Edit className="h-4 w-4" />
                      </Button>
                      {entry.status === 'DRAFT' && (
                        <Button variant="ghost" size="sm">
                          <Check className="h-4 w-4" />
                        </Button>
                      )}
                    </div>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>

      {/* View Entry Dialog */}
      <Dialog open={!!viewingEntry} onOpenChange={() => setViewingEntry(null)}>
        <DialogContent className="max-w-4xl">
          <DialogHeader>
            <DialogTitle>Journal Entry Details</DialogTitle>
            <DialogDescription>
              {viewingEntry?.entryNumber} - {viewingEntry?.description}
            </DialogDescription>
          </DialogHeader>
          {viewingEntry && (
            <div className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label>Entry Number</Label>
                  <p className="font-medium">{viewingEntry.entryNumber}</p>
                </div>
                <div>
                  <Label>Date</Label>
                  <p className="font-medium">{formatDate(viewingEntry.date)}</p>
                </div>
                <div>
                  <Label>Reference</Label>
                  <p className="font-medium">{viewingEntry.reference || 'N/A'}</p>
                </div>
                <div>
                  <Label>Status</Label>
                  <Badge className={getStatusColor(viewingEntry.status)}>
                    {viewingEntry.status}
                  </Badge>
                </div>
              </div>
              <div>
                <Label>Description</Label>
                <p className="font-medium">{viewingEntry.description}</p>
              </div>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Account</TableHead>
                    <TableHead>Description</TableHead>
                    <TableHead className="text-right">Debit</TableHead>
                    <TableHead className="text-right">Credit</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {viewingEntry.lines.map((line, index) => (
                    <TableRow key={index}>
                      <TableCell>{getAccountName(line.accountId)}</TableCell>
                      <TableCell>{line.description}</TableCell>
                      <TableCell className="text-right">
                        {line.debit > 0 ? formatCurrency(line.debit) : '-'}
                      </TableCell>
                      <TableCell className="text-right">
                        {line.credit > 0 ? formatCurrency(line.credit) : '-'}
                      </TableCell>
                    </TableRow>
                  ))}
                  <TableRow className="font-bold">
                    <TableCell colSpan={2}>Totals</TableCell>
                    <TableCell className="text-right">
                      {formatCurrency(calculateTotals(viewingEntry.lines).totalDebits)}
                    </TableCell>
                    <TableCell className="text-right">
                      {formatCurrency(calculateTotals(viewingEntry.lines).totalCredits)}
                    </TableCell>
                  </TableRow>
                </TableBody>
              </Table>
            </div>
          )}
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default JournalEntries;
