#!/usr/bin/env node

require('dotenv').config();

function checkEnvVars() {
  console.log('🔍 Environment Variables Check\n');

  const requiredVars = [
    'DATABASE_URL',
    'BETTER_AUTH_SECRET',
    'BETTER_AUTH_URL',
    'VITE_BETTER_AUTH_URL'
  ];

  const optionalVars = [
    'NODE_ENV',
    'PORT',
    'VITE_SUPABASE_URL',
    'VITE_SUPABASE_ANON_KEY'
  ];

  console.log('Required Variables:');
  let missingRequired = [];
  
  requiredVars.forEach(varName => {
    const value = process.env[varName];
    if (value) {
      console.log(`✅ ${varName}: ${value.substring(0, 20)}...`);
    } else {
      console.log(`❌ ${varName}: NOT SET`);
      missingRequired.push(varName);
    }
  });

  console.log('\nOptional Variables:');
  optionalVars.forEach(varName => {
    const value = process.env[varName];
    if (value) {
      console.log(`✅ ${varName}: ${value}`);
    } else {
      console.log(`⚠️  ${varName}: NOT SET`);
    }
  });

  console.log('\nValidation:');
  
  // Check DATABASE_URL format
  const dbUrl = process.env.DATABASE_URL;
  if (dbUrl) {
    if (dbUrl.startsWith('postgresql://')) {
      console.log('✅ DATABASE_URL format looks correct');
    } else {
      console.log('❌ DATABASE_URL should start with postgresql://');
    }
  }

  // Check BETTER_AUTH_SECRET length
  const authSecret = process.env.BETTER_AUTH_SECRET;
  if (authSecret) {
    if (authSecret.length >= 32) {
      console.log('✅ BETTER_AUTH_SECRET length is sufficient');
    } else {
      console.log(`❌ BETTER_AUTH_SECRET too short (${authSecret.length} chars, need 32+)`);
    }
  }

  // Check URL consistency
  const authUrl = process.env.BETTER_AUTH_URL;
  const viteAuthUrl = process.env.VITE_BETTER_AUTH_URL;
  if (authUrl && viteAuthUrl) {
    if (viteAuthUrl.startsWith(authUrl)) {
      console.log('✅ Auth URLs are consistent');
    } else {
      console.log('❌ Auth URL mismatch:');
      console.log(`   BETTER_AUTH_URL: ${authUrl}`);
      console.log(`   VITE_BETTER_AUTH_URL: ${viteAuthUrl}`);
    }
  }

  if (missingRequired.length > 0) {
    console.log(`\n❌ Missing required variables: ${missingRequired.join(', ')}`);
    process.exit(1);
  } else {
    console.log('\n✅ All required environment variables are set');
  }
}

checkEnvVars();