import React, { useState, useEffect } from 'react';
import { AdvancedChart } from './AdvancedChart';
import { analyticsService } from '../../services/analytics/analyticsService';
import { businessIntelligenceService } from '../../services/analytics/businessIntelligenceService';
import { dataQualityService } from '../../services/analytics/dataQualityService';
import { performanceAnalyticsService } from '../../services/analytics/performanceAnalyticsService';
import { 
  AnalyticsDashboard as DashboardType, 
  DashboardWidget, 
  MetricCategory,
  ChartType 
} from '../../services/analytics/types';

interface AnalyticsDashboardProps {
  dashboardId?: string;
  timeRange?: {
    start: Date;
    end: Date;
  };
  refreshInterval?: number;
  editable?: boolean;
}

export const AnalyticsDashboard: React.FC<AnalyticsDashboardProps> = ({
  dashboardId,
  timeRange,
  refreshInterval = 30000,
  editable = false
}) => {
  const [dashboard, setDashboard] = useState<DashboardType | null>(null);
  const [widgets, setWidgets] = useState<DashboardWidget[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedTimeRange, setSelectedTimeRange] = useState(
    timeRange || {
      start: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000), // Last 7 days
      end: new Date()
    }
  );

  useEffect(() => {
    loadDashboard();
  }, [dashboardId]);

  useEffect(() => {
    if (refreshInterval > 0) {
      const interval = setInterval(loadDashboard, refreshInterval);
      return () => clearInterval(interval);
    }
  }, [refreshInterval]);

  const loadDashboard = async () => {
    try {
      setLoading(true);
      setError(null);

      if (dashboardId) {
        // Load existing dashboard
        const dashboards = await analyticsService.getDashboards();
        const foundDashboard = dashboards.data.find(d => d.id === dashboardId);
        
        if (foundDashboard) {
          setDashboard(foundDashboard);
          setWidgets(foundDashboard.widgets);
        } else {
          throw new Error('Dashboard not found');
        }
      } else {
        // Create default dashboard
        await createDefaultDashboard();
      }

      setLoading(false);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load dashboard');
      setLoading(false);
    }
  };

  const createDefaultDashboard = async () => {
    const defaultWidgets: DashboardWidget[] = [
      {
        id: 'revenue-chart',
        type: 'chart',
        title: 'Revenue Trend',
        description: 'Monthly revenue over time',
        dataSource: 'business_metrics',
        query: 'revenue',
        visualization: {
          type: 'line',
          xAxis: { label: 'Month', type: 'time' },
          yAxis: { label: 'Revenue ($)', type: 'value' },
          colors: ['#3b82f6']
        },
        position: { x: 0, y: 0 },
        size: { width: 6, height: 4 },
        filters: []
      },
      {
        id: 'user-metrics',
        type: 'metric',
        title: 'Active Users',
        description: 'Current active users',
        dataSource: 'user_behavior',
        query: 'active_users',
        visualization: {
          type: 'gauge',
          colors: ['#10b981']
        },
        position: { x: 6, y: 0 },
        size: { width: 3, height: 2 },
        filters: []
      },
      {
        id: 'performance-summary',
        type: 'table',
        title: 'Performance Summary',
        description: 'System performance metrics',
        dataSource: 'performance',
        query: 'summary',
        visualization: {
          type: 'bar'
        },
        position: { x: 9, y: 0 },
        size: { width: 3, height: 4 },
        filters: []
      },
      {
        id: 'data-quality',
        type: 'metric',
        title: 'Data Quality Score',
        description: 'Overall data quality percentage',
        dataSource: 'data_quality',
        query: 'overall_score',
        visualization: {
          type: 'gauge',
          colors: ['#f59e0b', '#ef4444', '#10b981']
        },
        position: { x: 6, y: 2 },
        size: { width: 3, height: 2 },
        filters: []
      },
      {
        id: 'top-pages',
        type: 'chart',
        title: 'Top Pages',
        description: 'Most visited pages',
        dataSource: 'user_behavior',
        query: 'top_pages',
        visualization: {
          type: 'bar',
          xAxis: { label: 'Page', type: 'category' },
          yAxis: { label: 'Views', type: 'value' },
          colors: ['#8b5cf6']
        },
        position: { x: 0, y: 4 },
        size: { width: 6, height: 3 },
        filters: []
      },
      {
        id: 'error-rate',
        type: 'chart',
        title: 'Error Rate',
        description: 'Application error rate over time',
        dataSource: 'performance',
        query: 'error_rate',
        visualization: {
          type: 'line',
          xAxis: { label: 'Time', type: 'time' },
          yAxis: { label: 'Error Rate (%)', type: 'value' },
          colors: ['#ef4444']
        },
        position: { x: 6, y: 4 },
        size: { width: 6, height: 3 },
        filters: []
      }
    ];

    const defaultDashboard: DashboardType = {
      id: 'default-dashboard',
      name: 'Analytics Overview',
      description: 'Comprehensive analytics dashboard',
      widgets: defaultWidgets,
      layout: { columns: 12, rows: 8, gap: 16 },
      filters: [],
      refreshInterval: 30000,
      isPublic: false,
      owner: 'system',
      createdAt: new Date(),
      updatedAt: new Date()
    };

    setDashboard(defaultDashboard);
    setWidgets(defaultWidgets);
  };

  const loadWidgetData = async (widget: DashboardWidget) => {
    try {
      switch (widget.dataSource) {
        case 'business_metrics':
          return await loadBusinessMetrics(widget);
        case 'user_behavior':
          return await loadUserBehaviorData(widget);
        case 'performance':
          return await loadPerformanceData(widget);
        case 'data_quality':
          return await loadDataQualityData(widget);
        default:
          return [];
      }
    } catch (error) {
      console.error(`Error loading data for widget ${widget.id}:`, error);
      return [];
    }
  };

  const loadBusinessMetrics = async (widget: DashboardWidget) => {
    const metrics = await analyticsService.getMetrics(
      'business' as MetricCategory,
      selectedTimeRange.start,
      selectedTimeRange.end
    );
    
    if (widget.query === 'revenue') {
      return metrics.data
        .filter(m => m.name === 'revenue')
        .map(m => ({ x: m.timestamp, y: m.value, label: m.name }));
    }
    
    return metrics.data;
  };

  const loadUserBehaviorData = async (widget: DashboardWidget) => {
    if (widget.query === 'active_users') {
      const snapshot = await performanceAnalyticsService.getCurrentPerformanceSnapshot();
      return [{ value: snapshot.active_users || 0 }];
    }
    
    if (widget.query === 'top_pages') {
      // Mock data for top pages
      return [
        { name: '/dashboard', value: 1250 },
        { name: '/analytics', value: 980 },
        { name: '/reports', value: 750 },
        { name: '/settings', value: 420 },
        { name: '/profile', value: 380 }
      ];
    }
    
    return [];
  };

  const loadPerformanceData = async (widget: DashboardWidget) => {
    const report = await performanceAnalyticsService.getPerformanceReport(
      selectedTimeRange.start,
      selectedTimeRange.end
    );
    
    if (widget.query === 'summary') {
      return [
        { metric: 'Avg Response Time', value: `${report.summary.avg_response_time}ms` },
        { metric: 'Total Requests', value: report.summary.total_requests.toLocaleString() },
        { metric: 'Error Rate', value: `${report.summary.error_rate}%` },
        { metric: 'Uptime', value: `${report.summary.uptime_percentage}%` }
      ];
    }
    
    if (widget.query === 'error_rate') {
      return report.metrics
        .filter(m => m.type === 'error_rate')
        .map(m => ({ x: m.timestamp, y: m.value }));
    }
    
    return [];
  };

  const loadDataQualityData = async (widget: DashboardWidget) => {
    const reports = await dataQualityService.getQualityReports(
      undefined,
      selectedTimeRange.start,
      selectedTimeRange.end
    );
    
    const latestReport = reports.data[0];
    if (widget.query === 'overall_score') {
      return [{ value: latestReport?.overall_score || 100 }];
    }
    
    return [];
  };

  const renderWidget = (widget: DashboardWidget) => {
    const [widgetData, setWidgetData] = useState<any[]>([]);
    const [widgetLoading, setWidgetLoading] = useState(true);

    useEffect(() => {
      const loadData = async () => {
        setWidgetLoading(true);
        const data = await loadWidgetData(widget);
        setWidgetData(data);
        setWidgetLoading(false);
      };
      
      loadData();
    }, [widget, selectedTimeRange]);

    const widgetStyle = {
      gridColumn: `span ${widget.size.width}`,
      gridRow: `span ${widget.size.height}`,
    };

    return (
      <div
        key={widget.id}
        className="bg-white rounded-lg shadow-sm border p-4"
        style={widgetStyle}
      >
        <div className="flex justify-between items-center mb-4">
          <div>
            <h3 className="text-lg font-semibold text-gray-900">{widget.title}</h3>
            {widget.description && (
              <p className="text-sm text-gray-600">{widget.description}</p>
            )}
          </div>
          {editable && (
            <div className="flex space-x-2">
              <button
                className="text-gray-400 hover:text-gray-600"
                onClick={() => editWidget(widget)}
              >
                ⚙️
              </button>
              <button
                className="text-gray-400 hover:text-red-600"
                onClick={() => removeWidget(widget.id)}
              >
                🗑️
              </button>
            </div>
          )}
        </div>

        {widgetLoading ? (
          <div className="flex items-center justify-center h-32">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
          </div>
        ) : (
          <div className="h-full">
            {widget.type === 'chart' && (
              <AdvancedChart
                type={widget.visualization.type as ChartType}
                data={widgetData}
                config={widget.visualization}
                width={300}
                height={200}
                interactive
              />
            )}
            
            {widget.type === 'metric' && (
              <div className="text-center">
                <div className="text-3xl font-bold text-blue-600">
                  {widgetData[0]?.value || 0}
                </div>
                <div className="text-sm text-gray-500 mt-2">
                  {widget.title}
                </div>
              </div>
            )}
            
            {widget.type === 'table' && (
              <div className="overflow-x-auto">
                <table className="min-w-full divide-y divide-gray-200">
                  <tbody className="divide-y divide-gray-200">
                    {widgetData.map((row, index) => (
                      <tr key={index}>
                        <td className="px-3 py-2 text-sm font-medium text-gray-900">
                          {row.metric}
                        </td>
                        <td className="px-3 py-2 text-sm text-gray-500">
                          {row.value}
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            )}
          </div>
        )}
      </div>
    );
  };

  const editWidget = (widget: DashboardWidget) => {
    // This would open a widget editor modal
    console.log('Edit widget:', widget);
  };

  const removeWidget = (widgetId: string) => {
    setWidgets(widgets.filter(w => w.id !== widgetId));
  };

  const addWidget = () => {
    // This would open a widget creation modal
    console.log('Add new widget');
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-red-50 border border-red-200 rounded-lg p-4">
        <div className="text-red-800">
          <h3 className="font-medium">Dashboard Error</h3>
          <p className="text-sm mt-1">{error}</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Dashboard Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">
            {dashboard?.name || 'Analytics Dashboard'}
          </h1>
          <p className="text-gray-600">
            {dashboard?.description || 'Comprehensive analytics overview'}
          </p>
        </div>
        
        <div className="flex items-center space-x-4">
          {/* Time Range Selector */}
          <select
            className="border border-gray-300 rounded-md px-3 py-2 text-sm"
            title="Select time range"
            aria-label="Select time range for dashboard data"
            onChange={(e) => {
              const days = parseInt(e.target.value);
              setSelectedTimeRange({
                start: new Date(Date.now() - days * 24 * 60 * 60 * 1000),
                end: new Date()
              });
            }}
          >
            <option value="7">Last 7 days</option>
            <option value="30">Last 30 days</option>
            <option value="90">Last 90 days</option>
          </select>

          {editable && (
            <button
              onClick={addWidget}
              className="bg-blue-600 text-white px-4 py-2 rounded-md text-sm hover:bg-blue-700"
            >
              Add Widget
            </button>
          )}

          <button
            onClick={loadDashboard}
            className="bg-gray-100 text-gray-700 px-4 py-2 rounded-md text-sm hover:bg-gray-200"
          >
            Refresh
          </button>
        </div>
      </div>

      {/* Dashboard Grid */}
      <div 
        className="grid gap-4"
        style={{
          gridTemplateColumns: `repeat(${dashboard?.layout.columns || 12}, minmax(0, 1fr))`,
          gridAutoRows: 'minmax(100px, auto)'
        }}
      >
        {widgets.map(renderWidget)}
      </div>

      {/* Dashboard Footer */}
      <div className="text-center text-sm text-gray-500">
        Last updated: {new Date().toLocaleString()}
        {refreshInterval > 0 && (
          <span className="ml-2">
            • Auto-refresh every {refreshInterval / 1000}s
          </span>
        )}
      </div>
    </div>
  );
};

export default AnalyticsDashboard;
