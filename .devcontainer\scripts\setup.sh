#!/bin/bash

# Setup script for NXT-WEB-DEV-X with MCP servers
set -e

echo "🚀 Setting up NXT-WEB-DEV-X development environment with MCP servers..."

# Install project dependencies
echo "📦 Installing project dependencies..."
npm install

# Create MCP configuration directory
echo "🔧 Setting up MCP configuration..."
mkdir -p /workspace/mcp-config
mkdir -p /mcp-data

# Set permissions
sudo chown -R vscode:vscode /mcp-data
sudo chmod -R 755 /mcp-data

# Create environment file template if it doesn't exist
if [ ! -f /workspace/.env ]; then
    echo "📝 Creating environment file template..."
    cat > /workspace/.env << 'EOF'
# MCP Server API Keys - Add your actual keys here
BRAVE_API_KEY=your_brave_api_key_here
TAVILY_API_KEY=your_tavily_api_key_here
FIRECRAWL_API_KEY=your_firecrawl_api_key_here
UPSTASH_REDIS_REST_URL=your_upstash_redis_url_here
UPSTASH_REDIS_REST_TOKEN=your_upstash_redis_token_here
NOTION_API_KEY=your_notion_api_key_here
SUPABASE_ACCESS_TOKEN=your_supabase_access_token_here
SUPABASE_PROJECT_ID=your_supabase_project_id_here
NEO4J_URI=your_neo4j_uri_here
NEO4J_USERNAME=your_neo4j_username_here
NEO4J_PASSWORD=your_neo4j_password_here

# Existing environment variables
VITE_SUPABASE_URL=https://your-project-ref.supabase.co
VITE_SUPABASE_ANON_KEY=your-anon-key-here
SUPABASE_SERVICE_ROLE_KEY=your-service-role-key-here
VITE_APP_NAME=NXT-DOT-X
VITE_APP_VERSION=1.0.0
VITE_APP_ENV=development
VITE_API_BASE_URL=https://your-project-ref.supabase.co/functions/v1
VITE_STORAGE_URL=https://your-project-ref.supabase.co/storage/v1
VITE_AUTH_REDIRECT_URL=http://localhost:3000/auth/callback
VITE_AUTH_SITE_URL=http://localhost:3000
VITE_ENABLE_ANALYTICS=true
VITE_ENABLE_REALTIME=true
VITE_ENABLE_STORAGE=true
EOF
    echo "⚠️  Please update the .env file with your actual API keys!"
fi

# Install MCP SDK globally
echo "🔌 Installing MCP SDK..."
npm install -g @modelcontextprotocol/sdk

# Create MCP server health check script
echo "🏥 Creating MCP health check script..."
cat > /workspace/mcp-config/health-check.js << 'EOF'
const http = require('http');

const servers = [
    { name: 'Brave Search', port: 8080 },
    { name: 'Tavily', port: 8081 },
    { name: 'FireCrawl', port: 8082 },
    { name: 'Context7', port: 8083 },
    { name: 'Notion', port: 8084 },
    { name: 'Desktop Commander', port: 8085 },
    { name: 'Taskmaster', port: 8086 },
    { name: 'Supabase', port: 8087 },
    { name: 'Browser Tools', port: 8088 },
    { name: 'Magic MCP', port: 8089 },
    { name: 'Neo4j', port: 8090 }
];

async function checkServer(server) {
    return new Promise((resolve) => {
        const req = http.request({
            hostname: 'localhost',
            port: server.port,
            path: '/health',
            method: 'GET',
            timeout: 5000
        }, (res) => {
            resolve({ ...server, status: 'healthy', code: res.statusCode });
        });

        req.on('error', () => {
            resolve({ ...server, status: 'unhealthy', code: null });
        });

        req.on('timeout', () => {
            resolve({ ...server, status: 'timeout', code: null });
        });

        req.end();
    });
}

async function checkAllServers() {
    console.log('🏥 Checking MCP server health...\n');
    
    const results = await Promise.all(servers.map(checkServer));
    
    results.forEach(result => {
        const status = result.status === 'healthy' ? '✅' : '❌';
        console.log(`${status} ${result.name} (port ${result.port}): ${result.status}`);
    });
    
    const healthy = results.filter(r => r.status === 'healthy').length;
    const total = results.length;
    
    console.log(`\n📊 Health Summary: ${healthy}/${total} servers healthy`);
    
    if (healthy === total) {
        console.log('🎉 All MCP servers are running!');
        process.exit(0);
    } else {
        console.log('⚠️  Some MCP servers are not responding');
        process.exit(1);
    }
}

checkAllServers();
EOF

chmod +x /workspace/mcp-config/health-check.js

echo "✅ Setup complete! Next steps:"
echo "1. Update your .env file with actual API keys"
echo "2. Run 'npm run dev' to start the development server"
echo "3. Use 'node mcp-config/health-check.js' to check MCP server status"
echo "4. MCP servers will be available on ports 8080-8090"
