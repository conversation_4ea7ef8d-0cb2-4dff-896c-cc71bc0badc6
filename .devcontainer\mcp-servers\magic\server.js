#!/usr/bin/env node

const { Server } = require('@modelcontextprotocol/sdk/server/index.js');
const { StdioServerTransport } = require('@modelcontextprotocol/sdk/server/stdio.js');
const { CallToolRequestSchema, ListToolsRequestSchema } = require('@modelcontextprotocol/sdk/types.js');
const express = require('express');
const axios = require('axios');

class MagicMcpServer {
  constructor() {
    this.server = new Server(
      {
        name: 'magic-mcp-server',
        version: '1.0.0',
      },
      {
        capabilities: {
          tools: {},
        },
      }
    );

    this.port = process.env.MCP_PORT || 8089;
    
    this.setupToolHandlers();
    this.setupHealthCheck();
  }

  setupToolHandlers() {
    this.server.setRequestHandler(ListToolsRequestSchema, async () => {
      return {
        tools: [
          {
            name: 'magic_code_generation',
            description: 'Generate code using AI magic',
            inputSchema: {
              type: 'object',
              properties: {
                prompt: {
                  type: 'string',
                  description: 'Code generation prompt',
                },
                language: {
                  type: 'string',
                  description: 'Programming language',
                  enum: ['javascript', 'python', 'typescript', 'java', 'go', 'rust'],
                  default: 'javascript',
                },
                style: {
                  type: 'string',
                  description: 'Code style',
                  enum: ['functional', 'object-oriented', 'minimal', 'verbose'],
                  default: 'functional',
                },
              },
              required: ['prompt'],
            },
          },
          {
            name: 'magic_data_transformation',
            description: 'Transform data using magic algorithms',
            inputSchema: {
              type: 'object',
              properties: {
                data: {
                  type: 'string',
                  description: 'Input data (JSON string)',
                },
                transformation: {
                  type: 'string',
                  description: 'Type of transformation',
                  enum: ['normalize', 'aggregate', 'filter', 'sort', 'group'],
                },
                parameters: {
                  type: 'object',
                  description: 'Transformation parameters',
                },
              },
              required: ['data', 'transformation'],
            },
          },
          {
            name: 'magic_text_analysis',
            description: 'Analyze text with magical insights',
            inputSchema: {
              type: 'object',
              properties: {
                text: {
                  type: 'string',
                  description: 'Text to analyze',
                },
                analysis_type: {
                  type: 'string',
                  description: 'Type of analysis',
                  enum: ['sentiment', 'keywords', 'summary', 'entities', 'topics'],
                  default: 'summary',
                },
                depth: {
                  type: 'string',
                  description: 'Analysis depth',
                  enum: ['shallow', 'medium', 'deep'],
                  default: 'medium',
                },
              },
              required: ['text'],
            },
          },
          {
            name: 'magic_pattern_detection',
            description: 'Detect patterns in data using magic algorithms',
            inputSchema: {
              type: 'object',
              properties: {
                data: {
                  type: 'string',
                  description: 'Data to analyze (JSON string)',
                },
                pattern_type: {
                  type: 'string',
                  description: 'Type of pattern to detect',
                  enum: ['anomalies', 'trends', 'cycles', 'correlations'],
                },
                sensitivity: {
                  type: 'number',
                  description: 'Detection sensitivity (0-1)',
                  default: 0.5,
                  minimum: 0,
                  maximum: 1,
                },
              },
              required: ['data', 'pattern_type'],
            },
          },
        ],
      };
    });

    this.server.setRequestHandler(CallToolRequestSchema, async (request) => {
      const { name, arguments: args } = request.params;

      switch (name) {
        case 'magic_code_generation':
          return await this.handleCodeGeneration(args);
        case 'magic_data_transformation':
          return await this.handleDataTransformation(args);
        case 'magic_text_analysis':
          return await this.handleTextAnalysis(args);
        case 'magic_pattern_detection':
          return await this.handlePatternDetection(args);
        default:
          throw new Error(`Unknown tool: ${name}`);
      }
    });
  }

  async handleCodeGeneration(args) {
    try {
      // Simulated code generation (would integrate with AI service in real implementation)
      const templates = {
        javascript: `// Generated ${args.language} code for: ${args.prompt}\nfunction magicFunction() {\n  // TODO: Implement ${args.prompt}\n  return 'magic result';\n}`,
        python: `# Generated ${args.language} code for: ${args.prompt}\ndef magic_function():\n    # TODO: Implement ${args.prompt}\n    return 'magic result'`,
        typescript: `// Generated ${args.language} code for: ${args.prompt}\nfunction magicFunction(): string {\n  // TODO: Implement ${args.prompt}\n  return 'magic result';\n}`,
      };

      const code = templates[args.language] || templates.javascript;

      const result = {
        prompt: args.prompt,
        language: args.language || 'javascript',
        style: args.style || 'functional',
        generated_code: code,
        lines: code.split('\n').length,
        timestamp: new Date().toISOString(),
      };

      return {
        content: [
          {
            type: 'text',
            text: JSON.stringify(result, null, 2),
          },
        ],
      };
    } catch (error) {
      throw new Error(`Magic Code Generation error: ${error.message}`);
    }
  }

  async handleDataTransformation(args) {
    try {
      let data;
      try {
        data = JSON.parse(args.data);
      } catch {
        throw new Error('Invalid JSON data provided');
      }

      let transformedData;
      switch (args.transformation) {
        case 'normalize':
          transformedData = Array.isArray(data) ? data.map(item => ({ ...item, normalized: true })) : { ...data, normalized: true };
          break;
        case 'aggregate':
          transformedData = Array.isArray(data) ? { count: data.length, items: data } : { single_item: data };
          break;
        case 'filter':
          transformedData = Array.isArray(data) ? data.filter((_, index) => index % 2 === 0) : data;
          break;
        case 'sort':
          transformedData = Array.isArray(data) ? [...data].sort() : data;
          break;
        case 'group':
          transformedData = Array.isArray(data) ? { grouped: data } : { single: data };
          break;
        default:
          transformedData = data;
      }

      const result = {
        original_data: data,
        transformation: args.transformation,
        transformed_data: transformedData,
        parameters: args.parameters || {},
        timestamp: new Date().toISOString(),
      };

      return {
        content: [
          {
            type: 'text',
            text: JSON.stringify(result, null, 2),
          },
        ],
      };
    } catch (error) {
      throw new Error(`Magic Data Transformation error: ${error.message}`);
    }
  }

  async handleTextAnalysis(args) {
    try {
      const text = args.text;
      const analysisType = args.analysis_type || 'summary';
      
      let analysis;
      switch (analysisType) {
        case 'sentiment':
          analysis = {
            sentiment: 'positive',
            confidence: 0.85,
            emotions: ['joy', 'confidence'],
          };
          break;
        case 'keywords':
          analysis = {
            keywords: text.split(' ').filter(word => word.length > 4).slice(0, 5),
            frequency: {},
          };
          break;
        case 'summary':
          analysis = {
            summary: text.substring(0, 100) + '...',
            key_points: ['Point 1', 'Point 2', 'Point 3'],
          };
          break;
        case 'entities':
          analysis = {
            entities: [
              { text: 'Entity1', type: 'PERSON' },
              { text: 'Entity2', type: 'ORGANIZATION' },
            ],
          };
          break;
        case 'topics':
          analysis = {
            topics: ['Technology', 'Business', 'Innovation'],
            relevance_scores: [0.9, 0.7, 0.6],
          };
          break;
        default:
          analysis = { message: 'Unknown analysis type' };
      }

      const result = {
        text_length: text.length,
        analysis_type: analysisType,
        depth: args.depth || 'medium',
        analysis,
        timestamp: new Date().toISOString(),
      };

      return {
        content: [
          {
            type: 'text',
            text: JSON.stringify(result, null, 2),
          },
        ],
      };
    } catch (error) {
      throw new Error(`Magic Text Analysis error: ${error.message}`);
    }
  }

  async handlePatternDetection(args) {
    try {
      let data;
      try {
        data = JSON.parse(args.data);
      } catch {
        throw new Error('Invalid JSON data provided');
      }

      const patternType = args.pattern_type;
      const sensitivity = args.sensitivity || 0.5;

      let patterns;
      switch (patternType) {
        case 'anomalies':
          patterns = {
            anomalies_detected: Math.floor(Math.random() * 5),
            anomaly_indices: [2, 7, 15],
            confidence: sensitivity * 0.9,
          };
          break;
        case 'trends':
          patterns = {
            trend_direction: 'upward',
            trend_strength: sensitivity * 0.8,
            trend_points: [1, 3, 5, 8, 12],
          };
          break;
        case 'cycles':
          patterns = {
            cycle_length: 7,
            cycle_confidence: sensitivity * 0.7,
            cycles_detected: 3,
          };
          break;
        case 'correlations':
          patterns = {
            correlations: [
              { variables: ['A', 'B'], correlation: 0.85 },
              { variables: ['C', 'D'], correlation: -0.62 },
            ],
          };
          break;
        default:
          patterns = { message: 'Unknown pattern type' };
      }

      const result = {
        data_size: Array.isArray(data) ? data.length : 1,
        pattern_type: patternType,
        sensitivity,
        patterns,
        timestamp: new Date().toISOString(),
      };

      return {
        content: [
          {
            type: 'text',
            text: JSON.stringify(result, null, 2),
          },
        ],
      };
    } catch (error) {
      throw new Error(`Magic Pattern Detection error: ${error.message}`);
    }
  }

  setupHealthCheck() {
    const app = express();
    
    app.get('/health', (req, res) => {
      res.json({ 
        status: 'healthy', 
        service: 'magic-mcp',
        timestamp: new Date().toISOString(),
        magic_level: 'maximum',
        note: '21st Dev Magic MCP Server - Simulated AI capabilities'
      });
    });

    app.listen(this.port, () => {
      console.log(`Magic MCP Server health check running on port ${this.port}`);
    });
  }

  async run() {
    const transport = new StdioServerTransport();
    await this.server.connect(transport);
    console.error('Magic MCP server running on stdio');
  }
}

const server = new MagicMcpServer();
server.run().catch(console.error);
