// <PERSON>ript to reset a user's password via the Better Auth approach
// Usage (in PowerShell or terminal):
//   node server/reset-password.mjs <EMAIL> "P@3301"

import dotenv from "dotenv";
import fs from "fs";
import path from "path";
import postgres from "postgres";
import { randomUUID } from "crypto";
import { drizzle } from "drizzle-orm/postgres-js";
import { eq, and } from "drizzle-orm";
import { account as accountTable, user as userTable } from "../src/lib/better-auth-schema.js";
import { hashPassword as customHashPassword, verifyPassword as customVerifyPassword } from "../src/lib/custom-password.js";

// Priority load .env.local if in development, .env.production if in production, else fallback.
if (process.env.NODE_ENV === "development" && fs.existsSync(".env.local")) {
  dotenv.config({ path: ".env.local" });
} else if (process.env.NODE_ENV === "production" && fs.existsSync(".env.production")) {
  dotenv.config({ path: ".env.production" });
} else {
  dotenv.config();
}

// Grab input from CLI
const [,, emailArg, newPasswordArg] = process.argv;
if (!emailArg || !newPasswordArg) {
  console.error("Usage: node server/reset-password.mjs <email> <newPassword>");
  process.exit(1);
}

const email = emailArg;
const newPassword = newPasswordArg;

async function main() {
  try {
    const DATABASE_URL = process.env.DATABASE_URL;
    if (!DATABASE_URL) {
      throw new Error("DATABASE_URL is missing from environment variables.");
    }

    // Connect to Postgres
    const client = postgres(DATABASE_URL, {
      ssl: process.env.NODE_ENV === 'production' ? 'require' : false,
      max: 20,
      idle_timeout: 30,
      connect_timeout: 10,
    });
    const db = drizzle(client);

    // Check if user exists
    const userRows = await db.select()
      .from(userTable)
      .where(eq(userTable.email, email));
    if (userRows.length === 0) {
      console.error(`No user found with email: ${email}`);
      process.exit(1);
    }

    // Generate hashed password
    const hashed = await customHashPassword(newPassword);

    // Update password in account table
    const accountUpdate = await db
      .update(accountTable)
      .set({ password: hashed })
      .where(and(eq(accountTable.providerId, "credential"), eq(accountTable.accountId, email)))
      .returning();

    if (accountUpdate.length === 0) {
      console.warn(`User found, but no matching credential account. Creating one for user ID: ${userRows[0].id}...`);
      
      // Create a new credential account for this user
      const insertResult = await db
        .insert(accountTable)
        .values({
              id: randomUUID(),
              userId: userRows[0].id,
              providerId: "credential",
              accountId: email,
              password: hashed,
        })
        .returning();
      
      if (insertResult.length === 0) {
        console.error("Failed to create credential account. Please check table schema or constraints.");
        process.exit(1);
      } else {
        console.log("New credential account created. Password reset successful for:", email);
        process.exit(0);
      }
    }

    console.log("Password reset successful for:", email);
    process.exit(0);
  } catch (error) {
    console.error("Error resetting password:", error);
    process.exit(1);
  }
}

main();