name: Docker Build and Publish

on:
  push:
    branches: [ "main" ]
  pull_request:
    branches: [ "main" ]

env:
  # Use lowercase dev-xxx as the image name
  IMAGE_NAME: nxtleveltech1/dev-xxx

jobs:
  fix-commits:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout repository
        uses: actions/checkout@v3

      - name: Set Git identity
        run: |
          git config user.name "GitHub Actions"
          git config user.email "<EMAIL>"

      - name: Commit local changes (if any)
        run: |
          git add -A
          git commit -m "Fix commits block" || echo "No changes to commit"

      - name: Push changes
        run: |
          git push origin HEAD:main || echo "No changes to push"

  build:
    runs-on: ubuntu-latest
    permissions:
      contents: read
      packages: write

    steps:
      - name: Checkout repository
        uses: actions/checkout@v3

      # Login to GitHub Container Registry
      - name: Log in to GitHub Container Registry
        uses: docker/login-action@v2
        with:
          registry: ghcr.io
          username: ${{ github.actor }}
          password: ${{ secrets.GITHUB_TOKEN }}

      # Extract metadata for Docker
      - name: Extract Docker metadata
        id: meta
        uses: docker/metadata-action@v4
        with:
          images: ghcr.io/${{ env.IMAGE_NAME }}

      # Build and push Docker image
      - name: Build and push Docker image
        uses: docker/build-push-action@v4
        with:
          context: .
          push: ${{ github.event_name != 'pull_request' }}
          tags: ${{ steps.meta.outputs.tags }}
          labels: ${{ steps.meta.outputs.labels }}