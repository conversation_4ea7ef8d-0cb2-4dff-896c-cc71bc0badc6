import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";
import { 
  Mail, 
  MessageSquare, 
  Users, 
  Send, 
  Calendar,
  Eye,
  Save,
  ArrowLeft,
  Clock,
  Target,
  FileText,
  Settings
} from "lucide-react";
import { 
  CustomerCommunicationService, 
  EmailTemplate, 
  SMSTemplate, 
  ContactSegment,
  Contact
} from "@/services/customer-communication";

interface CampaignComposerProps {
  onBack?: () => void;
  onSave?: (campaign: any) => void;
  onSend?: (campaign: any) => void;
  initialData?: any;
}

const CampaignComposer: React.FC<CampaignComposerProps> = ({
  onBack,
  onSave,
  onSend,
  initialData
}) => {
  const [campaignType, setCampaignType] = useState<'email' | 'sms'>('email');
  const [campaignName, setCampaignName] = useState('');
  const [subject, setSubject] = useState('');
  const [content, setContent] = useState('');
  const [selectedTemplate, setSelectedTemplate] = useState<string>('');
  const [selectedSegments, setSelectedSegments] = useState<string[]>([]);
  const [scheduledAt, setScheduledAt] = useState('');
  const [isScheduled, setIsScheduled] = useState(false);
  
  const [emailTemplates, setEmailTemplates] = useState<EmailTemplate[]>([]);
  const [smsTemplates, setSmsTemplates] = useState<SMSTemplate[]>([]);
  const [segments, setSegments] = useState<ContactSegment[]>([]);
  const [previewMode, setPreviewMode] = useState(false);
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    loadTemplatesAndSegments();
  }, []);

  useEffect(() => {
    if (initialData) {
      setCampaignName(initialData.name || '');
      setCampaignType(initialData.type || 'email');
      setSubject(initialData.subject || '');
      setContent(initialData.content || '');
      setSelectedSegments(initialData.targetSegments || []);
      setScheduledAt(initialData.scheduledAt || '');
      setIsScheduled(!!initialData.scheduledAt);
    }
  }, [initialData]);

  const loadTemplatesAndSegments = async () => {
    try {
      // Mock data - replace with actual API calls
      const mockEmailTemplates: EmailTemplate[] = [
        {
          id: 'welcome-email',
          name: 'Welcome Email',
          subject: 'Welcome to {{company_name}}!',
          content: `
            <h1>Welcome {{customer_name}}!</h1>
            <p>Thank you for joining {{company_name}}. We're excited to have you on board.</p>
            <p>Here's what you can expect:</p>
            <ul>
              <li>Exclusive offers and discounts</li>
              <li>Product updates and news</li>
              <li>Priority customer support</li>
            </ul>
            <p>Best regards,<br>The {{company_name}} Team</p>
          `,
          type: 'transactional',
          variables: ['customer_name', 'company_name'],
          isActive: true,
          createdAt: '2025-05-20T10:00:00Z',
          updatedAt: '2025-05-20T10:00:00Z'
        },
        {
          id: 'product-update',
          name: 'Product Update',
          subject: 'New Features Available - {{product_name}}',
          content: `
            <h1>Exciting Updates to {{product_name}}!</h1>
            <p>Hi {{customer_name}},</p>
            <p>We've just released some amazing new features that we think you'll love:</p>
            <div>{{feature_list}}</div>
            <p><a href="{{update_link}}">Learn More</a></p>
          `,
          type: 'marketing',
          variables: ['customer_name', 'product_name', 'feature_list', 'update_link'],
          isActive: true,
          createdAt: '2025-05-22T14:00:00Z',
          updatedAt: '2025-05-22T14:00:00Z'
        }
      ];

      const mockSmsTemplates: SMSTemplate[] = [
        {
          id: 'welcome-sms',
          name: 'Welcome SMS',
          content: 'Welcome to {{company_name}}, {{customer_name}}! Your account is ready. Reply STOP to opt out.',
          type: 'transactional',
          variables: ['customer_name', 'company_name'],
          isActive: true,
          createdAt: '2025-05-20T10:00:00Z',
          updatedAt: '2025-05-20T10:00:00Z'
        },
        {
          id: 'flash-sale',
          name: 'Flash Sale Alert',
          content: '🔥 FLASH SALE: {{discount}}% off {{product_name}}! Use code {{promo_code}}. Valid until {{expiry}}. Shop now: {{link}}',
          type: 'marketing',
          variables: ['discount', 'product_name', 'promo_code', 'expiry', 'link'],
          isActive: true,
          createdAt: '2025-05-24T09:00:00Z',
          updatedAt: '2025-05-24T09:00:00Z'
        }
      ];

      const mockSegments: ContactSegment[] = [
        {
          id: 'new-customers',
          name: 'New Customers',
          description: 'Customers who joined in the last 30 days',
          criteria: [
            { field: 'createdAt', operator: 'greater_than', value: '2025-04-26' }
          ],
          contactCount: 245,
          createdAt: '2025-05-01T10:00:00Z',
          updatedAt: '2025-05-01T10:00:00Z'
        },
        {
          id: 'vip-customers',
          name: 'VIP Customers',
          description: 'High-value customers with premium status',
          criteria: [
            { field: 'segments', operator: 'in', value: ['vip', 'premium'] }
          ],
          contactCount: 156,
          createdAt: '2025-05-01T10:00:00Z',
          updatedAt: '2025-05-01T10:00:00Z'
        },
        {
          id: 'active-customers',
          name: 'Active Customers',
          description: 'Customers with recent activity',
          criteria: [
            { field: 'lastPurchaseDate', operator: 'greater_than', value: '2025-04-01' }
          ],
          contactCount: 1823,
          createdAt: '2025-05-01T10:00:00Z',
          updatedAt: '2025-05-01T10:00:00Z'
        }
      ];

      setEmailTemplates(mockEmailTemplates);
      setSmsTemplates(mockSmsTemplates);
      setSegments(mockSegments);
    } catch (error) {
      console.error('Failed to load templates and segments:', error);
    }
  };

  const handleTemplateSelect = (templateId: string) => {
    setSelectedTemplate(templateId);
    
    if (campaignType === 'email') {
      const template = emailTemplates.find(t => t.id === templateId);
      if (template) {
        setSubject(template.subject);
        setContent(template.content);
      }
    } else {
      const template = smsTemplates.find(t => t.id === templateId);
      if (template) {
        setContent(template.content);
      }
    }
  };

  const handleSegmentToggle = (segmentId: string) => {
    setSelectedSegments(prev => 
      prev.includes(segmentId) 
        ? prev.filter(id => id !== segmentId)
        : [...prev, segmentId]
    );
  };

  const handleSave = async () => {
    if (!campaignName.trim()) {
      alert('Please enter a campaign name');
      return;
    }

    if (!content.trim()) {
      alert('Please enter campaign content');
      return;
    }

    if (selectedSegments.length === 0) {
      alert('Please select at least one target segment');
      return;
    }

    setLoading(true);
    try {
      const campaignData = {
        name: campaignName,
        type: campaignType,
        subject: campaignType === 'email' ? subject : undefined,
        content,
        templateId: selectedTemplate,
        targetSegments: selectedSegments,
        scheduledAt: isScheduled ? scheduledAt : undefined,
        status: 'draft'
      };

      if (campaignType === 'email') {
        await CustomerCommunicationService.createEmailCampaign(
          campaignName,
          selectedTemplate,
          selectedSegments,
          isScheduled ? scheduledAt : undefined
        );
      } else {
        await CustomerCommunicationService.createSMSCampaign(
          campaignName,
          selectedTemplate,
          selectedSegments,
          isScheduled ? scheduledAt : undefined
        );
      }

      onSave?.(campaignData);
    } catch (error) {
      console.error('Failed to save campaign:', error);
      alert('Failed to save campaign. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const handleSend = async () => {
    if (!campaignName.trim() || !content.trim() || selectedSegments.length === 0) {
      alert('Please complete all required fields before sending');
      return;
    }

    const confirmed = globalThis.confirm(
      `Are you sure you want to send this ${campaignType} campaign to ${selectedSegments.length} segment(s)?`
    );

    if (!confirmed) return;

    setLoading(true);
    try {
      // Mock sending logic
      const campaignData = {
        name: campaignName,
        type: campaignType,
        subject: campaignType === 'email' ? subject : undefined,
        content,
        templateId: selectedTemplate,
        targetSegments: selectedSegments,
        status: 'sending'
      };

      onSend?.(campaignData);
    } catch (error) {
      console.error('Failed to send campaign:', error);
      alert('Failed to send campaign. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const getPreviewContent = () => {
    const mockVariables = {
      customer_name: 'John Doe',
      company_name: 'Your Company',
      product_name: 'Premium Service',
      discount: '25',
      promo_code: 'SAVE25',
      expiry: 'midnight',
      link: 'https://example.com',
      feature_list: '• Enhanced dashboard\n• Real-time analytics\n• Mobile app',
      update_link: 'https://example.com/updates'
    };

    return CustomerCommunicationService.processTemplate(content, mockVariables);
  };

  const totalContacts = selectedSegments.reduce((total, segmentId) => {
    const segment = segments.find(s => s.id === segmentId);
    return total + (segment?.contactCount || 0);
  }, 0);

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Button variant="ghost" onClick={onBack}>
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back
          </Button>
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Campaign Composer</h1>
            <p className="text-gray-600">Create and send email or SMS campaigns</p>
          </div>
        </div>
        <div className="flex gap-2">
          <Button variant="outline" onClick={() => setPreviewMode(!previewMode)}>
            <Eye className="h-4 w-4 mr-2" />
            {previewMode ? 'Edit' : 'Preview'}
          </Button>
          <Button variant="outline" onClick={handleSave} disabled={loading}>
            <Save className="h-4 w-4 mr-2" />
            Save Draft
          </Button>
          <Button onClick={handleSend} disabled={loading}>
            <Send className="h-4 w-4 mr-2" />
            Send Campaign
          </Button>
        </div>
      </div>

      <div className="grid gap-6 lg:grid-cols-3">
        {/* Main Content */}
        <div className="lg:col-span-2 space-y-6">
          {previewMode ? (
            <Card>
              <CardHeader>
                <CardTitle>Campaign Preview</CardTitle>
                <CardDescription>How your campaign will appear to recipients</CardDescription>
              </CardHeader>
              <CardContent>
                {campaignType === 'email' && subject && (
                  <div className="mb-4">
                    <Label className="text-sm font-medium">Subject Line</Label>
                    <div className="mt-1 p-3 bg-gray-50 rounded border">
                      {CustomerCommunicationService.processTemplate(subject, {
                        customer_name: 'John Doe',
                        company_name: 'Your Company',
                        product_name: 'Premium Service'
                      })}
                    </div>
                  </div>
                )}
                <div>
                  <Label className="text-sm font-medium">Content</Label>
                  <div className="mt-1 p-4 bg-gray-50 rounded border min-h-[200px]">
                    {campaignType === 'email' ? (
                      <div dangerouslySetInnerHTML={{ __html: getPreviewContent() }} />
                    ) : (
                      <div className="whitespace-pre-wrap">{getPreviewContent()}</div>
                    )}
                  </div>
                </div>
              </CardContent>
            </Card>
          ) : (
            <>
              {/* Campaign Type and Basic Info */}
              <Card>
                <CardHeader>
                  <CardTitle>Campaign Details</CardTitle>
                  <CardDescription>Configure your campaign settings</CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div>
                    <Label htmlFor="campaign-name">Campaign Name</Label>
                    <Input
                      id="campaign-name"
                      value={campaignName}
                      onChange={(e) => setCampaignName(e.target.value)}
                      placeholder="Enter campaign name"
                    />
                  </div>

                  <div>
                    <Label>Campaign Type</Label>
                    <Tabs value={campaignType} onValueChange={(value) => setCampaignType(value as 'email' | 'sms')}>
                      <TabsList className="grid w-full grid-cols-2">
                        <TabsTrigger value="email" className="flex items-center gap-2">
                          <Mail className="h-4 w-4" />
                          Email
                        </TabsTrigger>
                        <TabsTrigger value="sms" className="flex items-center gap-2">
                          <MessageSquare className="h-4 w-4" />
                          SMS
                        </TabsTrigger>
                      </TabsList>
                    </Tabs>
                  </div>

                  {campaignType === 'email' && (
                    <div>
                      <Label htmlFor="subject">Subject Line</Label>
                      <Input
                        id="subject"
                        value={subject}
                        onChange={(e) => setSubject(e.target.value)}
                        placeholder="Enter email subject"
                      />
                    </div>
                  )}
                </CardContent>
              </Card>

              {/* Template Selection */}
              <Card>
                <CardHeader>
                  <CardTitle>Template Selection</CardTitle>
                  <CardDescription>Choose a template or create from scratch</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <Select value={selectedTemplate} onValueChange={handleTemplateSelect}>
                      <SelectTrigger>
                        <SelectValue placeholder="Select a template (optional)" />
                      </SelectTrigger>
                      <SelectContent>
                        {(campaignType === 'email' ? emailTemplates : smsTemplates).map((template) => (
                          <SelectItem key={template.id} value={template.id}>
                            {template.name}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>

                    <div>
                      <Label htmlFor="content">Content</Label>
                      <Textarea
                        id="content"
                        value={content}
                        onChange={(e) => setContent(e.target.value)}
                        placeholder={campaignType === 'email' ? 'Enter email content (HTML supported)' : 'Enter SMS content'}
                        rows={campaignType === 'email' ? 10 : 4}
                        className="font-mono"
                      />
                      <p className="text-xs text-gray-500 mt-1">
                        Use variables like &#123;&#123;customer_name&#125;&#125; for personalization
                      </p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </>
          )}
        </div>

        {/* Sidebar */}
        <div className="space-y-6">
          {/* Target Audience */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Target className="h-5 w-5" />
                Target Audience
              </CardTitle>
              <CardDescription>Select customer segments</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {segments.map((segment) => (
                  <div
                    key={segment.id}
                    className={`p-3 border rounded cursor-pointer transition-colors ${
                      selectedSegments.includes(segment.id)
                        ? 'border-blue-500 bg-blue-50'
                        : 'border-gray-200 hover:border-gray-300'
                    }`}
                    onClick={() => handleSegmentToggle(segment.id)}
                  >
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="font-medium text-sm">{segment.name}</p>
                        <p className="text-xs text-gray-600">{segment.description}</p>
                      </div>
                      <Badge variant="secondary">
                        {segment.contactCount.toLocaleString()}
                      </Badge>
                    </div>
                  </div>
                ))}
              </div>
              
              {selectedSegments.length > 0 && (
                <div className="mt-4 p-3 bg-blue-50 rounded">
                  <p className="text-sm font-medium text-blue-900">
                    Total Recipients: {totalContacts.toLocaleString()}
                  </p>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Scheduling */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Calendar className="h-5 w-5" />
                Scheduling
              </CardTitle>
              <CardDescription>When to send this campaign</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center space-x-2">
                <input
                  type="checkbox"
                  id="schedule-later"
                  checked={isScheduled}
                  onChange={(e) => setIsScheduled(e.target.checked)}
                  className="rounded"
                />
                <Label htmlFor="schedule-later">Schedule for later</Label>
              </div>

              {isScheduled && (
                <div>
                  <Label htmlFor="scheduled-at">Send Date & Time</Label>
                  <Input
                    id="scheduled-at"
                    type="datetime-local"
                    value={scheduledAt}
                    onChange={(e) => setScheduledAt(e.target.value)}
                    min={new Date().toISOString().slice(0, 16)}
                  />
                </div>
              )}

              {!isScheduled && (
                <div className="flex items-center gap-2 text-sm text-gray-600">
                  <Clock className="h-4 w-4" />
                  Will send immediately
                </div>
              )}
            </CardContent>
          </Card>

          {/* Campaign Summary */}
          <Card>
            <CardHeader>
              <CardTitle>Campaign Summary</CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <div className="flex justify-between text-sm">
                <span className="text-gray-600">Type:</span>
                <span className="font-medium">{campaignType.toUpperCase()}</span>
              </div>
              <div className="flex justify-between text-sm">
                <span className="text-gray-600">Recipients:</span>
                <span className="font-medium">{totalContacts.toLocaleString()}</span>
              </div>
              <div className="flex justify-between text-sm">
                <span className="text-gray-600">Segments:</span>
                <span className="font-medium">{selectedSegments.length}</span>
              </div>
              <div className="flex justify-between text-sm">
                <span className="text-gray-600">Status:</span>
                <span className="font-medium">
                  {isScheduled ? 'Scheduled' : 'Ready to Send'}
                </span>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
};

export default CampaignComposer;
