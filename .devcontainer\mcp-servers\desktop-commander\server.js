#!/usr/bin/env node
const { Server } = require('@modelcontextprotocol/sdk/server/index.js');
const { StdioServerTransport } = require('@modelcontextprotocol/sdk/server/stdio.js');
const { CallToolRequestSchema, ListToolsRequestSchema } = require('@modelcontextprotocol/sdk/types.js');
const express = require('express');
const { exec } = require('child_process');

class DesktopCommanderServer {
  constructor() {
    this.server = new Server({ name: 'desktop-commander-server', version: '1.0.0' }, { capabilities: { tools: {} } });
    this.port = process.env.MCP_PORT || 8085;
    this.setupToolHandlers();
    this.setupHealthCheck();
  }

  setupToolHandlers() {
    this.server.setRequestHandler(ListToolsRequestSchema, async () => ({
      tools: [
        {
          name: 'execute_command',
          description: 'Execute system commands safely',
          inputSchema: {
            type: 'object',
            properties: {
              command: { type: 'string', description: 'Command to execute' },
              safe_mode: { type: 'boolean', description: 'Enable safe mode', default: true }
            },
            required: ['command']
          }
        }
      ]
    }));

    this.server.setRequestHandler(CallToolRequestSchema, async (request) => {
      const { name, arguments: args } = request.params;
      if (name === 'execute_command') {
        return new Promise((resolve, reject) => {
          const safeCommands = ['ls', 'pwd', 'date', 'whoami', 'echo'];
          const cmd = args.command.split(' ')[0];
          
          if (args.safe_mode && !safeCommands.includes(cmd)) {
            reject(new Error(`Command '${cmd}' not allowed in safe mode`));
            return;
          }

          exec(args.command, (error, stdout, stderr) => {
            resolve({
              content: [{
                type: 'text',
                text: JSON.stringify({ stdout, stderr, error: error?.message }, null, 2)
              }]
            });
          });
        });
      }
      throw new Error(`Unknown tool: ${name}`);
    });
  }

  setupHealthCheck() {
    const app = express();
    app.get('/health', (req, res) => {
      res.json({ status: 'healthy', service: 'desktop-commander-mcp', timestamp: new Date().toISOString() });
    });
    app.listen(this.port, () => console.log(`Desktop Commander MCP Server health check running on port ${this.port}`));
  }

  async run() {
    const transport = new StdioServerTransport();
    await this.server.connect(transport);
    console.error('Desktop Commander MCP server running on stdio');
  }
}

const server = new DesktopCommanderServer();
server.run().catch(console.error);
