import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
import { useEffect, useRef, useState } from 'react';
export const AdvancedChart = ({ type, data, config, width = 800, height = 400, interactive = true, onDataPointClick, onZoom }) => {
    const chartRef = useRef(null);
    const [isLoading, setIsLoading] = useState(true);
    const [error, setError] = useState(null);
    useEffect(() => {
        if (!chartRef.current || !data.length)
            return;
        const renderChart = async () => {
            try {
                setIsLoading(true);
                setError(null);
                // This would integrate with a charting library like D3.js, Chart.js, or Plotly
                // For now, we'll create a simple SVG-based chart
                await renderSVGChart();
                setIsLoading(false);
            }
            catch (err) {
                setError(err instanceof Error ? err.message : 'Failed to render chart');
                setIsLoading(false);
            }
        };
        renderChart();
    }, [type, data, config, width, height]);
    const renderSVGChart = async () => {
        if (!chartRef.current)
            return;
        const svg = document.createElementNS('http://www.w3.org/2000/svg', 'svg');
        svg.setAttribute('width', width.toString());
        svg.setAttribute('height', height.toString());
        svg.setAttribute('viewBox', `0 0 ${width} ${height}`);
        // Clear previous content
        chartRef.current.innerHTML = '';
        chartRef.current.appendChild(svg);
        switch (type) {
            case 'line':
                renderLineChart(svg);
                break;
            case 'bar':
                renderBarChart(svg);
                break;
            case 'pie':
                renderPieChart(svg);
                break;
            case 'heatmap':
                renderHeatmap(svg);
                break;
            case 'treemap':
                renderTreemap(svg);
                break;
            case 'sankey':
                renderSankeyDiagram(svg);
                break;
            default:
                renderLineChart(svg);
        }
    };
    const renderLineChart = (svg) => {
        const margin = { top: 20, right: 30, bottom: 40, left: 50 };
        const chartWidth = width - margin.left - margin.right;
        const chartHeight = height - margin.top - margin.bottom;
        // Create scales
        const xScale = (index) => (index / (data.length - 1)) * chartWidth;
        const yScale = (value) => {
            const maxValue = Math.max(...data.map(d => d.value));
            return chartHeight - (value / maxValue) * chartHeight;
        };
        // Create line path
        const pathData = data.map((d, i) => {
            const x = xScale(i) + margin.left;
            const y = yScale(d.value) + margin.top;
            return `${i === 0 ? 'M' : 'L'} ${x} ${y}`;
        }).join(' ');
        const path = document.createElementNS('http://www.w3.org/2000/svg', 'path');
        path.setAttribute('d', pathData);
        path.setAttribute('stroke', config.colors?.[0] || '#3b82f6');
        path.setAttribute('stroke-width', '2');
        path.setAttribute('fill', 'none');
        svg.appendChild(path);
        // Add data points
        data.forEach((d, i) => {
            const circle = document.createElementNS('http://www.w3.org/2000/svg', 'circle');
            circle.setAttribute('cx', (xScale(i) + margin.left).toString());
            circle.setAttribute('cy', (yScale(d.value) + margin.top).toString());
            circle.setAttribute('r', '4');
            circle.setAttribute('fill', config.colors?.[0] || '#3b82f6');
            if (interactive && onDataPointClick) {
                circle.style.cursor = 'pointer';
                circle.addEventListener('click', () => onDataPointClick(d));
            }
            svg.appendChild(circle);
        });
        // Add axes
        addAxes(svg, margin, chartWidth, chartHeight);
    };
    const renderBarChart = (svg) => {
        const margin = { top: 20, right: 30, bottom: 40, left: 50 };
        const chartWidth = width - margin.left - margin.right;
        const chartHeight = height - margin.top - margin.bottom;
        const barWidth = chartWidth / data.length * 0.8;
        const maxValue = Math.max(...data.map(d => d.value));
        data.forEach((d, i) => {
            const barHeight = (d.value / maxValue) * chartHeight;
            const x = (i * chartWidth / data.length) + margin.left + (chartWidth / data.length - barWidth) / 2;
            const y = margin.top + chartHeight - barHeight;
            const rect = document.createElementNS('http://www.w3.org/2000/svg', 'rect');
            rect.setAttribute('x', x.toString());
            rect.setAttribute('y', y.toString());
            rect.setAttribute('width', barWidth.toString());
            rect.setAttribute('height', barHeight.toString());
            rect.setAttribute('fill', config.colors?.[i % (config.colors?.length || 1)] || '#3b82f6');
            if (interactive && onDataPointClick) {
                rect.style.cursor = 'pointer';
                rect.addEventListener('click', () => onDataPointClick(d));
            }
            svg.appendChild(rect);
        });
        addAxes(svg, margin, chartWidth, chartHeight);
    };
    const renderPieChart = (svg) => {
        const centerX = width / 2;
        const centerY = height / 2;
        const radius = Math.min(width, height) / 2 - 20;
        const total = data.reduce((sum, d) => sum + d.value, 0);
        let currentAngle = 0;
        data.forEach((d, i) => {
            const sliceAngle = (d.value / total) * 2 * Math.PI;
            const startAngle = currentAngle;
            const endAngle = currentAngle + sliceAngle;
            const x1 = centerX + radius * Math.cos(startAngle);
            const y1 = centerY + radius * Math.sin(startAngle);
            const x2 = centerX + radius * Math.cos(endAngle);
            const y2 = centerY + radius * Math.sin(endAngle);
            const largeArcFlag = sliceAngle > Math.PI ? 1 : 0;
            const pathData = [
                `M ${centerX} ${centerY}`,
                `L ${x1} ${y1}`,
                `A ${radius} ${radius} 0 ${largeArcFlag} 1 ${x2} ${y2}`,
                'Z'
            ].join(' ');
            const path = document.createElementNS('http://www.w3.org/2000/svg', 'path');
            path.setAttribute('d', pathData);
            path.setAttribute('fill', config.colors?.[i % (config.colors?.length || 1)] || `hsl(${i * 360 / data.length}, 70%, 50%)`);
            path.setAttribute('stroke', '#fff');
            path.setAttribute('stroke-width', '2');
            if (interactive && onDataPointClick) {
                path.style.cursor = 'pointer';
                path.addEventListener('click', () => onDataPointClick(d));
            }
            svg.appendChild(path);
            currentAngle += sliceAngle;
        });
    };
    const renderHeatmap = (svg) => {
        // Simplified heatmap implementation
        const cellSize = 20;
        const cols = Math.floor(width / cellSize);
        const rows = Math.floor(height / cellSize);
        for (let i = 0; i < Math.min(data.length, cols * rows); i++) {
            const row = Math.floor(i / cols);
            const col = i % cols;
            const value = data[i]?.value || 0;
            const intensity = Math.min(value / 100, 1); // Normalize to 0-1
            const rect = document.createElementNS('http://www.w3.org/2000/svg', 'rect');
            rect.setAttribute('x', (col * cellSize).toString());
            rect.setAttribute('y', (row * cellSize).toString());
            rect.setAttribute('width', cellSize.toString());
            rect.setAttribute('height', cellSize.toString());
            rect.setAttribute('fill', `rgba(59, 130, 246, ${intensity})`);
            rect.setAttribute('stroke', '#fff');
            rect.setAttribute('stroke-width', '1');
            svg.appendChild(rect);
        }
    };
    const renderTreemap = (svg) => {
        // Simplified treemap implementation
        const total = data.reduce((sum, d) => sum + d.value, 0);
        let currentX = 0;
        let currentY = 0;
        const rowHeight = height / Math.ceil(Math.sqrt(data.length));
        data.forEach((d, i) => {
            const rectWidth = (d.value / total) * width;
            if (currentX + rectWidth > width) {
                currentX = 0;
                currentY += rowHeight;
            }
            const rect = document.createElementNS('http://www.w3.org/2000/svg', 'rect');
            rect.setAttribute('x', currentX.toString());
            rect.setAttribute('y', currentY.toString());
            rect.setAttribute('width', rectWidth.toString());
            rect.setAttribute('height', rowHeight.toString());
            rect.setAttribute('fill', config.colors?.[i % (config.colors?.length || 1)] || `hsl(${i * 360 / data.length}, 70%, 50%)`);
            rect.setAttribute('stroke', '#fff');
            rect.setAttribute('stroke-width', '2');
            svg.appendChild(rect);
            currentX += rectWidth;
        });
    };
    const renderSankeyDiagram = (svg) => {
        // Simplified Sankey diagram - would need more complex implementation
        const nodeHeight = 20;
        const nodeSpacing = 40;
        data.forEach((d, i) => {
            const y = i * (nodeHeight + nodeSpacing);
            const rect = document.createElementNS('http://www.w3.org/2000/svg', 'rect');
            rect.setAttribute('x', '50');
            rect.setAttribute('y', y.toString());
            rect.setAttribute('width', '100');
            rect.setAttribute('height', nodeHeight.toString());
            rect.setAttribute('fill', config.colors?.[i % (config.colors?.length || 1)] || '#3b82f6');
            svg.appendChild(rect);
        });
    };
    const addAxes = (svg, margin, chartWidth, chartHeight) => {
        // X-axis
        const xAxis = document.createElementNS('http://www.w3.org/2000/svg', 'line');
        xAxis.setAttribute('x1', margin.left.toString());
        xAxis.setAttribute('y1', (margin.top + chartHeight).toString());
        xAxis.setAttribute('x2', (margin.left + chartWidth).toString());
        xAxis.setAttribute('y2', (margin.top + chartHeight).toString());
        xAxis.setAttribute('stroke', '#666');
        svg.appendChild(xAxis);
        // Y-axis
        const yAxis = document.createElementNS('http://www.w3.org/2000/svg', 'line');
        yAxis.setAttribute('x1', margin.left.toString());
        yAxis.setAttribute('y1', margin.top.toString());
        yAxis.setAttribute('x2', margin.left.toString());
        yAxis.setAttribute('y2', (margin.top + chartHeight).toString());
        yAxis.setAttribute('stroke', '#666');
        svg.appendChild(yAxis);
    };
    if (isLoading) {
        return (_jsx("div", { className: "flex items-center justify-center", style: { width, height }, children: _jsx("div", { className: "animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600" }) }));
    }
    if (error) {
        return (_jsx("div", { className: "flex items-center justify-center bg-red-50 border border-red-200 rounded-lg", style: { width, height }, children: _jsxs("div", { className: "text-red-600 text-center", children: [_jsx("p", { className: "font-medium", children: "Chart Error" }), _jsx("p", { className: "text-sm", children: error })] }) }));
    }
    return (_jsxs("div", { className: "relative", children: [_jsx("div", { ref: chartRef, className: "border rounded-lg bg-white" }), interactive && (_jsxs("div", { className: "absolute top-2 right-2 flex space-x-2", children: [_jsx("button", { className: "px-2 py-1 text-xs bg-gray-100 hover:bg-gray-200 rounded", onClick: () => { }, children: "Export" }), _jsx("button", { className: "px-2 py-1 text-xs bg-gray-100 hover:bg-gray-200 rounded", onClick: () => { }, children: "Fullscreen" })] }))] }));
};
export default AdvancedChart;
