import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
import { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Textarea } from '@/components/ui/textarea';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger, } from '@/components/ui/dialog';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue, } from '@/components/ui/select';
import { Label } from '@/components/ui/label';
import { Plus, Search, Edit, Check, X, Eye } from 'lucide-react';
export const JournalEntries = () => {
    const [searchTerm, setSearchTerm] = useState('');
    const [selectedStatus, setSelectedStatus] = useState('ALL');
    const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
    const [viewingEntry, setViewingEntry] = useState(null);
    // Mock data for demonstration
    const mockAccounts = [
        { id: '1', code: '1000', name: 'Cash', type: 'ASSET' },
        { id: '2', code: '1200', name: 'Accounts Receivable', type: 'ASSET' },
        { id: '3', code: '2000', name: 'Accounts Payable', type: 'LIABILITY' },
        { id: '4', code: '4000', name: 'Sales Revenue', type: 'REVENUE' },
        { id: '5', code: '5000', name: 'Office Expenses', type: 'EXPENSE' }
    ];
    const mockEntries = [
        {
            id: '1',
            entryNumber: 'JE-2024-001',
            date: new Date('2024-01-15'),
            description: 'Initial cash investment',
            reference: 'INV-001',
            status: 'POSTED',
            createdBy: 'admin',
            totalAmount: 50000,
            lines: [
                {
                    id: '1',
                    accountId: '1',
                    debit: 50000,
                    credit: 0,
                    description: 'Cash received from investor'
                },
                {
                    id: '2',
                    accountId: '4',
                    debit: 0,
                    credit: 50000,
                    description: 'Owner equity contribution'
                }
            ]
        },
        {
            id: '2',
            entryNumber: 'JE-2024-002',
            date: new Date('2024-01-20'),
            description: 'Office supplies purchase',
            reference: 'PO-001',
            status: 'APPROVED',
            createdBy: 'admin',
            totalAmount: 1500,
            lines: [
                {
                    id: '3',
                    accountId: '5',
                    debit: 1500,
                    credit: 0,
                    description: 'Office supplies expense'
                },
                {
                    id: '4',
                    accountId: '1',
                    debit: 0,
                    credit: 1500,
                    description: 'Cash payment for supplies'
                }
            ]
        },
        {
            id: '3',
            entryNumber: 'JE-2024-003',
            date: new Date('2024-01-25'),
            description: 'Service revenue earned',
            reference: 'INV-002',
            status: 'DRAFT',
            createdBy: 'admin',
            totalAmount: 5000,
            lines: [
                {
                    id: '5',
                    accountId: '2',
                    debit: 5000,
                    credit: 0,
                    description: 'Accounts receivable for services'
                },
                {
                    id: '6',
                    accountId: '4',
                    debit: 0,
                    credit: 5000,
                    description: 'Service revenue earned'
                }
            ]
        }
    ];
    const [newEntry, setNewEntry] = useState({
        entryNumber: '',
        date: new Date().toISOString().split('T')[0],
        description: '',
        reference: '',
        status: 'DRAFT',
        lines: [],
        createdBy: 'current-user'
    });
    const [newLine, setNewLine] = useState({
        accountId: '',
        debit: 0,
        credit: 0,
        description: ''
    });
    const handleCreateEntry = () => {
        if (newEntry.lines.length === 0) {
            alert('Please add at least one journal line');
            return;
        }
        const totalDebits = newEntry.lines.reduce((sum, line) => sum + line.debit, 0);
        const totalCredits = newEntry.lines.reduce((sum, line) => sum + line.credit, 0);
        if (Math.abs(totalDebits - totalCredits) > 0.01) {
            alert('Journal entry must be balanced (debits must equal credits)');
            return;
        }
        setIsCreateDialogOpen(false);
        resetNewEntry();
        alert('Journal entry created successfully!');
    };
    const resetNewEntry = () => {
        setNewEntry({
            entryNumber: '',
            date: new Date().toISOString().split('T')[0],
            description: '',
            reference: '',
            status: 'DRAFT',
            lines: [],
            createdBy: 'current-user'
        });
        setNewLine({
            accountId: '',
            debit: 0,
            credit: 0,
            description: ''
        });
    };
    const addLineToEntry = () => {
        if (!newLine.accountId) {
            alert('Please select an account');
            return;
        }
        if (newLine.debit === 0 && newLine.credit === 0) {
            alert('Please enter either a debit or credit amount');
            return;
        }
        if (newLine.debit > 0 && newLine.credit > 0) {
            alert('A line cannot have both debit and credit amounts');
            return;
        }
        const line = {
            id: `temp-${Date.now()}`,
            accountId: newLine.accountId,
            debit: newLine.debit,
            credit: newLine.credit,
            description: newLine.description
        };
        setNewEntry({
            ...newEntry,
            lines: [...newEntry.lines, line]
        });
        setNewLine({
            accountId: '',
            debit: 0,
            credit: 0,
            description: ''
        });
    };
    const removeLineFromEntry = (index) => {
        const updatedLines = newEntry.lines.filter((_, i) => i !== index);
        setNewEntry({
            ...newEntry,
            lines: updatedLines
        });
    };
    const getStatusColor = (status) => {
        switch (status) {
            case 'DRAFT':
                return 'bg-gray-100 text-gray-800';
            case 'PENDING_APPROVAL':
                return 'bg-yellow-100 text-yellow-800';
            case 'APPROVED':
                return 'bg-blue-100 text-blue-800';
            case 'POSTED':
                return 'bg-green-100 text-green-800';
            case 'REVERSED':
                return 'bg-red-100 text-red-800';
            default:
                return 'bg-gray-100 text-gray-800';
        }
    };
    const formatCurrency = (amount) => {
        return new Intl.NumberFormat('en-US', {
            style: 'currency',
            currency: 'USD'
        }).format(amount);
    };
    const formatDate = (date) => {
        return new Date(date).toLocaleDateString();
    };
    const getAccountName = (accountId) => {
        const account = mockAccounts.find(a => a.id === accountId);
        return account ? `${account.code} - ${account.name}` : 'Unknown Account';
    };
    const filteredEntries = mockEntries.filter(entry => {
        const matchesSearch = entry.entryNumber.toLowerCase().includes(searchTerm.toLowerCase()) ||
            entry.description.toLowerCase().includes(searchTerm.toLowerCase());
        const matchesStatus = selectedStatus === 'ALL' || entry.status === selectedStatus;
        return matchesSearch && matchesStatus;
    });
    const calculateTotals = (lines) => {
        const totalDebits = lines.reduce((sum, line) => sum + line.debit, 0);
        const totalCredits = lines.reduce((sum, line) => sum + line.credit, 0);
        return { totalDebits, totalCredits };
    };
    return (_jsxs("div", { className: "space-y-6", children: [_jsxs("div", { className: "flex justify-between items-center", children: [_jsxs("div", { children: [_jsx("h1", { className: "text-3xl font-bold tracking-tight", children: "Journal Entries" }), _jsx("p", { className: "text-muted-foreground", children: "Manage general ledger transactions and postings" })] }), _jsxs(Dialog, { open: isCreateDialogOpen, onOpenChange: setIsCreateDialogOpen, children: [_jsx(DialogTrigger, { asChild: true, children: _jsxs(Button, { children: [_jsx(Plus, { className: "h-4 w-4 mr-2" }), "New Entry"] }) }), _jsxs(DialogContent, { className: "max-w-4xl", children: [_jsxs(DialogHeader, { children: [_jsx(DialogTitle, { children: "Create Journal Entry" }), _jsx(DialogDescription, { children: "Create a new journal entry with balanced debits and credits." })] }), _jsxs("div", { className: "space-y-6", children: [_jsxs("div", { className: "grid grid-cols-2 gap-4", children: [_jsxs("div", { children: [_jsx(Label, { htmlFor: "entry-number", children: "Entry Number" }), _jsx(Input, { id: "entry-number", value: newEntry.entryNumber, onChange: (e) => setNewEntry({ ...newEntry, entryNumber: e.target.value }), placeholder: "Auto-generated if empty" })] }), _jsxs("div", { children: [_jsx(Label, { htmlFor: "entry-date", children: "Date" }), _jsx(Input, { id: "entry-date", type: "date", value: newEntry.date, onChange: (e) => setNewEntry({ ...newEntry, date: e.target.value }) })] })] }), _jsxs("div", { children: [_jsx(Label, { htmlFor: "description", children: "Description" }), _jsx(Textarea, { id: "description", value: newEntry.description, onChange: (e) => setNewEntry({ ...newEntry, description: e.target.value }), placeholder: "Enter journal entry description" })] }), _jsxs("div", { children: [_jsx(Label, { htmlFor: "reference", children: "Reference" }), _jsx(Input, { id: "reference", value: newEntry.reference, onChange: (e) => setNewEntry({ ...newEntry, reference: e.target.value }), placeholder: "Optional reference number" })] }), _jsxs(Card, { children: [_jsx(CardHeader, { children: _jsx(CardTitle, { children: "Add Journal Line" }) }), _jsx(CardContent, { children: _jsxs("div", { className: "grid grid-cols-5 gap-4", children: [_jsxs("div", { children: [_jsx(Label, { children: "Account" }), _jsxs(Select, { value: newLine.accountId, onValueChange: (value) => setNewLine({ ...newLine, accountId: value }), children: [_jsx(SelectTrigger, { children: _jsx(SelectValue, { placeholder: "Select account" }) }), _jsx(SelectContent, { children: mockAccounts.map(account => (_jsxs(SelectItem, { value: account.id, children: [account.code, " - ", account.name] }, account.id))) })] })] }), _jsxs("div", { children: [_jsx(Label, { children: "Debit" }), _jsx(Input, { type: "number", step: "0.01", value: newLine.debit || '', onChange: (e) => setNewLine({ ...newLine, debit: parseFloat(e.target.value) || 0 }), placeholder: "0.00" })] }), _jsxs("div", { children: [_jsx(Label, { children: "Credit" }), _jsx(Input, { type: "number", step: "0.01", value: newLine.credit || '', onChange: (e) => setNewLine({ ...newLine, credit: parseFloat(e.target.value) || 0 }), placeholder: "0.00" })] }), _jsxs("div", { children: [_jsx(Label, { children: "Description" }), _jsx(Input, { value: newLine.description, onChange: (e) => setNewLine({ ...newLine, description: e.target.value }), placeholder: "Line description" })] }), _jsx("div", { className: "flex items-end", children: _jsxs(Button, { onClick: addLineToEntry, className: "w-full", children: [_jsx(Plus, { className: "h-4 w-4 mr-2" }), "Add"] }) })] }) })] }), newEntry.lines.length > 0 && (_jsxs(Card, { children: [_jsx(CardHeader, { children: _jsx(CardTitle, { children: "Journal Lines" }) }), _jsx(CardContent, { children: _jsxs(Table, { children: [_jsx(TableHeader, { children: _jsxs(TableRow, { children: [_jsx(TableHead, { children: "Account" }), _jsx(TableHead, { children: "Description" }), _jsx(TableHead, { className: "text-right", children: "Debit" }), _jsx(TableHead, { className: "text-right", children: "Credit" }), _jsx(TableHead, {})] }) }), _jsxs(TableBody, { children: [newEntry.lines.map((line, index) => (_jsxs(TableRow, { children: [_jsx(TableCell, { children: getAccountName(line.accountId) }), _jsx(TableCell, { children: line.description }), _jsx(TableCell, { className: "text-right", children: line.debit > 0 ? formatCurrency(line.debit) : '-' }), _jsx(TableCell, { className: "text-right", children: line.credit > 0 ? formatCurrency(line.credit) : '-' }), _jsx(TableCell, { children: _jsx(Button, { variant: "ghost", size: "sm", onClick: () => removeLineFromEntry(index), children: _jsx(X, { className: "h-4 w-4" }) }) })] }, index))), _jsxs(TableRow, { className: "font-bold", children: [_jsx(TableCell, { colSpan: 2, children: "Totals" }), _jsx(TableCell, { className: "text-right", children: formatCurrency(calculateTotals(newEntry.lines).totalDebits) }), _jsx(TableCell, { className: "text-right", children: formatCurrency(calculateTotals(newEntry.lines).totalCredits) }), _jsx(TableCell, {})] })] })] }) })] }))] }), _jsxs(DialogFooter, { children: [_jsx(Button, { variant: "outline", onClick: () => setIsCreateDialogOpen(false), children: "Cancel" }), _jsx(Button, { onClick: handleCreateEntry, children: "Create Entry" })] })] })] })] }), _jsxs(Card, { children: [_jsx(CardHeader, { children: _jsx(CardTitle, { children: "Filters" }) }), _jsx(CardContent, { children: _jsxs("div", { className: "flex space-x-4", children: [_jsx("div", { className: "flex-1", children: _jsxs("div", { className: "relative", children: [_jsx(Search, { className: "absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" }), _jsx(Input, { placeholder: "Search entries...", value: searchTerm, onChange: (e) => setSearchTerm(e.target.value), className: "pl-8" })] }) }), _jsxs(Select, { value: selectedStatus, onValueChange: setSelectedStatus, children: [_jsx(SelectTrigger, { className: "w-[180px]", children: _jsx(SelectValue, { placeholder: "Status" }) }), _jsxs(SelectContent, { children: [_jsx(SelectItem, { value: "ALL", children: "All Status" }), _jsx(SelectItem, { value: "DRAFT", children: "Draft" }), _jsx(SelectItem, { value: "PENDING_APPROVAL", children: "Pending Approval" }), _jsx(SelectItem, { value: "APPROVED", children: "Approved" }), _jsx(SelectItem, { value: "POSTED", children: "Posted" }), _jsx(SelectItem, { value: "REVERSED", children: "Reversed" })] })] })] }) })] }), _jsxs(Card, { children: [_jsxs(CardHeader, { children: [_jsx(CardTitle, { children: "Journal Entries" }), _jsxs(CardDescription, { children: [filteredEntries.length, " entries found"] })] }), _jsx(CardContent, { children: _jsxs(Table, { children: [_jsx(TableHeader, { children: _jsxs(TableRow, { children: [_jsx(TableHead, { children: "Entry #" }), _jsx(TableHead, { children: "Date" }), _jsx(TableHead, { children: "Description" }), _jsx(TableHead, { children: "Reference" }), _jsx(TableHead, { children: "Status" }), _jsx(TableHead, { className: "text-right", children: "Amount" }), _jsx(TableHead, { className: "text-right", children: "Actions" })] }) }), _jsx(TableBody, { children: filteredEntries.map((entry) => (_jsxs(TableRow, { children: [_jsx(TableCell, { className: "font-medium", children: entry.entryNumber }), _jsx(TableCell, { children: formatDate(entry.date) }), _jsx(TableCell, { children: entry.description }), _jsx(TableCell, { children: entry.reference }), _jsx(TableCell, { children: _jsx(Badge, { className: getStatusColor(entry.status), children: entry.status }) }), _jsx(TableCell, { className: "text-right font-medium", children: formatCurrency(entry.totalAmount) }), _jsx(TableCell, { className: "text-right", children: _jsxs("div", { className: "flex justify-end space-x-2", children: [_jsx(Button, { variant: "ghost", size: "sm", onClick: () => setViewingEntry(entry), children: _jsx(Eye, { className: "h-4 w-4" }) }), _jsx(Button, { variant: "ghost", size: "sm", children: _jsx(Edit, { className: "h-4 w-4" }) }), entry.status === 'DRAFT' && (_jsx(Button, { variant: "ghost", size: "sm", children: _jsx(Check, { className: "h-4 w-4" }) }))] }) })] }, entry.id))) })] }) })] }), _jsx(Dialog, { open: !!viewingEntry, onOpenChange: () => setViewingEntry(null), children: _jsxs(DialogContent, { className: "max-w-4xl", children: [_jsxs(DialogHeader, { children: [_jsx(DialogTitle, { children: "Journal Entry Details" }), _jsxs(DialogDescription, { children: [viewingEntry?.entryNumber, " - ", viewingEntry?.description] })] }), viewingEntry && (_jsxs("div", { className: "space-y-4", children: [_jsxs("div", { className: "grid grid-cols-2 gap-4", children: [_jsxs("div", { children: [_jsx(Label, { children: "Entry Number" }), _jsx("p", { className: "font-medium", children: viewingEntry.entryNumber })] }), _jsxs("div", { children: [_jsx(Label, { children: "Date" }), _jsx("p", { className: "font-medium", children: formatDate(viewingEntry.date) })] }), _jsxs("div", { children: [_jsx(Label, { children: "Reference" }), _jsx("p", { className: "font-medium", children: viewingEntry.reference || 'N/A' })] }), _jsxs("div", { children: [_jsx(Label, { children: "Status" }), _jsx(Badge, { className: getStatusColor(viewingEntry.status), children: viewingEntry.status })] })] }), _jsxs("div", { children: [_jsx(Label, { children: "Description" }), _jsx("p", { className: "font-medium", children: viewingEntry.description })] }), _jsxs(Table, { children: [_jsx(TableHeader, { children: _jsxs(TableRow, { children: [_jsx(TableHead, { children: "Account" }), _jsx(TableHead, { children: "Description" }), _jsx(TableHead, { className: "text-right", children: "Debit" }), _jsx(TableHead, { className: "text-right", children: "Credit" })] }) }), _jsxs(TableBody, { children: [viewingEntry.lines.map((line, index) => (_jsxs(TableRow, { children: [_jsx(TableCell, { children: getAccountName(line.accountId) }), _jsx(TableCell, { children: line.description }), _jsx(TableCell, { className: "text-right", children: line.debit > 0 ? formatCurrency(line.debit) : '-' }), _jsx(TableCell, { className: "text-right", children: line.credit > 0 ? formatCurrency(line.credit) : '-' })] }, index))), _jsxs(TableRow, { className: "font-bold", children: [_jsx(TableCell, { colSpan: 2, children: "Totals" }), _jsx(TableCell, { className: "text-right", children: formatCurrency(calculateTotals(viewingEntry.lines).totalDebits) }), _jsx(TableCell, { className: "text-right", children: formatCurrency(calculateTotals(viewingEntry.lines).totalCredits) })] })] })] })] }))] }) })] }));
};
export default JournalEntries;
