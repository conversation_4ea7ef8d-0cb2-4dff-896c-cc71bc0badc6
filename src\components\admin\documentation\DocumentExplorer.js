import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
import { useState } from 'react';
import { Folder, File, ChevronRight, ChevronDown, MoreHorizontal, Edit, Trash2, Copy } from 'lucide-react';
import { cn } from '@/lib/utils';
import { ScrollArea } from '@/components/ui/scroll-area';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@/components/ui/dropdown-menu';
import { toast } from 'sonner';
export const DocumentExplorer = ({ categories, onSelectDocument, selectedDocument, onDeleteDocument }) => {
    const [expandedCategories, setExpandedCategories] = useState([categories[0]?.id || '']);
    const toggleCategory = (categoryId) => {
        setExpandedCategories(prev => prev.includes(categoryId)
            ? prev.filter(id => id !== categoryId)
            : [...prev, categoryId]);
    };
    const handleEditDocument = (document) => {
        // This would be implemented with an edit dialog in a real app
        toast.info(`Editing document: ${document.title}`);
    };
    const handleDeleteDocument = (document) => {
        if (onDeleteDocument) {
            if (globalThis.confirm(`Are you sure you want to delete "${document.title}"?`)) {
                onDeleteDocument(document.id);
                toast.success(`Document "${document.title}" deleted`);
            }
        }
    };
    const handleDuplicateDocument = (document) => {
        // This would be implemented with document duplication in a real app
        toast.info(`Duplicating document: ${document.title}`);
    };
    return (_jsx(ScrollArea, { className: "h-[calc(100vh-16rem)] pr-4", children: _jsx("div", { className: "space-y-1 p-2", children: categories.map((category) => (_jsxs("div", { className: "select-none", children: [_jsxs("button", { onClick: () => toggleCategory(category.id), className: "flex items-center w-full py-2 px-2 text-sm font-medium rounded-md hover:bg-gray-100 dark:hover:bg-gray-800", children: [expandedCategories.includes(category.id)
                                ? _jsx(ChevronDown, { className: "h-4 w-4 mr-1 text-gray-500" })
                                : _jsx(ChevronRight, { className: "h-4 w-4 mr-1 text-gray-500" }), _jsx(Folder, { className: "h-4 w-4 mr-2 text-blue-500" }), _jsx("span", { className: "flex-1 text-left", children: category.name }), _jsx("span", { className: "text-xs text-gray-400", children: category.documents?.length || 0 })] }), expandedCategories.includes(category.id) && (_jsx("div", { className: "ml-6 mt-1 space-y-1", children: !category.documents || category.documents.length === 0 ? (_jsx("div", { className: "px-2 py-1.5 text-sm text-gray-400 italic", children: "No documents" })) : (category.documents.map((document) => (_jsxs("div", { className: cn("flex items-center w-full py-1.5 px-2 text-sm rounded-md group", selectedDocument?.id === document.id
                                ? "bg-blue-100 dark:bg-blue-900 text-blue-700 dark:text-blue-300"
                                : "hover:bg-gray-100 dark:hover:bg-gray-800"), children: [_jsxs("button", { onClick: () => onSelectDocument(document), className: "flex items-center flex-1 overflow-hidden", children: [_jsx(File, { className: "h-4 w-4 mr-2 text-gray-500 flex-shrink-0" }), _jsx("span", { className: "truncate", children: document.title })] }), _jsxs(DropdownMenu, { children: [_jsx(DropdownMenuTrigger, { asChild: true, children: _jsx("button", { className: "h-6 w-6 rounded-full hover:bg-white/20 dark:hover:bg-black/20 flex items-center justify-center opacity-0 group-hover:opacity-100 focus:opacity-100 transition-opacity", children: _jsx(MoreHorizontal, { className: "h-3.5 w-3.5" }) }) }), _jsxs(DropdownMenuContent, { align: "end", children: [_jsxs(DropdownMenuItem, { onClick: () => handleEditDocument(document), children: [_jsx(Edit, { className: "h-4 w-4 mr-2" }), "Edit"] }), _jsxs(DropdownMenuItem, { onClick: () => handleDuplicateDocument(document), children: [_jsx(Copy, { className: "h-4 w-4 mr-2" }), "Duplicate"] }), _jsxs(DropdownMenuItem, { onClick: () => handleDeleteDocument(document), className: "text-red-600", children: [_jsx(Trash2, { className: "h-4 w-4 mr-2" }), "Delete"] })] })] })] }, document.id)))) }))] }, category.id))) }) }));
};
