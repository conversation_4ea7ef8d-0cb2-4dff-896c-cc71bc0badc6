import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
import { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow, } from "@/components/ui/table";
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuLabel, DropdownMenuSeparator, DropdownMenuTrigger, } from "@/components/ui/dropdown-menu";
import { Badge } from "@/components/ui/badge";
import { MoreHorizontal, Edit, Trash2, FileText } from "lucide-react";
import { Button } from '@/components/ui/button';
// Mock customer data
const mockCustomers = [
    {
        id: "1",
        code: "ACME001",
        name: "Acme Corporation",
        contact_name: "<PERSON>",
        email: "<EMAIL>",
        status: "active",
        account_type: "enterprise"
    },
    {
        id: "2",
        code: "<PERSON>L<PERSON>BEX002",
        name: "Globex Industries",
        contact_name: "<PERSON>",
        email: "<EMAIL>",
        status: "active",
        account_type: "premium"
    },
    {
        id: "3",
        code: "INITECH003",
        name: "Initech LLC",
        contact_name: "<PERSON>",
        email: "<EMAIL>",
        status: "inactive",
        account_type: "standard"
    }
];
export function CustomersTable() {
    const navigate = useNavigate();
    const [customers] = useState(mockCustomers);
    const handleDelete = (id, name) => {
        if (globalThis.confirm(`Are you sure you want to delete customer "${name}"?`)) {
            console.log(`Deleting customer ${id}`);
            // In a real implementation, this would delete the customer
        }
    };
    return (_jsx("div", { className: "rounded-md border", children: _jsxs(Table, { children: [_jsx(TableHeader, { children: _jsxs(TableRow, { children: [_jsx(TableHead, { children: "Customer Code" }), _jsx(TableHead, { children: "Name" }), _jsx(TableHead, { children: "Contact" }), _jsx(TableHead, { children: "Email" }), _jsx(TableHead, { children: "Account Type" }), _jsx(TableHead, { children: "Status" }), _jsx(TableHead, { className: "w-[100px]", children: "Actions" })] }) }), _jsx(TableBody, { children: customers.length === 0 ? (_jsx(TableRow, { children: _jsx(TableCell, { colSpan: 7, className: "text-center h-32 text-muted-foreground", children: "No customers found" }) })) : (customers.map((customer) => (_jsxs(TableRow, { children: [_jsx(TableCell, { className: "font-medium", children: customer.code }), _jsx(TableCell, { children: customer.name }), _jsx(TableCell, { children: customer.contact_name || "—" }), _jsx(TableCell, { children: customer.email || "—" }), _jsx(TableCell, { children: _jsx(Badge, { variant: "outline", children: customer.account_type.charAt(0).toUpperCase() + customer.account_type.slice(1) }) }), _jsx(TableCell, { children: _jsx(Badge, { variant: customer.status === "active" ? "default" : "secondary", children: customer.status === "active" ? "Active" : "Inactive" }) }), _jsx(TableCell, { children: _jsxs(DropdownMenu, { children: [_jsx(DropdownMenuTrigger, { asChild: true, children: _jsx(Button, { variant: "ghost", className: "h-8 w-8 p-0", children: _jsx(MoreHorizontal, { className: "h-4 w-4" }) }) }), _jsxs(DropdownMenuContent, { align: "end", children: [_jsx(DropdownMenuLabel, { children: "Actions" }), _jsxs(DropdownMenuItem, { onClick: () => navigate(`/customer-management/${customer.id}`), children: [_jsx(Edit, { className: "h-4 w-4 mr-2" }), "Edit Customer"] }), _jsxs(DropdownMenuItem, { onClick: () => navigate(`/customer-management/${customer.id}/analytics`), children: [_jsx(FileText, { className: "h-4 w-4 mr-2" }), "View Analytics"] }), _jsx(DropdownMenuSeparator, {}), _jsxs(DropdownMenuItem, { className: "text-red-600", onClick: () => handleDelete(customer.id, customer.name), children: [_jsx(Trash2, { className: "h-4 w-4 mr-2" }), "Delete"] })] })] }) })] }, customer.id)))) })] }) }));
}
