#!/usr/bin/env node

const { Server } = require('@modelcontextprotocol/sdk/server/index.js');
const { StdioServerTransport } = require('@modelcontextprotocol/sdk/server/stdio.js');
const { CallToolRequestSchema, ListToolsRequestSchema } = require('@modelcontextprotocol/sdk/types.js');
const axios = require('axios');
const express = require('express');

class TavilySearchServer {
  constructor() {
    this.server = new Server(
      {
        name: 'tavily-search-server',
        version: '1.0.0',
      },
      {
        capabilities: {
          tools: {},
        },
      }
    );

    this.apiKey = process.env.TAVILY_API_KEY;
    this.port = process.env.MCP_PORT || 8081;
    
    this.setupToolHandlers();
    this.setupHealthCheck();
  }

  setupToolHandlers() {
    this.server.setRequestHandler(ListToolsRequestSchema, async () => {
      return {
        tools: [
          {
            name: 'tavily_search',
            description: 'Search the web using Tavily Search API with AI-powered results',
            inputSchema: {
              type: 'object',
              properties: {
                query: {
                  type: 'string',
                  description: 'Search query',
                },
                search_depth: {
                  type: 'string',
                  description: 'Search depth (basic or advanced)',
                  enum: ['basic', 'advanced'],
                  default: 'basic',
                },
                include_answer: {
                  type: 'boolean',
                  description: 'Include AI-generated answer',
                  default: true,
                },
                include_raw_content: {
                  type: 'boolean',
                  description: 'Include raw content from sources',
                  default: false,
                },
                max_results: {
                  type: 'number',
                  description: 'Maximum number of results (default: 5)',
                  default: 5,
                  minimum: 1,
                  maximum: 20,
                },
                include_images: {
                  type: 'boolean',
                  description: 'Include images in results',
                  default: false,
                },
                include_domains: {
                  type: 'array',
                  description: 'List of domains to include in search',
                  items: {
                    type: 'string',
                  },
                },
                exclude_domains: {
                  type: 'array',
                  description: 'List of domains to exclude from search',
                  items: {
                    type: 'string',
                  },
                },
              },
              required: ['query'],
            },
          },
          {
            name: 'tavily_extract',
            description: 'Extract content from a specific URL using Tavily',
            inputSchema: {
              type: 'object',
              properties: {
                urls: {
                  type: 'array',
                  description: 'URLs to extract content from',
                  items: {
                    type: 'string',
                  },
                },
              },
              required: ['urls'],
            },
          },
        ],
      };
    });

    this.server.setRequestHandler(CallToolRequestSchema, async (request) => {
      const { name, arguments: args } = request.params;

      if (name === 'tavily_search') {
        return await this.handleTavilySearch(args);
      } else if (name === 'tavily_extract') {
        return await this.handleTavilyExtract(args);
      }

      throw new Error(`Unknown tool: ${name}`);
    });
  }

  async handleTavilySearch(args) {
    if (!this.apiKey) {
      throw new Error('TAVILY_API_KEY environment variable is required');
    }

    try {
      const payload = {
        api_key: this.apiKey,
        query: args.query,
        search_depth: args.search_depth || 'basic',
        include_answer: args.include_answer !== false,
        include_raw_content: args.include_raw_content || false,
        max_results: args.max_results || 5,
        include_images: args.include_images || false,
      };

      if (args.include_domains) {
        payload.include_domains = args.include_domains;
      }

      if (args.exclude_domains) {
        payload.exclude_domains = args.exclude_domains;
      }

      const response = await axios.post('https://api.tavily.com/search', payload, {
        headers: {
          'Content-Type': 'application/json',
        },
      });

      const results = response.data;
      
      return {
        content: [
          {
            type: 'text',
            text: JSON.stringify({
              query: args.query,
              answer: results.answer || null,
              results: results.results || [],
              images: results.images || [],
              follow_up_questions: results.follow_up_questions || [],
              response_time: results.response_time || null,
            }, null, 2),
          },
        ],
      };
    } catch (error) {
      throw new Error(`Tavily Search API error: ${error.message}`);
    }
  }

  async handleTavilyExtract(args) {
    if (!this.apiKey) {
      throw new Error('TAVILY_API_KEY environment variable is required');
    }

    try {
      const payload = {
        api_key: this.apiKey,
        urls: args.urls,
      };

      const response = await axios.post('https://api.tavily.com/extract', payload, {
        headers: {
          'Content-Type': 'application/json',
        },
      });

      const results = response.data;
      
      return {
        content: [
          {
            type: 'text',
            text: JSON.stringify({
              results: results.results || [],
              failed_urls: results.failed_urls || [],
            }, null, 2),
          },
        ],
      };
    } catch (error) {
      throw new Error(`Tavily Extract API error: ${error.message}`);
    }
  }

  setupHealthCheck() {
    const app = express();
    
    app.get('/health', (req, res) => {
      res.json({ 
        status: 'healthy', 
        service: 'tavily-mcp',
        timestamp: new Date().toISOString(),
        api_key_configured: !!this.apiKey
      });
    });

    app.listen(this.port, () => {
      console.log(`Tavily MCP Server health check running on port ${this.port}`);
    });
  }

  async run() {
    const transport = new StdioServerTransport();
    await this.server.connect(transport);
    console.error('Tavily MCP server running on stdio');
  }
}

const server = new TavilySearchServer();
server.run().catch(console.error);
