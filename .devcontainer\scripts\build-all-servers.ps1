# PowerShell script to build all MCP servers

Write-Host "🏗️  Building all MCP servers..."

# Change to the .devcontainer directory
Set-Location -Path ".devcontainer"

# Create remaining server directories first
Write-Host "📁 Creating remaining server directories..."
& scripts/create-remaining-servers.ps1

# List of servers
$servers = @(
    "brave-search",
    "tavily",
    "firecrawl",
    "context7",
    "notion",
    "desktop-commander",
    "taskmaster",
    "supabase",
    "browser-tools",
    "magic",
    "neo4j"
)

Write-Host "🔨 Building MCP server Docker images..."

foreach ($server in $servers) {
    Write-Host "Building $server..."
    if (Test-Path "mcp-servers/$server") {
        docker build -t "mcp-$server:latest" "mcp-servers/$server/"
        Write-Host "✅ Built mcp-$server:latest"
    } else {
        Write-Host "⚠️  Directory mcp-servers/$server not found, skipping..."
    }
}

Write-Host "🎉 All MCP servers built successfully!"
Write-Host ""
Write-Host "📋 Available MCP servers:"
foreach ($server in $servers) {
    Write-Host "  - mcp-$server (port 808*)"
}

Write-Host ""
Write-Host "🚀 To start all servers, run:"
Write-Host "  docker-compose up -d"
Write-Host ""
Write-Host "🏥 To check server health, run:"
Write-Host "  node ../mcp-config/health-check.js"
