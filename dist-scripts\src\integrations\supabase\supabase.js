"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.SupabaseError = void 0;
exports.executeSupabaseOperationArray = executeSupabaseOperationArray;
exports.handleSupabaseError = handleSupabaseError;
async function executeSupabaseOperationArray(operation, operationName) {
    try {
        const { data, error } = await operation();
        if (error) {
            // Check if this is a "table does not exist" error
            if (error.code === '42P01' || (error.message && error.message.includes('does not exist'))) {
                console.warn(`${operationName} failed: Table or view does not exist - database schema may need updating`);
                return []; // Return empty array instead of throwing an error
            }
            handleSupabaseError(error, operationName);
        }
        return data || [];
    }
    catch (error) {
        // Also catch any other errors related to missing tables that might slip through
        if (error instanceof Error &&
            (error.message.includes('does not exist') ||
                error.message.includes('42P01'))) {
            console.warn(`${operationName} failed: Table or view does not exist - database schema may need updating`);
            return []; // Return empty array instead of throwing
        }
        if (error instanceof SupabaseError) {
            throw error;
        }
        // Handle unexpected errors
        console.error(`Unexpected error in ${operationName}:`, error);
        throw new SupabaseError(`${operationName} failed with unexpected error: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
}
// Add SupabaseError class for error handling
class SupabaseError extends Error {
    constructor(message, code, details, hint, statusCode) {
        super(message);
        this.name = 'SupabaseError';
        this.code = code;
        this.details = details;
        this.hint = hint;
        this.statusCode = statusCode;
    }
}
exports.SupabaseError = SupabaseError;
/**
 * Enhanced error handler for Supabase operations
 */
function handleSupabaseError(error, operation) {
    console.error(`Supabase ${operation} error:`, error);
    // Extract error information
    const message = (error === null || error === void 0 ? void 0 : error.message) || 'An unknown error occurred';
    const code = error === null || error === void 0 ? void 0 : error.code;
    const details = error === null || error === void 0 ? void 0 : error.details;
    const hint = error === null || error === void 0 ? void 0 : error.hint;
    const statusCode = error === null || error === void 0 ? void 0 : error.statusCode;
    // Enhanced error categorization
    let enhancedMessage = message;
    if (code === 'PGRST116') {
        enhancedMessage = 'No data found for the requested operation';
    }
    else if (code === '23505') {
        enhancedMessage = 'Duplicate entry - this record already exists';
    }
    else if (code === '23503') {
        enhancedMessage = 'Referenced record not found - check foreign key constraints';
    }
    else if (code === '42P01') {
        enhancedMessage = 'Table or view does not exist - database schema may need updating';
    }
    else if (statusCode === 401) {
        enhancedMessage = 'Authentication required - please log in';
    }
    else if (statusCode === 403) {
        enhancedMessage = 'Access denied - insufficient permissions';
    }
    // Throw standardized error
    throw new SupabaseError(`${operation} failed: ${enhancedMessage}`, code, details, hint, statusCode);
}
