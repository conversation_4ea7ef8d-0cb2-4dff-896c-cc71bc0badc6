import React from 'react';
import { useAuth, Permission } from '@/auth';

export function AdminPanel() {
  const { hasPermission, hasAnyPermission, user } = useAuth();

  // Check for specific permission
  if (!hasPermission(Permission.ADMIN_PANEL)) {
    return <div>Access denied. Administrator privileges required.</div>;
  }

  return (
    <div>
      <h1>Administration Panel</h1>
      
      {/* Conditional rendering based on permissions */}
      {hasPermission(Permission.USER_MANAGEMENT) && (
        <div>User Management Section</div>
      )}
      
      {hasAnyPermission([Permission.FINANCIAL_ADMIN, Permission.FINANCIAL_VIEW]) && (
        <div>Financial Section</div>
      )}
      
      {hasPermission(Permission.SYSTEM_MONITORING) && (
        <div>System Monitoring Section</div>
      )}
    </div>
  );
}
