import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue, } from '@/components/ui/select';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { toast } from 'sonner';
import { Save, Plus, Trash2 } from 'lucide-react';
export const CustomerColumnMappings = () => {
    const [mappings, setMappings] = useState([
        { id: '1', fileColumn: 'Customer Name', systemField: 'name', required: true },
        { id: '2', fileColumn: 'Customer Code', systemField: 'code', required: true },
        { id: '3', fileColumn: 'Contact Person', systemField: 'contact_name', required: false },
        { id: '4', fileColumn: 'Email Address', systemField: 'email', required: false },
        { id: '5', fileColumn: 'Phone Number', systemField: 'phone', required: false },
    ]);
    const systemFields = [
        { value: 'name', label: 'Customer Name' },
        { value: 'code', label: 'Customer Code' },
        { value: 'contact_name', label: 'Contact Person' },
        { value: 'email', label: 'Email Address' },
        { value: 'phone', label: 'Phone Number' },
        { value: 'website', label: 'Website' },
        { value: 'account_type', label: 'Account Type' },
        { value: 'status', label: 'Status' },
    ];
    const addMapping = () => {
        const newId = (mappings.length + 1).toString();
        setMappings([...mappings, { id: newId, fileColumn: '', systemField: '', required: false }]);
    };
    const removeMapping = (id) => {
        setMappings(mappings.filter(mapping => mapping.id !== id));
    };
    const updateMapping = (id, field, value) => {
        setMappings(mappings.map(mapping => mapping.id === id ? { ...mapping, [field]: value } : mapping));
    };
    const handleSave = () => {
        console.log('Saving column mappings:', mappings);
        toast.success('Column mappings saved successfully');
    };
    return (_jsxs("div", { className: "space-y-6", children: [_jsxs("div", { className: "space-y-4", children: [_jsxs("div", { className: "flex justify-between items-center", children: [_jsx("h3", { className: "text-lg font-medium", children: "Column Mappings" }), _jsxs(Button, { onClick: addMapping, size: "sm", variant: "outline", className: "flex items-center gap-1", children: [_jsx(Plus, { className: "h-4 w-4" }), "Add Mapping"] })] }), _jsxs(Table, { children: [_jsx(TableHeader, { children: _jsxs(TableRow, { children: [_jsx(TableHead, { children: "File Column" }), _jsx(TableHead, { children: "System Field" }), _jsx(TableHead, { children: "Required" }), _jsx(TableHead, { className: "w-[100px]" })] }) }), _jsx(TableBody, { children: mappings.map((mapping) => (_jsxs(TableRow, { children: [_jsx(TableCell, { children: _jsx(Input, { value: mapping.fileColumn, onChange: (e) => updateMapping(mapping.id, 'fileColumn', e.target.value), placeholder: "CSV column name" }) }), _jsx(TableCell, { children: _jsxs(Select, { value: mapping.systemField, onValueChange: (value) => updateMapping(mapping.id, 'systemField', value), children: [_jsx(SelectTrigger, { children: _jsx(SelectValue, { placeholder: "Select system field" }) }), _jsx(SelectContent, { children: systemFields.map(field => (_jsx(SelectItem, { value: field.value, children: field.label }, field.value))) })] }) }), _jsx(TableCell, { children: _jsxs(Select, { value: mapping.required.toString(), onValueChange: (value) => updateMapping(mapping.id, 'required', value === 'true'), children: [_jsx(SelectTrigger, { children: _jsx(SelectValue, {}) }), _jsxs(SelectContent, { children: [_jsx(SelectItem, { value: "true", children: "Yes" }), _jsx(SelectItem, { value: "false", children: "No" })] })] }) }), _jsx(TableCell, { children: _jsx(Button, { variant: "ghost", size: "icon", onClick: () => removeMapping(mapping.id), disabled: mapping.required, children: _jsx(Trash2, { className: "h-4 w-4 text-red-500" }) }) })] }, mapping.id))) })] })] }), _jsx("div", { className: "flex justify-end", children: _jsxs(Button, { onClick: handleSave, children: [_jsx(Save, { className: "h-4 w-4 mr-2" }), "Save Mappings"] }) })] }));
};
