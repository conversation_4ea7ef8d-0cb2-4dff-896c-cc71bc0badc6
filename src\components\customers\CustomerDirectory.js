'use client';
import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import { Search, Plus, Phone, Mail, MapPin, Calendar, FileText, Edit, Trash2, Download, Upload, MoreHorizontal } from 'lucide-react';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@/components/ui/dropdown-menu';
import { toast } from 'sonner';
const CustomerDirectory = () => {
    const [customers, setCustomers] = useState([]);
    const [filteredCustomers, setFilteredCustomers] = useState([]);
    const [searchTerm, setSearchTerm] = useState('');
    const [statusFilter, setStatusFilter] = useState('all');
    const [typeFilter, setTypeFilter] = useState('all');
    const [isLoading, setIsLoading] = useState(true);
    const [isDialogOpen, setIsDialogOpen] = useState(false);
    const [editingCustomer, setEditingCustomer] = useState(null);
    const [formData, setFormData] = useState({
        company_name: '',
        contact_person: '',
        email: '',
        phone: '',
        address: '',
        city: '',
        state: '',
        postal_code: '',
        country: 'South Africa',
        customer_type: 'Individual',
        credit_limit: 0,
        payment_terms: '30 days',
        tax_id: '',
        notes: ''
    });
    // Mock data for development - replace with actual API calls
    useEffect(() => {
        const mockCustomers = [
            {
                id: '1',
                customer_number: 'CUST001',
                company_name: 'ABC Construction Ltd',
                contact_person: 'John Smith',
                email: '<EMAIL>',
                phone: '+27 11 123 4567',
                address: '123 Main Street',
                city: 'Johannesburg',
                state: 'Gauteng',
                postal_code: '2000',
                country: 'South Africa',
                customer_type: 'Corporate',
                status: 'Active',
                credit_limit: 50000,
                payment_terms: '30 days',
                tax_id: '1234567890',
                notes: 'Preferred customer with excellent payment history',
                total_rentals: 15,
                total_revenue: 125000,
                last_rental_date: '2024-05-15',
                created_at: '2024-01-15'
            },
            {
                id: '2',
                customer_number: 'CUST002',
                company_name: 'Mike Johnson',
                contact_person: 'Mike Johnson',
                email: '<EMAIL>',
                phone: '+27 82 987 6543',
                address: '456 Oak Avenue',
                city: 'Cape Town',
                state: 'Western Cape',
                postal_code: '8000',
                country: 'South Africa',
                customer_type: 'Individual',
                status: 'Active',
                credit_limit: 10000,
                payment_terms: '14 days',
                tax_id: '',
                notes: 'Regular weekend rentals',
                total_rentals: 8,
                total_revenue: 32000,
                last_rental_date: '2024-05-20',
                created_at: '2024-02-10'
            },
            {
                id: '3',
                customer_number: 'CUST003',
                company_name: 'XYZ Mining Corp',
                contact_person: 'Sarah Williams',
                email: '<EMAIL>',
                phone: '+27 11 555 0123',
                address: '789 Industrial Road',
                city: 'Pretoria',
                state: 'Gauteng',
                postal_code: '0001',
                country: 'South Africa',
                customer_type: 'Corporate',
                status: 'Suspended',
                credit_limit: 100000,
                payment_terms: '60 days',
                tax_id: '9876543210',
                notes: 'Payment issues - suspended pending resolution',
                total_rentals: 25,
                total_revenue: 280000,
                last_rental_date: '2024-04-30',
                created_at: '2023-11-20'
            }
        ];
        setTimeout(() => {
            setCustomers(mockCustomers);
            setFilteredCustomers(mockCustomers);
            setIsLoading(false);
        }, 1000);
    }, []);
    // Filter customers based on search and filters
    useEffect(() => {
        let filtered = customers;
        if (searchTerm) {
            filtered = filtered.filter(customer => customer.company_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                customer.contact_person.toLowerCase().includes(searchTerm.toLowerCase()) ||
                customer.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
                customer.customer_number.toLowerCase().includes(searchTerm.toLowerCase()));
        }
        if (statusFilter !== 'all') {
            filtered = filtered.filter(customer => customer.status === statusFilter);
        }
        if (typeFilter !== 'all') {
            filtered = filtered.filter(customer => customer.customer_type === typeFilter);
        }
        setFilteredCustomers(filtered);
    }, [customers, searchTerm, statusFilter, typeFilter]);
    const handleCreateCustomer = async () => {
        try {
            // TODO: Replace with actual API call
            const newCustomer = {
                id: Date.now().toString(),
                customer_number: `CUST${String(customers.length + 1).padStart(3, '0')}`,
                ...formData,
                status: 'Active',
                total_rentals: 0,
                total_revenue: 0,
                last_rental_date: '',
                created_at: new Date().toISOString()
            };
            setCustomers(prev => [...prev, newCustomer]);
            setIsDialogOpen(false);
            resetForm();
            toast.success('Customer created successfully');
        }
        catch (error) {
            toast.error('Failed to create customer');
        }
    };
    const handleUpdateCustomer = async () => {
        if (!editingCustomer)
            return;
        try {
            // TODO: Replace with actual API call
            const updatedCustomer = { ...editingCustomer, ...formData };
            setCustomers(prev => prev.map(c => c.id === editingCustomer.id ? updatedCustomer : c));
            setIsDialogOpen(false);
            setEditingCustomer(null);
            resetForm();
            toast.success('Customer updated successfully');
        }
        catch (error) {
            toast.error('Failed to update customer');
        }
    };
    const handleDeleteCustomer = async (customerId) => {
        try {
            // TODO: Replace with actual API call
            setCustomers(prev => prev.filter(c => c.id !== customerId));
            toast.success('Customer deleted successfully');
        }
        catch (error) {
            toast.error('Failed to delete customer');
        }
    };
    const resetForm = () => {
        setFormData({
            company_name: '',
            contact_person: '',
            email: '',
            phone: '',
            address: '',
            city: '',
            state: '',
            postal_code: '',
            country: 'South Africa',
            customer_type: 'Individual',
            credit_limit: 0,
            payment_terms: '30 days',
            tax_id: '',
            notes: ''
        });
    };
    const openEditDialog = (customer) => {
        setEditingCustomer(customer);
        setFormData({
            company_name: customer.company_name,
            contact_person: customer.contact_person,
            email: customer.email,
            phone: customer.phone,
            address: customer.address,
            city: customer.city,
            state: customer.state,
            postal_code: customer.postal_code,
            country: customer.country,
            customer_type: customer.customer_type,
            credit_limit: customer.credit_limit,
            payment_terms: customer.payment_terms,
            tax_id: customer.tax_id,
            notes: customer.notes
        });
        setIsDialogOpen(true);
    };
    const getStatusBadgeVariant = (status) => {
        switch (status) {
            case 'Active': return 'default';
            case 'Inactive': return 'secondary';
            case 'Suspended': return 'destructive';
            default: return 'secondary';
        }
    };
    const formatCurrency = (amount) => {
        return new Intl.NumberFormat('en-ZA', {
            style: 'currency',
            currency: 'ZAR'
        }).format(amount);
    };
    if (isLoading) {
        return (_jsx("div", { className: "flex items-center justify-center h-64", children: _jsx("div", { className: "animate-spin rounded-full h-8 w-8 border-b-2 border-primary" }) }));
    }
    return (_jsxs("div", { className: "space-y-6", children: [_jsxs("div", { className: "flex justify-between items-center", children: [_jsxs("div", { children: [_jsx("h1", { className: "text-3xl font-bold tracking-tight", children: "Customer Directory" }), _jsx("p", { className: "text-muted-foreground", children: "Manage your customer database and relationships" })] }), _jsxs("div", { className: "flex gap-2", children: [_jsxs(Button, { variant: "outline", size: "sm", children: [_jsx(Download, { className: "h-4 w-4 mr-2" }), "Export"] }), _jsxs(Button, { variant: "outline", size: "sm", children: [_jsx(Upload, { className: "h-4 w-4 mr-2" }), "Import"] }), _jsxs(Dialog, { open: isDialogOpen, onOpenChange: setIsDialogOpen, children: [_jsx(DialogTrigger, { asChild: true, children: _jsxs(Button, { onClick: () => { setEditingCustomer(null); resetForm(); }, children: [_jsx(Plus, { className: "h-4 w-4 mr-2" }), "Add Customer"] }) }), _jsxs(DialogContent, { className: "max-w-2xl max-h-[90vh] overflow-y-auto", children: [_jsxs(DialogHeader, { children: [_jsx(DialogTitle, { children: editingCustomer ? 'Edit Customer' : 'Add New Customer' }), _jsx(DialogDescription, { children: editingCustomer
                                                            ? 'Update customer information and settings.'
                                                            : 'Create a new customer profile with contact and billing details.' })] }), _jsx("div", { className: "grid gap-4 py-4", children: _jsxs(Tabs, { defaultValue: "basic", className: "w-full", children: [_jsxs(TabsList, { className: "grid w-full grid-cols-3", children: [_jsx(TabsTrigger, { value: "basic", children: "Basic Info" }), _jsx(TabsTrigger, { value: "address", children: "Address" }), _jsx(TabsTrigger, { value: "billing", children: "Billing" })] }), _jsxs(TabsContent, { value: "basic", className: "space-y-4", children: [_jsxs("div", { className: "grid grid-cols-2 gap-4", children: [_jsxs("div", { className: "space-y-2", children: [_jsx(Label, { htmlFor: "company_name", children: "Company/Name *" }), _jsx(Input, { id: "company_name", value: formData.company_name, onChange: (e) => setFormData(prev => ({ ...prev, company_name: e.target.value })), placeholder: "Enter company or individual name" })] }), _jsxs("div", { className: "space-y-2", children: [_jsx(Label, { htmlFor: "contact_person", children: "Contact Person" }), _jsx(Input, { id: "contact_person", value: formData.contact_person, onChange: (e) => setFormData(prev => ({ ...prev, contact_person: e.target.value })), placeholder: "Primary contact person" })] })] }), _jsxs("div", { className: "grid grid-cols-2 gap-4", children: [_jsxs("div", { className: "space-y-2", children: [_jsx(Label, { htmlFor: "email", children: "Email *" }), _jsx(Input, { id: "email", type: "email", value: formData.email, onChange: (e) => setFormData(prev => ({ ...prev, email: e.target.value })), placeholder: "<EMAIL>" })] }), _jsxs("div", { className: "space-y-2", children: [_jsx(Label, { htmlFor: "phone", children: "Phone" }), _jsx(Input, { id: "phone", value: formData.phone, onChange: (e) => setFormData(prev => ({ ...prev, phone: e.target.value })), placeholder: "+27 11 123 4567" })] })] }), _jsxs("div", { className: "space-y-2", children: [_jsx(Label, { htmlFor: "customer_type", children: "Customer Type" }), _jsxs(Select, { value: formData.customer_type, onValueChange: (value) => setFormData(prev => ({ ...prev, customer_type: value })), children: [_jsx(SelectTrigger, { children: _jsx(SelectValue, {}) }), _jsxs(SelectContent, { children: [_jsx(SelectItem, { value: "Individual", children: "Individual" }), _jsx(SelectItem, { value: "Corporate", children: "Corporate" })] })] })] })] }), _jsxs(TabsContent, { value: "address", className: "space-y-4", children: [_jsxs("div", { className: "space-y-2", children: [_jsx(Label, { htmlFor: "address", children: "Street Address" }), _jsx(Input, { id: "address", value: formData.address, onChange: (e) => setFormData(prev => ({ ...prev, address: e.target.value })), placeholder: "123 Main Street" })] }), _jsxs("div", { className: "grid grid-cols-2 gap-4", children: [_jsxs("div", { className: "space-y-2", children: [_jsx(Label, { htmlFor: "city", children: "City" }), _jsx(Input, { id: "city", value: formData.city, onChange: (e) => setFormData(prev => ({ ...prev, city: e.target.value })), placeholder: "Johannesburg" })] }), _jsxs("div", { className: "space-y-2", children: [_jsx(Label, { htmlFor: "state", children: "State/Province" }), _jsx(Input, { id: "state", value: formData.state, onChange: (e) => setFormData(prev => ({ ...prev, state: e.target.value })), placeholder: "Gauteng" })] })] }), _jsxs("div", { className: "grid grid-cols-2 gap-4", children: [_jsxs("div", { className: "space-y-2", children: [_jsx(Label, { htmlFor: "postal_code", children: "Postal Code" }), _jsx(Input, { id: "postal_code", value: formData.postal_code, onChange: (e) => setFormData(prev => ({ ...prev, postal_code: e.target.value })), placeholder: "2000" })] }), _jsxs("div", { className: "space-y-2", children: [_jsx(Label, { htmlFor: "country", children: "Country" }), _jsx(Input, { id: "country", value: formData.country, onChange: (e) => setFormData(prev => ({ ...prev, country: e.target.value })), placeholder: "South Africa" })] })] })] }), _jsxs(TabsContent, { value: "billing", className: "space-y-4", children: [_jsxs("div", { className: "grid grid-cols-2 gap-4", children: [_jsxs("div", { className: "space-y-2", children: [_jsx(Label, { htmlFor: "credit_limit", children: "Credit Limit (ZAR)" }), _jsx(Input, { id: "credit_limit", type: "number", value: formData.credit_limit, onChange: (e) => setFormData(prev => ({ ...prev, credit_limit: Number(e.target.value) })), placeholder: "0" })] }), _jsxs("div", { className: "space-y-2", children: [_jsx(Label, { htmlFor: "payment_terms", children: "Payment Terms" }), _jsxs(Select, { value: formData.payment_terms, onValueChange: (value) => setFormData(prev => ({ ...prev, payment_terms: value })), children: [_jsx(SelectTrigger, { children: _jsx(SelectValue, {}) }), _jsxs(SelectContent, { children: [_jsx(SelectItem, { value: "Cash on delivery", children: "Cash on delivery" }), _jsx(SelectItem, { value: "14 days", children: "14 days" }), _jsx(SelectItem, { value: "30 days", children: "30 days" }), _jsx(SelectItem, { value: "60 days", children: "60 days" }), _jsx(SelectItem, { value: "90 days", children: "90 days" })] })] })] })] }), _jsxs("div", { className: "space-y-2", children: [_jsx(Label, { htmlFor: "tax_id", children: "Tax ID/VAT Number" }), _jsx(Input, { id: "tax_id", value: formData.tax_id, onChange: (e) => setFormData(prev => ({ ...prev, tax_id: e.target.value })), placeholder: "1234567890" })] }), _jsxs("div", { className: "space-y-2", children: [_jsx(Label, { htmlFor: "notes", children: "Notes" }), _jsx(Textarea, { id: "notes", value: formData.notes, onChange: (e) => setFormData(prev => ({ ...prev, notes: e.target.value })), placeholder: "Additional notes about this customer...", rows: 3 })] })] })] }) }), _jsxs("div", { className: "flex justify-end gap-2", children: [_jsx(Button, { variant: "outline", onClick: () => setIsDialogOpen(false), children: "Cancel" }), _jsx(Button, { onClick: editingCustomer ? handleUpdateCustomer : handleCreateCustomer, children: editingCustomer ? 'Update Customer' : 'Create Customer' })] })] })] })] })] }), _jsx(Card, { children: _jsx(CardContent, { className: "pt-6", children: _jsxs("div", { className: "flex flex-col sm:flex-row gap-4", children: [_jsx("div", { className: "flex-1", children: _jsxs("div", { className: "relative", children: [_jsx(Search, { className: "absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" }), _jsx(Input, { placeholder: "Search customers by name, email, or customer number...", value: searchTerm, onChange: (e) => setSearchTerm(e.target.value), className: "pl-10" })] }) }), _jsxs("div", { className: "flex gap-2", children: [_jsxs(Select, { value: statusFilter, onValueChange: setStatusFilter, children: [_jsx(SelectTrigger, { className: "w-32", children: _jsx(SelectValue, { placeholder: "Status" }) }), _jsxs(SelectContent, { children: [_jsx(SelectItem, { value: "all", children: "All Status" }), _jsx(SelectItem, { value: "Active", children: "Active" }), _jsx(SelectItem, { value: "Inactive", children: "Inactive" }), _jsx(SelectItem, { value: "Suspended", children: "Suspended" })] })] }), _jsxs(Select, { value: typeFilter, onValueChange: setTypeFilter, children: [_jsx(SelectTrigger, { className: "w-32", children: _jsx(SelectValue, { placeholder: "Type" }) }), _jsxs(SelectContent, { children: [_jsx(SelectItem, { value: "all", children: "All Types" }), _jsx(SelectItem, { value: "Individual", children: "Individual" }), _jsx(SelectItem, { value: "Corporate", children: "Corporate" })] })] })] })] }) }) }), _jsx("div", { className: "grid gap-6 md:grid-cols-2 lg:grid-cols-3", children: filteredCustomers.map((customer) => (_jsxs(Card, { className: "hover:shadow-md transition-shadow", children: [_jsx(CardHeader, { className: "pb-3", children: _jsxs("div", { className: "flex justify-between items-start", children: [_jsxs("div", { className: "space-y-1", children: [_jsx(CardTitle, { className: "text-lg", children: customer.company_name }), _jsx(CardDescription, { className: "text-sm", children: customer.customer_number })] }), _jsxs("div", { className: "flex items-center gap-2", children: [_jsx(Badge, { variant: getStatusBadgeVariant(customer.status), children: customer.status }), _jsxs(DropdownMenu, { children: [_jsx(DropdownMenuTrigger, { asChild: true, children: _jsx(Button, { variant: "ghost", size: "sm", children: _jsx(MoreHorizontal, { className: "h-4 w-4" }) }) }), _jsxs(DropdownMenuContent, { align: "end", children: [_jsxs(DropdownMenuItem, { onClick: () => openEditDialog(customer), children: [_jsx(Edit, { className: "h-4 w-4 mr-2" }), "Edit"] }), _jsxs(DropdownMenuItem, { onClick: () => handleDeleteCustomer(customer.id), className: "text-destructive", children: [_jsx(Trash2, { className: "h-4 w-4 mr-2" }), "Delete"] })] })] })] })] }) }), _jsxs(CardContent, { className: "space-y-4", children: [_jsxs("div", { className: "space-y-2", children: [_jsxs("div", { className: "flex items-center text-sm text-muted-foreground", children: [_jsx(Mail, { className: "h-4 w-4 mr-2" }), customer.email] }), customer.phone && (_jsxs("div", { className: "flex items-center text-sm text-muted-foreground", children: [_jsx(Phone, { className: "h-4 w-4 mr-2" }), customer.phone] })), _jsxs("div", { className: "flex items-center text-sm text-muted-foreground", children: [_jsx(MapPin, { className: "h-4 w-4 mr-2" }), customer.city, ", ", customer.state] })] }), _jsxs("div", { className: "grid grid-cols-2 gap-4 pt-2 border-t", children: [_jsxs("div", { className: "text-center", children: [_jsx("div", { className: "text-2xl font-bold text-primary", children: customer.total_rentals }), _jsx("div", { className: "text-xs text-muted-foreground", children: "Rentals" })] }), _jsxs("div", { className: "text-center", children: [_jsx("div", { className: "text-2xl font-bold text-green-600", children: formatCurrency(customer.total_revenue) }), _jsx("div", { className: "text-xs text-muted-foreground", children: "Revenue" })] })] }), customer.last_rental_date && (_jsxs("div", { className: "flex items-center text-xs text-muted-foreground pt-2 border-t", children: [_jsx(Calendar, { className: "h-3 w-3 mr-1" }), "Last rental: ", new Date(customer.last_rental_date).toLocaleDateString()] }))] })] }, customer.id))) }), filteredCustomers.length === 0 && (_jsx(Card, { children: _jsx(CardContent, { className: "flex flex-col items-center justify-center py-12", children: _jsxs("div", { className: "text-muted-foreground text-center", children: [_jsx(FileText, { className: "h-12 w-12 mx-auto mb-4 opacity-50" }), _jsx("h3", { className: "text-lg font-medium mb-2", children: "No customers found" }), _jsx("p", { className: "text-sm", children: searchTerm || statusFilter !== 'all' || typeFilter !== 'all'
                                    ? 'Try adjusting your search criteria or filters.'
                                    : 'Get started by adding your first customer.' })] }) }) }))] }));
};
export default CustomerDirectory;
