const fetch = require('node-fetch');
const https = require('https');

// Create agent that accepts self-signed certificates for local development
const agent = new https.Agent({
  rejectUnauthorized: false
});

const BASE_URL = process.env.BETTER_AUTH_URL || 'https://localhost:9000/api/auth';

const ADMIN_EMAIL = "<EMAIL>";
const FIRST_NAME = "Admin";
const LAST_NAME = "Two";
const PASSWORD = "P@ssw0rd123!";

async function createBetterAuthAdmin() {
  try {
    // 1. Register admin user
    console.log("Registering admin user...");
    const registerResp = await fetch(`${BASE_URL}/sign-up/email`, {
      method: "POST",
      headers: { "Content-Type": "application/json" },
      agent: agent,
      body: JSON.stringify({
        email: ADMIN_EMAIL,
        password: PASSWORD,
        firstName: FIRST_NAME,
        lastName: LAST_NAME
      })
    });

    if (!registerResp.ok) {
      const errorText = await registerResp.text();
      console.log(`Registration response: ${errorText}`);
      if (!errorText.includes("already exists")) {
        throw new Error(`Failed to register: ${registerResp.statusText} - ${errorText}`);
      } else {
        console.log("User already exists, proceeding to login...");
      }
    } else {
      console.log("Admin user registered successfully");
    }

    // 2. Login to confirm success
    const loginResp = await fetch(`${BASE_URL}/sign-in/email`, {
      method: "POST",
      headers: { "Content-Type": "application/json" },
      agent: agent,
      body: JSON.stringify({
        email: ADMIN_EMAIL,
        password: PASSWORD,
      })
    });

    if (!loginResp.ok) {
        const errorText = await loginResp.text();
        throw new Error(`Failed to login: ${loginResp.statusText} - ${errorText}`);
    }

    const loginData = await loginResp.json();
    console.log(`Admin login successful. Login result:`, loginData);


    console.log("Better Auth admin user login test complete!");
    console.log("Email:", ADMIN_EMAIL);
    console.log("Password:", PASSWORD);
  } catch (err) {
    console.error("Failed to login Better Auth admin user:", err.message || err);
    process.exit(1);
  }
}

createBetterAuthAdmin();