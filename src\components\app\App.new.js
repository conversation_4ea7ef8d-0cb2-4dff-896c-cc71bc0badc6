import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
import { Suspense } from "react";
import { BrowserRouter, Routes, Route, Navigate } from "react-router-dom";
import { AuthProvider } from "@/auth"; // New consolidated auth
import { ThemeProvider } from "@/context/ThemeContext";
import { ModulesProvider } from "@/context/ModulesContext";
import { Toaster } from "sonner";
import { DashboardLayout } from "@/components/layout/DashboardLayout";
import MasterDash from "@/pages/MasterDash.tsx";
// If the file is named differently or in a different location, update the path accordingly, e.g.:
// import MasterDash from "../pages/MasterDash";
// or
// import MasterDash from "@/pages/MasterDash/index";
import { supabase } from "@/integrations/supabase";
export default function App() {
    return (_jsx(AuthProvider, { supabaseClient: supabase, children: _jsx(ThemeProvider, { children: _jsx(<PERSON><PERSON>lesProvider, { children: _jsx(<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, { children: _jsxs(Suspense, { fallback: _jsx("p", { className: "p-4", "aria-label": "Loading application", children: "Loading\u2026" }), children: [_jsx(Toaster, { position: "top-right", richColors: true, closeButton: true, toastOptions: {
                                    className: "toast"
                                } }), _jsxs(Routes, { children: [_jsx(Route, { path: "/", element: _jsx(Navigate, { to: "/master" }) }), _jsx(Route, { path: "/master", element: _jsx(DashboardLayout, { children: _jsx(MasterDash, {}) }) }), _jsx(Route, { path: "/landing", element: _jsx(Navigate, { to: "/master" }) })] })] }) }) }) }) }));
}
