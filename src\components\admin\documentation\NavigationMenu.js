import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
import React from 'react';
import { NavigationMenu as NavMenu, NavigationMenuContent, NavigationMenuItem, NavigationMenuLink, NavigationMenuList, NavigationMenuTrigger, navigationMenuTriggerStyle } from "@/components/ui/navigation-menu";
import { cn } from '@/lib/utils';
import { FileText, BookOpen, HelpCircle, Settings, FileUp } from 'lucide-react';
export const NavigationMenu = () => {
    return (_jsx(NavMenu, { children: _jsxs(NavigationMenuList, { children: [_jsxs(NavigationMenuItem, { children: [_jsx(NavigationMenuTrigger, { children: "Getting Started" }), _jsx(NavigationMenuContent, { children: _jsxs("ul", { className: "grid gap-3 p-4 w-80 md:w-[400px] lg:w-[500px] lg:grid-cols-2", children: [_jsx(ListItem, { title: "Documentation Overview", href: "#overview", icon: _jsx(FileText, { className: "h-4 w-4 mr-2" }), children: "Introduction to the documentation area" }), _jsx(ListItem, { title: "User Guides", href: "#user-guides", icon: _jsx(BookOpen, { className: "h-4 w-4 mr-2" }), children: "Step-by-step guides for common tasks" }), _jsx(ListItem, { title: "Quick Tips", href: "#quick-tips", icon: _jsx(HelpCircle, { className: "h-4 w-4 mr-2" }), children: "Helpful tips for efficient usage" }), _jsx(ListItem, { title: "Tutorials", href: "#tutorials", icon: _jsx(FileText, { className: "h-4 w-4 mr-2" }), children: "Detailed tutorials for advanced features" })] }) })] }), _jsxs(NavigationMenuItem, { children: [_jsx(NavigationMenuTrigger, { children: "Manage Documents" }), _jsx(NavigationMenuContent, { children: _jsxs("ul", { className: "grid gap-3 p-4 w-80 md:w-[400px]", children: [_jsx(ListItem, { title: "Upload Document", href: "#upload", icon: _jsx(FileUp, { className: "h-4 w-4 mr-2" }), children: "Upload new documentation files" }), _jsx(ListItem, { title: "Organize Categories", href: "#categories", icon: _jsx(Settings, { className: "h-4 w-4 mr-2" }), children: "Manage document categories and structure" }), _jsx(ListItem, { title: "Document Settings", href: "#settings", icon: _jsx(Settings, { className: "h-4 w-4 mr-2" }), children: "Configure documentation preferences" })] }) })] }), _jsx(NavigationMenuItem, { children: _jsx(NavigationMenuLink, { href: "#help", className: navigationMenuTriggerStyle(), children: "Help & Support" }) })] }) }));
};
// Reusable list item component for navigation menu content
const ListItem = React.forwardRef(({ className, title, children, icon, ...props }, ref) => {
    return (_jsx("li", { children: _jsx(NavigationMenuLink, { asChild: true, children: _jsxs("a", { ref: ref, className: cn("block select-none space-y-1 rounded-md p-3 leading-none no-underline outline-none transition-colors hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground", className), ...props, children: [_jsxs("div", { className: "flex items-center text-sm font-medium leading-none", children: [icon, title] }), _jsx("p", { className: "line-clamp-2 text-sm leading-snug text-muted-foreground", children: children })] }) }) }));
});
ListItem.displayName = "ListItem";
