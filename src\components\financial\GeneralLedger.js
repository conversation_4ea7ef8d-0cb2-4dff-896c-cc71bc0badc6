import { jsx as _jsx, jsxs as _jsxs, Fragment as _Fragment } from "react/jsx-runtime";
import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow, } from '@/components/ui/table';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue, } from '@/components/ui/select';
import { Book, Search, Filter, Download, DollarSign, FileText, RefreshCw, AlertCircle, CheckCircle, Eye, Calculator } from 'lucide-react';
import { GeneralLedgerService } from '@/services/financial';
const GeneralLedger = () => {
    const [entries, setEntries] = useState([]);
    const [trialBalance, setTrialBalance] = useState([]);
    const [accountBalances, setAccountBalances] = useState([]);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState(null);
    const [searchTerm, setSearchTerm] = useState('');
    const [selectedAccount, setSelectedAccount] = useState('ALL');
    const [startDate, setStartDate] = useState('');
    const [endDate, setEndDate] = useState('');
    const [activeTab, setActiveTab] = useState('entries');
    useEffect(() => {
        loadGeneralLedgerData();
    }, [selectedAccount, startDate, endDate]);
    const loadGeneralLedgerData = async () => {
        try {
            setLoading(true);
            setError(null);
            await Promise.all([
                loadLedgerEntries(),
                loadTrialBalance(),
                loadAccountBalances()
            ]);
        }
        catch (error) {
            console.error('Error loading general ledger data:', error);
            setError('Failed to load general ledger data. Please try again.');
        }
        finally {
            setLoading(false);
        }
    };
    const loadLedgerEntries = async () => {
        try {
            const data = await GeneralLedgerService.getLedgerEntries('default-org-id', {
                accountId: selectedAccount,
                startDate,
                endDate,
                limit: 1000
            });
            setEntries(data);
        }
        catch (error) {
            console.error('Error loading ledger entries:', error);
            throw error;
        }
    };
    const loadTrialBalance = async () => {
        try {
            const data = await GeneralLedgerService.getTrialBalance('default-org-id', endDate || undefined);
            setTrialBalance(data);
        }
        catch (error) {
            console.error('Error loading trial balance:', error);
            throw error;
        }
    };
    const loadAccountBalances = async () => {
        try {
            const data = await GeneralLedgerService.getAccountBalances('default-org-id', endDate || undefined);
            setAccountBalances(data);
        }
        catch (error) {
            console.error('Error loading account balances:', error);
            throw error;
        }
    };
    // Utility functions
    const formatCurrency = (amount) => {
        return new Intl.NumberFormat('en-US', {
            style: 'currency',
            currency: 'USD'
        }).format(amount);
    };
    const formatDate = (dateString) => {
        return new Date(dateString).toLocaleDateString('en-US', {
            year: 'numeric',
            month: 'short',
            day: 'numeric'
        });
    };
    const getAccountTypeColor = (type) => {
        switch (type) {
            case 'Asset':
                return 'bg-blue-100 text-blue-800';
            case 'Liability':
                return 'bg-red-100 text-red-800';
            case 'Equity':
                return 'bg-purple-100 text-purple-800';
            case 'Revenue':
                return 'bg-green-100 text-green-800';
            case 'Expense':
                return 'bg-orange-100 text-orange-800';
            default:
                return 'bg-gray-100 text-gray-800';
        }
    };
    const exportToCSV = (data, filename) => {
        if (data.length === 0)
            return;
        const headers = Object.keys(data[0]);
        const csvContent = [
            headers.join(','),
            ...data.map(row => headers.map(header => {
                const value = row[header];
                return typeof value === 'string' && value.includes(',')
                    ? `"${value}"`
                    : value;
            }).join(','))
        ].join('\n');
        const blob = new Blob([csvContent], { type: 'text/csv' });
        const url = window.URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.href = url;
        link.download = filename;
        link.click();
        window.URL.revokeObjectURL(url);
    };
    // Filter functions
    const filteredEntries = entries.filter(entry => {
        const matchesSearch = searchTerm === '' ||
            entry.account_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
            entry.account_code.toLowerCase().includes(searchTerm.toLowerCase()) ||
            entry.description.toLowerCase().includes(searchTerm.toLowerCase());
        return matchesSearch;
    });
    const filteredTrialBalance = trialBalance.filter(entry => {
        const matchesSearch = searchTerm === '' ||
            entry.account_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
            entry.account_code.toLowerCase().includes(searchTerm.toLowerCase());
        return matchesSearch;
    });
    const filteredAccountBalances = accountBalances.filter(account => {
        const matchesSearch = searchTerm === '' ||
            account.account_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
            account.account_code.toLowerCase().includes(searchTerm.toLowerCase());
        return matchesSearch;
    });
    // Calculate totals for trial balance
    const trialBalanceTotals = filteredTrialBalance.reduce((totals, entry) => ({
        totalDebits: totals.totalDebits + entry.debit_balance,
        totalCredits: totals.totalCredits + entry.credit_balance
    }), { totalDebits: 0, totalCredits: 0 });
    if (loading) {
        return (_jsx("div", { className: "flex items-center justify-center h-64", children: _jsxs("div", { className: "flex flex-col items-center space-y-4", children: [_jsx("div", { className: "animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600" }), _jsx("p", { className: "text-muted-foreground", children: "Loading general ledger data..." })] }) }));
    }
    if (error) {
        return (_jsx("div", { className: "flex items-center justify-center h-64", children: _jsx(Card, { className: "w-full max-w-md", children: _jsx(CardContent, { className: "pt-6", children: _jsxs("div", { className: "flex flex-col items-center space-y-4", children: [_jsx(AlertCircle, { className: "h-12 w-12 text-red-500" }), _jsxs("div", { className: "text-center", children: [_jsx("h3", { className: "text-lg font-semibold", children: "Error Loading General Ledger" }), _jsx("p", { className: "text-muted-foreground mt-2", children: error })] }), _jsx(Button, { onClick: loadGeneralLedgerData, className: "mt-4", children: "Try Again" })] }) }) }) }));
    }
    return (_jsxs("div", { className: "space-y-6", children: [_jsxs("div", { className: "flex justify-between items-center", children: [_jsxs("div", { children: [_jsxs("h1", { className: "text-3xl font-bold tracking-tight flex items-center", children: [_jsx(Book, { className: "h-8 w-8 mr-3 text-blue-600" }), "General Ledger"] }), _jsx("p", { className: "text-muted-foreground", children: "Complete record of all financial transactions and account balances" })] }), _jsxs("div", { className: "flex space-x-2", children: [_jsxs(Button, { variant: "outline", onClick: loadGeneralLedgerData, children: [_jsx(RefreshCw, { className: "h-4 w-4 mr-2" }), "Refresh Data"] }), _jsxs(Button, { variant: "outline", onClick: () => {
                                    if (activeTab === 'entries') {
                                        exportToCSV(filteredEntries, 'general-ledger-entries.csv');
                                    }
                                    else if (activeTab === 'trial-balance') {
                                        exportToCSV(filteredTrialBalance, 'trial-balance.csv');
                                    }
                                    else {
                                        exportToCSV(filteredAccountBalances, 'account-balances.csv');
                                    }
                                }, children: [_jsx(Download, { className: "h-4 w-4 mr-2" }), "Export CSV"] })] })] }), _jsxs(Card, { children: [_jsx(CardHeader, { children: _jsxs(CardTitle, { className: "flex items-center", children: [_jsx(Filter, { className: "h-5 w-5 mr-2" }), "Filters"] }) }), _jsx(CardContent, { children: _jsxs("div", { className: "grid gap-4 md:grid-cols-4", children: [_jsxs("div", { className: "space-y-2", children: [_jsx(Label, { htmlFor: "search", children: "Search" }), _jsxs("div", { className: "relative", children: [_jsx(Search, { className: "absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" }), _jsx(Input, { id: "search", placeholder: "Search accounts, descriptions...", value: searchTerm, onChange: (e) => setSearchTerm(e.target.value), className: "pl-8" })] })] }), _jsxs("div", { className: "space-y-2", children: [_jsx(Label, { htmlFor: "account", children: "Account" }), _jsxs(Select, { value: selectedAccount, onValueChange: setSelectedAccount, children: [_jsx(SelectTrigger, { children: _jsx(SelectValue, { placeholder: "All Accounts" }) }), _jsxs(SelectContent, { children: [_jsx(SelectItem, { value: "ALL", children: "All Accounts" }), accountBalances.map((account) => (_jsxs(SelectItem, { value: account.account_id, children: [account.account_code, " - ", account.account_name] }, account.account_id)))] })] })] }), _jsxs("div", { className: "space-y-2", children: [_jsx(Label, { htmlFor: "start-date", children: "Start Date" }), _jsx(Input, { id: "start-date", type: "date", value: startDate, onChange: (e) => setStartDate(e.target.value) })] }), _jsxs("div", { className: "space-y-2", children: [_jsx(Label, { htmlFor: "end-date", children: "End Date" }), _jsx(Input, { id: "end-date", type: "date", value: endDate, onChange: (e) => setEndDate(e.target.value) })] })] }) })] }), _jsxs(Tabs, { value: activeTab, onValueChange: setActiveTab, className: "space-y-4", children: [_jsxs(TabsList, { className: "grid w-full grid-cols-3", children: [_jsx(TabsTrigger, { value: "entries", children: "Ledger Entries" }), _jsx(TabsTrigger, { value: "trial-balance", children: "Trial Balance" }), _jsx(TabsTrigger, { value: "balances", children: "Account Balances" })] }), _jsx(TabsContent, { value: "entries", className: "space-y-4", children: _jsxs(Card, { children: [_jsxs(CardHeader, { children: [_jsxs(CardTitle, { className: "flex items-center justify-between", children: [_jsxs("span", { className: "flex items-center", children: [_jsx(FileText, { className: "h-5 w-5 mr-2" }), "General Ledger Entries"] }), _jsxs(Badge, { variant: "secondary", children: [filteredEntries.length, " entries"] })] }), _jsx(CardDescription, { children: "Detailed view of all transactions affecting each account" })] }), _jsx(CardContent, { children: _jsx("div", { className: "rounded-md border", children: _jsxs(Table, { children: [_jsx(TableHeader, { children: _jsxs(TableRow, { children: [_jsx(TableHead, { children: "Date" }), _jsx(TableHead, { children: "Account" }), _jsx(TableHead, { children: "Description" }), _jsx(TableHead, { className: "text-right", children: "Debit" }), _jsx(TableHead, { className: "text-right", children: "Credit" }), _jsx(TableHead, { className: "text-right", children: "Balance" }), _jsx(TableHead, { children: "Reference" })] }) }), _jsx(TableBody, { children: filteredEntries.length === 0 ? (_jsx(TableRow, { children: _jsx(TableCell, { colSpan: 7, className: "text-center py-8", children: _jsxs("div", { className: "flex flex-col items-center space-y-2", children: [_jsx(FileText, { className: "h-8 w-8 text-muted-foreground" }), _jsx("p", { className: "text-muted-foreground", children: "No ledger entries found" }), _jsx("p", { className: "text-sm text-muted-foreground", children: "Try adjusting your filters or date range" })] }) }) })) : (filteredEntries.map((entry) => (_jsxs(TableRow, { children: [_jsx(TableCell, { children: formatDate(entry.transaction_date) }), _jsx(TableCell, { children: _jsxs("div", { className: "flex flex-col", children: [_jsx("span", { className: "font-medium", children: entry.account_code }), _jsx("span", { className: "text-sm text-muted-foreground", children: entry.account_name })] }) }), _jsx(TableCell, { className: "max-w-xs", children: _jsx("div", { className: "truncate", title: entry.description, children: entry.description }) }), _jsx(TableCell, { className: "text-right", children: entry.debit_amount > 0 ? (_jsx("span", { className: "font-medium text-blue-600", children: formatCurrency(entry.debit_amount) })) : (_jsx("span", { className: "text-muted-foreground", children: "\u2014" })) }), _jsx(TableCell, { className: "text-right", children: entry.credit_amount > 0 ? (_jsx("span", { className: "font-medium text-green-600", children: formatCurrency(entry.credit_amount) })) : (_jsx("span", { className: "text-muted-foreground", children: "\u2014" })) }), _jsx(TableCell, { className: "text-right", children: _jsx("span", { className: `font-medium ${entry.running_balance >= 0 ? 'text-green-600' : 'text-red-600'}`, children: formatCurrency(Math.abs(entry.running_balance)) }) }), _jsx(TableCell, { children: entry.reference_type && (_jsx(Badge, { variant: "outline", className: "text-xs", children: entry.reference_type })) })] }, entry.id)))) })] }) }) })] }) }), _jsx(TabsContent, { value: "trial-balance", className: "space-y-4", children: _jsxs(Card, { children: [_jsxs(CardHeader, { children: [_jsxs(CardTitle, { className: "flex items-center justify-between", children: [_jsxs("span", { className: "flex items-center", children: [_jsx(Calculator, { className: "h-5 w-5 mr-2" }), "Trial Balance"] }), _jsxs(Badge, { variant: "secondary", children: [filteredTrialBalance.length, " accounts"] })] }), _jsx(CardDescription, { children: "Summary of all account balances to verify debits equal credits" })] }), _jsxs(CardContent, { children: [_jsx("div", { className: "rounded-md border", children: _jsxs(Table, { children: [_jsx(TableHeader, { children: _jsxs(TableRow, { children: [_jsx(TableHead, { children: "Account Code" }), _jsx(TableHead, { children: "Account Name" }), _jsx(TableHead, { children: "Type" }), _jsx(TableHead, { className: "text-right", children: "Debit Balance" }), _jsx(TableHead, { className: "text-right", children: "Credit Balance" })] }) }), _jsx(TableBody, { children: filteredTrialBalance.length === 0 ? (_jsx(TableRow, { children: _jsx(TableCell, { colSpan: 5, className: "text-center py-8", children: _jsxs("div", { className: "flex flex-col items-center space-y-2", children: [_jsx(Calculator, { className: "h-8 w-8 text-muted-foreground" }), _jsx("p", { className: "text-muted-foreground", children: "No trial balance data found" }), _jsx("p", { className: "text-sm text-muted-foreground", children: "Try adjusting your date range or refresh the data" })] }) }) })) : (_jsxs(_Fragment, { children: [filteredTrialBalance.map((entry) => (_jsxs(TableRow, { children: [_jsx(TableCell, { className: "font-medium", children: entry.account_code }), _jsx(TableCell, { children: entry.account_name }), _jsx(TableCell, { children: _jsx(Badge, { className: getAccountTypeColor(entry.account_type), children: entry.account_type }) }), _jsx(TableCell, { className: "text-right", children: entry.debit_balance > 0 ? (_jsx("span", { className: "font-medium text-blue-600", children: formatCurrency(entry.debit_balance) })) : (_jsx("span", { className: "text-muted-foreground", children: "\u2014" })) }), _jsx(TableCell, { className: "text-right", children: entry.credit_balance > 0 ? (_jsx("span", { className: "font-medium text-green-600", children: formatCurrency(entry.credit_balance) })) : (_jsx("span", { className: "text-muted-foreground", children: "\u2014" })) })] }, entry.account_code))), _jsxs(TableRow, { className: "border-t-2 border-gray-300 bg-gray-50 font-bold", children: [_jsx(TableCell, { colSpan: 3, className: "text-right", children: _jsx("strong", { children: "TOTALS:" }) }), _jsx(TableCell, { className: "text-right", children: _jsx("span", { className: "font-bold text-blue-600", children: formatCurrency(trialBalanceTotals.totalDebits) }) }), _jsx(TableCell, { className: "text-right", children: _jsx("span", { className: "font-bold text-green-600", children: formatCurrency(trialBalanceTotals.totalCredits) }) })] })] })) })] }) }), filteredTrialBalance.length > 0 && (_jsx("div", { className: "mt-4 p-4 rounded-lg border", children: _jsxs("div", { className: "flex items-center justify-between", children: [_jsx("span", { className: "font-medium", children: "Balance Check:" }), _jsx("div", { className: "flex items-center space-x-2", children: Math.abs(trialBalanceTotals.totalDebits - trialBalanceTotals.totalCredits) < 0.01 ? (_jsxs(_Fragment, { children: [_jsx(CheckCircle, { className: "h-5 w-5 text-green-500" }), _jsx("span", { className: "text-green-600 font-medium", children: "Balanced" })] })) : (_jsxs(_Fragment, { children: [_jsx(AlertCircle, { className: "h-5 w-5 text-red-500" }), _jsxs("span", { className: "text-red-600 font-medium", children: ["Out of Balance by ", formatCurrency(Math.abs(trialBalanceTotals.totalDebits - trialBalanceTotals.totalCredits))] })] })) })] }) }))] })] }) }), _jsx(TabsContent, { value: "balances", className: "space-y-4", children: _jsxs(Card, { children: [_jsxs(CardHeader, { children: [_jsxs(CardTitle, { className: "flex items-center justify-between", children: [_jsxs("span", { className: "flex items-center", children: [_jsx(DollarSign, { className: "h-5 w-5 mr-2" }), "Account Balances"] }), _jsxs(Badge, { variant: "secondary", children: [filteredAccountBalances.length, " accounts"] })] }), _jsxs(CardDescription, { children: ["Current balance for each account as of ", endDate || 'today'] })] }), _jsxs(CardContent, { children: [_jsx("div", { className: "rounded-md border", children: _jsxs(Table, { children: [_jsx(TableHeader, { children: _jsxs(TableRow, { children: [_jsx(TableHead, { children: "Account Code" }), _jsx(TableHead, { children: "Account Name" }), _jsx(TableHead, { children: "Type" }), _jsx(TableHead, { className: "text-right", children: "Balance" }), _jsx(TableHead, { className: "text-center", children: "Actions" })] }) }), _jsx(TableBody, { children: filteredAccountBalances.length === 0 ? (_jsx(TableRow, { children: _jsx(TableCell, { colSpan: 5, className: "text-center py-8", children: _jsxs("div", { className: "flex flex-col items-center space-y-2", children: [_jsx(DollarSign, { className: "h-8 w-8 text-muted-foreground" }), _jsx("p", { className: "text-muted-foreground", children: "No account balances found" }), _jsx("p", { className: "text-sm text-muted-foreground", children: "Try refreshing the data or check your filters" })] }) }) })) : (filteredAccountBalances.map((account) => (_jsxs(TableRow, { children: [_jsx(TableCell, { className: "font-medium", children: account.account_code }), _jsx(TableCell, { children: account.account_name }), _jsx(TableCell, { children: _jsx(Badge, { className: getAccountTypeColor(account.account_type), children: account.account_type }) }), _jsx(TableCell, { className: "text-right", children: _jsx("span", { className: `font-medium ${account.balance >= 0 ? 'text-green-600' : 'text-red-600'}`, children: formatCurrency(Math.abs(account.balance)) }) }), _jsx(TableCell, { className: "text-center", children: _jsx(Button, { variant: "ghost", size: "sm", onClick: () => {
                                                                            setSelectedAccount(account.account_id);
                                                                            setActiveTab('entries');
                                                                        }, children: _jsx(Eye, { className: "h-4 w-4" }) }) })] }, account.account_id)))) })] }) }), filteredAccountBalances.length > 0 && (_jsx("div", { className: "mt-6 grid gap-4 md:grid-cols-2 lg:grid-cols-5", children: ['Asset', 'Liability', 'Equity', 'Revenue', 'Expense'].map((type) => {
                                                const typeAccounts = filteredAccountBalances.filter(acc => acc.account_type === type);
                                                const totalBalance = typeAccounts.reduce((sum, acc) => sum + acc.balance, 0);
                                                return (_jsx(Card, { children: _jsx(CardContent, { className: "pt-4", children: _jsxs("div", { className: "text-center", children: [_jsx(Badge, { className: getAccountTypeColor(type), variant: "secondary", children: type }), _jsxs("div", { className: "mt-2", children: [_jsx("div", { className: "text-2xl font-bold", children: formatCurrency(Math.abs(totalBalance)) }), _jsxs("p", { className: "text-xs text-muted-foreground", children: [typeAccounts.length, " accounts"] })] })] }) }) }, type));
                                            }) }))] })] }) })] })] }));
};
export default GeneralLedger;
