'use client';
import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import { Search, Plus, Package, AlertTriangle, Warehouse, Edit, Trash2, MoreHorizontal, Download, Upload, MapPin, DollarSign } from 'lucide-react';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@/components/ui/dropdown-menu';
import { toast } from 'sonner';
const InventoryDashboard = () => {
    const [inventory, setInventory] = useState([]);
    const [filteredInventory, setFilteredInventory] = useState([]);
    const [metrics, setMetrics] = useState({
        total_items: 0,
        total_value: 0,
        low_stock_items: 0,
        out_of_stock_items: 0,
        available_items: 0,
        rented_items: 0,
        maintenance_items: 0,
        monthly_revenue: 0
    });
    const [searchTerm, setSearchTerm] = useState('');
    const [categoryFilter, setCategoryFilter] = useState('all');
    const [statusFilter, setStatusFilter] = useState('all');
    const [conditionFilter, setConditionFilter] = useState('all');
    const [isLoading, setIsLoading] = useState(true);
    const [isDialogOpen, setIsDialogOpen] = useState(false);
    const [editingItem, setEditingItem] = useState(null);
    const [formData, setFormData] = useState({
        part_name: '',
        description: '',
        category: '',
        brand: '',
        model: '',
        current_stock: 0,
        minimum_stock: 1,
        maximum_stock: 10,
        unit_cost: 0,
        rental_rate: 0,
        location: '',
        condition: 'New',
        supplier_id: '',
        serial_number: '',
        barcode: '',
        notes: ''
    });
    // Mock data for development
    useEffect(() => {
        const mockInventory = [
            {
                id: '1',
                part_number: 'EXC001',
                part_name: 'Caterpillar 320D Excavator',
                description: '20-ton hydraulic excavator with GPS tracking',
                category: 'Heavy Machinery',
                brand: 'Caterpillar',
                model: '320D',
                current_stock: 3,
                minimum_stock: 1,
                maximum_stock: 5,
                unit_cost: 850000,
                rental_rate: 2500,
                location: 'Yard A - Section 1',
                condition: 'Good',
                status: 'Available',
                supplier_id: 'SUP001',
                supplier_name: 'Heavy Equipment SA',
                last_maintenance: '2024-04-15',
                next_maintenance: '2024-07-15',
                purchase_date: '2023-01-15',
                warranty_expiry: '2026-01-15',
                serial_number: 'CAT320D001',
                barcode: '1234567890123',
                notes: 'Recently serviced, excellent condition',
                total_rentals: 45,
                total_revenue: 112500,
                created_at: '2023-01-15'
            },
            {
                id: '2',
                part_number: 'GEN001',
                part_name: 'Diesel Generator 100kVA',
                description: 'Industrial diesel generator with automatic start',
                category: 'Power Equipment',
                brand: 'Cummins',
                model: 'C100D5',
                current_stock: 0,
                minimum_stock: 2,
                maximum_stock: 8,
                unit_cost: 125000,
                rental_rate: 800,
                location: 'Yard B - Section 3',
                condition: 'Good',
                status: 'Rented',
                supplier_id: 'SUP002',
                supplier_name: 'Power Solutions Ltd',
                last_maintenance: '2024-05-01',
                next_maintenance: '2024-08-01',
                purchase_date: '2023-03-10',
                warranty_expiry: '2025-03-10',
                serial_number: 'CUM100D001',
                barcode: '2345678901234',
                notes: 'All units currently on rental',
                total_rentals: 28,
                total_revenue: 22400,
                created_at: '2023-03-10'
            },
            {
                id: '3',
                part_number: 'SCF001',
                part_name: 'Steel Scaffolding System',
                description: 'Modular steel scaffolding with safety rails',
                category: 'Construction Equipment',
                brand: 'SafeScaff',
                model: 'SS-2000',
                current_stock: 15,
                minimum_stock: 10,
                maximum_stock: 50,
                unit_cost: 2500,
                rental_rate: 25,
                location: 'Warehouse C',
                condition: 'Good',
                status: 'Available',
                supplier_id: 'SUP003',
                supplier_name: 'Construction Supplies Co',
                last_maintenance: '2024-05-10',
                next_maintenance: '2024-11-10',
                purchase_date: '2023-02-20',
                warranty_expiry: '2025-02-20',
                serial_number: 'SS2000-001',
                barcode: '3456789012345',
                notes: 'Popular item, high demand',
                total_rentals: 120,
                total_revenue: 3000,
                created_at: '2023-02-20'
            },
            {
                id: '4',
                part_number: 'CMP001',
                part_name: 'Air Compressor 185CFM',
                description: 'Portable diesel air compressor',
                category: 'Air Tools',
                brand: 'Atlas Copco',
                model: 'XAS185',
                current_stock: 1,
                minimum_stock: 2,
                maximum_stock: 6,
                unit_cost: 95000,
                rental_rate: 450,
                location: 'Yard A - Section 2',
                condition: 'Fair',
                status: 'Maintenance',
                supplier_id: 'SUP004',
                supplier_name: 'Compressed Air Systems',
                last_maintenance: '2024-05-20',
                next_maintenance: '2024-06-20',
                purchase_date: '2022-11-15',
                warranty_expiry: '2024-11-15',
                serial_number: 'XAS185-001',
                barcode: '4567890123456',
                notes: 'Scheduled maintenance in progress',
                total_rentals: 35,
                total_revenue: 15750,
                created_at: '2022-11-15'
            }
        ];
        setTimeout(() => {
            setInventory(mockInventory);
            setFilteredInventory(mockInventory);
            // Calculate metrics
            const totalItems = mockInventory.length;
            const totalValue = mockInventory.reduce((sum, item) => sum + (item.unit_cost * item.current_stock), 0);
            const lowStockItems = mockInventory.filter(item => item.current_stock <= item.minimum_stock).length;
            const outOfStockItems = mockInventory.filter(item => item.current_stock === 0).length;
            const availableItems = mockInventory.filter(item => item.status === 'Available').length;
            const rentedItems = mockInventory.filter(item => item.status === 'Rented').length;
            const maintenanceItems = mockInventory.filter(item => item.status === 'Maintenance').length;
            const monthlyRevenue = mockInventory.reduce((sum, item) => sum + item.total_revenue, 0);
            setMetrics({
                total_items: totalItems,
                total_value: totalValue,
                low_stock_items: lowStockItems,
                out_of_stock_items: outOfStockItems,
                available_items: availableItems,
                rented_items: rentedItems,
                maintenance_items: maintenanceItems,
                monthly_revenue: monthlyRevenue
            });
            setIsLoading(false);
        }, 1000);
    }, []);
    // Filter inventory based on search and filters
    useEffect(() => {
        let filtered = inventory;
        if (searchTerm) {
            filtered = filtered.filter(item => item.part_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                item.part_number.toLowerCase().includes(searchTerm.toLowerCase()) ||
                item.brand.toLowerCase().includes(searchTerm.toLowerCase()) ||
                item.model.toLowerCase().includes(searchTerm.toLowerCase()) ||
                item.description.toLowerCase().includes(searchTerm.toLowerCase()));
        }
        if (categoryFilter !== 'all') {
            filtered = filtered.filter(item => item.category === categoryFilter);
        }
        if (statusFilter !== 'all') {
            filtered = filtered.filter(item => item.status === statusFilter);
        }
        if (conditionFilter !== 'all') {
            filtered = filtered.filter(item => item.condition === conditionFilter);
        }
        setFilteredInventory(filtered);
    }, [inventory, searchTerm, categoryFilter, statusFilter, conditionFilter]);
    const handleCreateItem = async () => {
        try {
            const newItem = {
                id: Date.now().toString(),
                part_number: `ITM${String(inventory.length + 1).padStart(3, '0')}`,
                ...formData,
                status: 'Available',
                supplier_name: 'Default Supplier',
                last_maintenance: '',
                next_maintenance: '',
                purchase_date: new Date().toISOString().split('T')[0],
                warranty_expiry: '',
                total_rentals: 0,
                total_revenue: 0,
                created_at: new Date().toISOString()
            };
            setInventory(prev => [...prev, newItem]);
            setIsDialogOpen(false);
            resetForm();
            toast.success('Inventory item created successfully');
        }
        catch (error) {
            toast.error('Failed to create inventory item');
        }
    };
    const handleUpdateItem = async () => {
        if (!editingItem)
            return;
        try {
            const updatedItem = { ...editingItem, ...formData };
            setInventory(prev => prev.map(item => item.id === editingItem.id ? updatedItem : item));
            setIsDialogOpen(false);
            setEditingItem(null);
            resetForm();
            toast.success('Inventory item updated successfully');
        }
        catch (error) {
            toast.error('Failed to update inventory item');
        }
    };
    const handleDeleteItem = async (itemId) => {
        try {
            setInventory(prev => prev.filter(item => item.id !== itemId));
            toast.success('Inventory item deleted successfully');
        }
        catch (error) {
            toast.error('Failed to delete inventory item');
        }
    };
    const resetForm = () => {
        setFormData({
            part_name: '',
            description: '',
            category: '',
            brand: '',
            model: '',
            current_stock: 0,
            minimum_stock: 1,
            maximum_stock: 10,
            unit_cost: 0,
            rental_rate: 0,
            location: '',
            condition: 'New',
            supplier_id: '',
            serial_number: '',
            barcode: '',
            notes: ''
        });
    };
    const openEditDialog = (item) => {
        setEditingItem(item);
        setFormData({
            part_name: item.part_name,
            description: item.description,
            category: item.category,
            brand: item.brand,
            model: item.model,
            current_stock: item.current_stock,
            minimum_stock: item.minimum_stock,
            maximum_stock: item.maximum_stock,
            unit_cost: item.unit_cost,
            rental_rate: item.rental_rate,
            location: item.location,
            condition: item.condition,
            supplier_id: item.supplier_id,
            serial_number: item.serial_number,
            barcode: item.barcode,
            notes: item.notes
        });
        setIsDialogOpen(true);
    };
    const getStatusBadgeVariant = (status) => {
        switch (status) {
            case 'Available': return 'default';
            case 'Rented': return 'secondary';
            case 'Maintenance': return 'destructive';
            case 'Reserved': return 'outline';
            default: return 'secondary';
        }
    };
    const getConditionBadgeVariant = (condition) => {
        switch (condition) {
            case 'New': return 'default';
            case 'Good': return 'secondary';
            case 'Fair': return 'outline';
            case 'Poor': return 'destructive';
            case 'Out of Service': return 'destructive';
            default: return 'secondary';
        }
    };
    const getStockStatus = (item) => {
        if (item.current_stock === 0)
            return { status: 'Out of Stock', variant: 'destructive' };
        if (item.current_stock <= item.minimum_stock)
            return { status: 'Low Stock', variant: 'destructive' };
        return { status: 'In Stock', variant: 'default' };
    };
    const formatCurrency = (amount) => {
        return new Intl.NumberFormat('en-ZA', {
            style: 'currency',
            currency: 'ZAR'
        }).format(amount);
    };
    if (isLoading) {
        return (_jsx("div", { className: "flex items-center justify-center h-64", children: _jsx("div", { className: "animate-spin rounded-full h-8 w-8 border-b-2 border-primary" }) }));
    }
    return (_jsxs("div", { className: "space-y-6", children: [_jsxs("div", { className: "flex justify-between items-center", children: [_jsxs("div", { children: [_jsx("h1", { className: "text-3xl font-bold tracking-tight", children: "Inventory Management" }), _jsx("p", { className: "text-muted-foreground", children: "Track and manage your equipment inventory" })] }), _jsxs("div", { className: "flex gap-2", children: [_jsxs(Button, { variant: "outline", size: "sm", children: [_jsx(Download, { className: "h-4 w-4 mr-2" }), "Export"] }), _jsxs(Button, { variant: "outline", size: "sm", children: [_jsx(Upload, { className: "h-4 w-4 mr-2" }), "Import"] }), _jsxs(Dialog, { open: isDialogOpen, onOpenChange: setIsDialogOpen, children: [_jsx(DialogTrigger, { asChild: true, children: _jsxs(Button, { onClick: () => { setEditingItem(null); resetForm(); }, children: [_jsx(Plus, { className: "h-4 w-4 mr-2" }), "Add Item"] }) }), _jsxs(DialogContent, { className: "max-w-3xl max-h-[90vh] overflow-y-auto", children: [_jsxs(DialogHeader, { children: [_jsx(DialogTitle, { children: editingItem ? 'Edit Inventory Item' : 'Add New Inventory Item' }), _jsx(DialogDescription, { children: editingItem
                                                            ? 'Update inventory item details and specifications.'
                                                            : 'Add a new item to your inventory with complete specifications.' })] }), _jsx("div", { className: "grid gap-4 py-4", children: _jsxs(Tabs, { defaultValue: "basic", className: "w-full", children: [_jsxs(TabsList, { className: "grid w-full grid-cols-4", children: [_jsx(TabsTrigger, { value: "basic", children: "Basic Info" }), _jsx(TabsTrigger, { value: "stock", children: "Stock & Pricing" }), _jsx(TabsTrigger, { value: "details", children: "Details" }), _jsx(TabsTrigger, { value: "tracking", children: "Tracking" })] }), _jsxs(TabsContent, { value: "basic", className: "space-y-4", children: [_jsxs("div", { className: "grid grid-cols-2 gap-4", children: [_jsxs("div", { className: "space-y-2", children: [_jsx(Label, { htmlFor: "part_name", children: "Item Name *" }), _jsx(Input, { id: "part_name", value: formData.part_name, onChange: (e) => setFormData(prev => ({ ...prev, part_name: e.target.value })), placeholder: "Enter item name" })] }), _jsxs("div", { className: "space-y-2", children: [_jsx(Label, { htmlFor: "category", children: "Category *" }), _jsxs(Select, { value: formData.category, onValueChange: (value) => setFormData(prev => ({ ...prev, category: value })), children: [_jsx(SelectTrigger, { children: _jsx(SelectValue, { placeholder: "Select category" }) }), _jsxs(SelectContent, { children: [_jsx(SelectItem, { value: "Heavy Machinery", children: "Heavy Machinery" }), _jsx(SelectItem, { value: "Power Equipment", children: "Power Equipment" }), _jsx(SelectItem, { value: "Construction Equipment", children: "Construction Equipment" }), _jsx(SelectItem, { value: "Air Tools", children: "Air Tools" }), _jsx(SelectItem, { value: "Hand Tools", children: "Hand Tools" }), _jsx(SelectItem, { value: "Safety Equipment", children: "Safety Equipment" })] })] })] })] }), _jsxs("div", { className: "grid grid-cols-2 gap-4", children: [_jsxs("div", { className: "space-y-2", children: [_jsx(Label, { htmlFor: "brand", children: "Brand" }), _jsx(Input, { id: "brand", value: formData.brand, onChange: (e) => setFormData(prev => ({ ...prev, brand: e.target.value })), placeholder: "Equipment brand" })] }), _jsxs("div", { className: "space-y-2", children: [_jsx(Label, { htmlFor: "model", children: "Model" }), _jsx(Input, { id: "model", value: formData.model, onChange: (e) => setFormData(prev => ({ ...prev, model: e.target.value })), placeholder: "Model number" })] })] }), _jsxs("div", { className: "space-y-2", children: [_jsx(Label, { htmlFor: "description", children: "Description" }), _jsx(Textarea, { id: "description", value: formData.description, onChange: (e) => setFormData(prev => ({ ...prev, description: e.target.value })), placeholder: "Detailed description of the item...", rows: 3 })] })] }), _jsxs(TabsContent, { value: "stock", className: "space-y-4", children: [_jsxs("div", { className: "grid grid-cols-3 gap-4", children: [_jsxs("div", { className: "space-y-2", children: [_jsx(Label, { htmlFor: "current_stock", children: "Current Stock" }), _jsx(Input, { id: "current_stock", type: "number", value: formData.current_stock, onChange: (e) => setFormData(prev => ({ ...prev, current_stock: Number(e.target.value) })), placeholder: "0" })] }), _jsxs("div", { className: "space-y-2", children: [_jsx(Label, { htmlFor: "minimum_stock", children: "Minimum Stock" }), _jsx(Input, { id: "minimum_stock", type: "number", value: formData.minimum_stock, onChange: (e) => setFormData(prev => ({ ...prev, minimum_stock: Number(e.target.value) })), placeholder: "1" })] }), _jsxs("div", { className: "space-y-2", children: [_jsx(Label, { htmlFor: "maximum_stock", children: "Maximum Stock" }), _jsx(Input, { id: "maximum_stock", type: "number", value: formData.maximum_stock, onChange: (e) => setFormData(prev => ({ ...prev, maximum_stock: Number(e.target.value) })), placeholder: "10" })] })] }), _jsxs("div", { className: "grid grid-cols-2 gap-4", children: [_jsxs("div", { className: "space-y-2", children: [_jsx(Label, { htmlFor: "unit_cost", children: "Unit Cost (ZAR)" }), _jsx(Input, { id: "unit_cost", type: "number", value: formData.unit_cost, onChange: (e) => setFormData(prev => ({ ...prev, unit_cost: Number(e.target.value) })), placeholder: "0" })] }), _jsxs("div", { className: "space-y-2", children: [_jsx(Label, { htmlFor: "rental_rate", children: "Daily Rental Rate (ZAR)" }), _jsx(Input, { id: "rental_rate", type: "number", value: formData.rental_rate, onChange: (e) => setFormData(prev => ({ ...prev, rental_rate: Number(e.target.value) })), placeholder: "0" })] })] })] }), _jsxs(TabsContent, { value: "details", className: "space-y-4", children: [_jsxs("div", { className: "grid grid-cols-2 gap-4", children: [_jsxs("div", { className: "space-y-2", children: [_jsx(Label, { htmlFor: "location", children: "Location" }), _jsx(Input, { id: "location", value: formData.location, onChange: (e) => setFormData(prev => ({ ...prev, location: e.target.value })), placeholder: "Storage location" })] }), _jsxs("div", { className: "space-y-2", children: [_jsx(Label, { htmlFor: "condition", children: "Condition" }), _jsxs(Select, { value: formData.condition, onValueChange: (value) => setFormData(prev => ({ ...prev, condition: value })), children: [_jsx(SelectTrigger, { children: _jsx(SelectValue, {}) }), _jsxs(SelectContent, { children: [_jsx(SelectItem, { value: "New", children: "New" }), _jsx(SelectItem, { value: "Good", children: "Good" }), _jsx(SelectItem, { value: "Fair", children: "Fair" }), _jsx(SelectItem, { value: "Poor", children: "Poor" }), _jsx(SelectItem, { value: "Out of Service", children: "Out of Service" })] })] })] })] }), _jsxs("div", { className: "space-y-2", children: [_jsx(Label, { htmlFor: "supplier_id", children: "Supplier ID" }), _jsx(Input, { id: "supplier_id", value: formData.supplier_id, onChange: (e) => setFormData(prev => ({ ...prev, supplier_id: e.target.value })), placeholder: "Supplier reference" })] }), _jsxs("div", { className: "space-y-2", children: [_jsx(Label, { htmlFor: "notes", children: "Notes" }), _jsx(Textarea, { id: "notes", value: formData.notes, onChange: (e) => setFormData(prev => ({ ...prev, notes: e.target.value })), placeholder: "Additional notes about this item...", rows: 3 })] })] }), _jsx(TabsContent, { value: "tracking", className: "space-y-4", children: _jsxs("div", { className: "grid grid-cols-2 gap-4", children: [_jsxs("div", { className: "space-y-2", children: [_jsx(Label, { htmlFor: "serial_number", children: "Serial Number" }), _jsx(Input, { id: "serial_number", value: formData.serial_number, onChange: (e) => setFormData(prev => ({ ...prev, serial_number: e.target.value })), placeholder: "Unique serial number" })] }), _jsxs("div", { className: "space-y-2", children: [_jsx(Label, { htmlFor: "barcode", children: "Barcode" }), _jsx(Input, { id: "barcode", value: formData.barcode, onChange: (e) => setFormData(prev => ({ ...prev, barcode: e.target.value })), placeholder: "Barcode for scanning" })] })] }) })] }) }), _jsxs("div", { className: "flex justify-end gap-2", children: [_jsx(Button, { variant: "outline", onClick: () => setIsDialogOpen(false), children: "Cancel" }), _jsx(Button, { onClick: editingItem ? handleUpdateItem : handleCreateItem, children: editingItem ? 'Update Item' : 'Create Item' })] })] })] })] })] }), _jsxs("div", { className: "grid gap-4 md:grid-cols-2 lg:grid-cols-4", children: [_jsxs(Card, { children: [_jsxs(CardHeader, { className: "flex flex-row items-center justify-between space-y-0 pb-2", children: [_jsx(CardTitle, { className: "text-sm font-medium", children: "Total Items" }), _jsx(Package, { className: "h-4 w-4 text-muted-foreground" })] }), _jsxs(CardContent, { children: [_jsx("div", { className: "text-2xl font-bold", children: metrics.total_items }), _jsxs("p", { className: "text-xs text-muted-foreground", children: [formatCurrency(metrics.total_value), " total value"] })] })] }), _jsxs(Card, { children: [_jsxs(CardHeader, { className: "flex flex-row items-center justify-between space-y-0 pb-2", children: [_jsx(CardTitle, { className: "text-sm font-medium", children: "Available" }), _jsx(Warehouse, { className: "h-4 w-4 text-muted-foreground" })] }), _jsxs(CardContent, { children: [_jsx("div", { className: "text-2xl font-bold text-green-600", children: metrics.available_items }), _jsx("p", { className: "text-xs text-muted-foreground", children: "Ready for rental" })] })] }), _jsxs(Card, { children: [_jsxs(CardHeader, { className: "flex flex-row items-center justify-between space-y-0 pb-2", children: [_jsx(CardTitle, { className: "text-sm font-medium", children: "Low Stock" }), _jsx(AlertTriangle, { className: "h-4 w-4 text-muted-foreground" })] }), _jsxs(CardContent, { children: [_jsx("div", { className: "text-2xl font-bold text-orange-600", children: metrics.low_stock_items }), _jsxs("p", { className: "text-xs text-muted-foreground", children: [metrics.out_of_stock_items, " out of stock"] })] })] }), _jsxs(Card, { children: [_jsxs(CardHeader, { className: "flex flex-row items-center justify-between space-y-0 pb-2", children: [_jsx(CardTitle, { className: "text-sm font-medium", children: "Monthly Revenue" }), _jsx(DollarSign, { className: "h-4 w-4 text-muted-foreground" })] }), _jsxs(CardContent, { children: [_jsx("div", { className: "text-2xl font-bold text-green-600", children: formatCurrency(metrics.monthly_revenue) }), _jsx("p", { className: "text-xs text-muted-foreground", children: "From inventory rentals" })] })] })] }), _jsx(Card, { children: _jsx(CardContent, { className: "pt-6", children: _jsxs("div", { className: "flex flex-col sm:flex-row gap-4", children: [_jsx("div", { className: "flex-1", children: _jsxs("div", { className: "relative", children: [_jsx(Search, { className: "absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" }), _jsx(Input, { placeholder: "Search inventory by name, part number, brand, or model...", value: searchTerm, onChange: (e) => setSearchTerm(e.target.value), className: "pl-10" })] }) }), _jsxs("div", { className: "flex gap-2", children: [_jsxs(Select, { value: categoryFilter, onValueChange: setCategoryFilter, children: [_jsx(SelectTrigger, { className: "w-40", children: _jsx(SelectValue, { placeholder: "Category" }) }), _jsxs(SelectContent, { children: [_jsx(SelectItem, { value: "all", children: "All Categories" }), _jsx(SelectItem, { value: "Heavy Machinery", children: "Heavy Machinery" }), _jsx(SelectItem, { value: "Power Equipment", children: "Power Equipment" }), _jsx(SelectItem, { value: "Construction Equipment", children: "Construction Equipment" }), _jsx(SelectItem, { value: "Air Tools", children: "Air Tools" })] })] }), _jsxs(Select, { value: statusFilter, onValueChange: setStatusFilter, children: [_jsx(SelectTrigger, { className: "w-32", children: _jsx(SelectValue, { placeholder: "Status" }) }), _jsxs(SelectContent, { children: [_jsx(SelectItem, { value: "all", children: "All Status" }), _jsx(SelectItem, { value: "Available", children: "Available" }), _jsx(SelectItem, { value: "Rented", children: "Rented" }), _jsx(SelectItem, { value: "Maintenance", children: "Maintenance" }), _jsx(SelectItem, { value: "Reserved", children: "Reserved" })] })] }), _jsxs(Select, { value: conditionFilter, onValueChange: setConditionFilter, children: [_jsx(SelectTrigger, { className: "w-32", children: _jsx(SelectValue, { placeholder: "Condition" }) }), _jsxs(SelectContent, { children: [_jsx(SelectItem, { value: "all", children: "All Conditions" }), _jsx(SelectItem, { value: "New", children: "New" }), _jsx(SelectItem, { value: "Good", children: "Good" }), _jsx(SelectItem, { value: "Fair", children: "Fair" }), _jsx(SelectItem, { value: "Poor", children: "Poor" })] })] })] })] }) }) }), _jsx("div", { className: "grid gap-6 md:grid-cols-2 lg:grid-cols-3", children: filteredInventory.map((item) => {
                    const stockStatus = getStockStatus(item);
                    return (_jsxs(Card, { className: "hover:shadow-md transition-shadow", children: [_jsx(CardHeader, { className: "pb-3", children: _jsxs("div", { className: "flex justify-between items-start", children: [_jsxs("div", { className: "space-y-1", children: [_jsx(CardTitle, { className: "text-lg", children: item.part_name }), _jsxs(CardDescription, { className: "text-sm", children: [item.part_number, " \u2022 ", item.brand, " ", item.model] })] }), _jsxs(DropdownMenu, { children: [_jsx(DropdownMenuTrigger, { asChild: true, children: _jsx(Button, { variant: "ghost", size: "sm", children: _jsx(MoreHorizontal, { className: "h-4 w-4" }) }) }), _jsxs(DropdownMenuContent, { align: "end", children: [_jsxs(DropdownMenuItem, { onClick: () => openEditDialog(item), children: [_jsx(Edit, { className: "h-4 w-4 mr-2" }), "Edit"] }), _jsxs(DropdownMenuItem, { onClick: () => handleDeleteItem(item.id), className: "text-destructive", children: [_jsx(Trash2, { className: "h-4 w-4 mr-2" }), "Delete"] })] })] })] }) }), _jsxs(CardContent, { className: "space-y-4", children: [_jsxs("div", { className: "flex flex-wrap gap-2", children: [_jsx(Badge, { variant: getStatusBadgeVariant(item.status), children: item.status }), _jsx(Badge, { variant: getConditionBadgeVariant(item.condition), children: item.condition }), _jsx(Badge, { variant: stockStatus.variant, children: stockStatus.status })] }), _jsxs("div", { className: "space-y-2", children: [_jsxs("div", { className: "flex items-center text-sm text-muted-foreground", children: [_jsx(Package, { className: "h-4 w-4 mr-2" }), "Stock: ", item.current_stock, " / ", item.maximum_stock] }), _jsxs("div", { className: "flex items-center text-sm text-muted-foreground", children: [_jsx(MapPin, { className: "h-4 w-4 mr-2" }), item.location] }), _jsxs("div", { className: "flex items-center text-sm text-muted-foreground", children: [_jsx(DollarSign, { className: "h-4 w-4 mr-2" }), formatCurrency(item.rental_rate), "/day"] })] }), _jsxs("div", { className: "grid grid-cols-2 gap-4 pt-2 border-t", children: [_jsxs("div", { className: "text-center", children: [_jsx("div", { className: "text-lg font-bold text-primary", children: item.total_rentals }), _jsx("div", { className: "text-xs text-muted-foreground", children: "Rentals" })] }), _jsxs("div", { className: "text-center", children: [_jsx("div", { className: "text-lg font-bold text-green-600", children: formatCurrency(item.total_revenue) }), _jsx("div", { className: "text-xs text-muted-foreground", children: "Revenue" })] })] }), item.description && (_jsx("p", { className: "text-sm text-muted-foreground pt-2 border-t", children: item.description.length > 100
                                            ? `${item.description.substring(0, 100)}...`
                                            : item.description }))] })] }, item.id));
                }) }), filteredInventory.length === 0 && (_jsx(Card, { children: _jsx(CardContent, { className: "flex flex-col items-center justify-center py-12", children: _jsxs("div", { className: "text-muted-foreground text-center", children: [_jsx(Package, { className: "h-12 w-12 mx-auto mb-4 opacity-50" }), _jsx("h3", { className: "text-lg font-medium mb-2", children: "No inventory items found" }), _jsx("p", { className: "text-sm", children: searchTerm || categoryFilter !== 'all' || statusFilter !== 'all' || conditionFilter !== 'all'
                                    ? 'Try adjusting your search criteria or filters.'
                                    : 'Get started by adding your first inventory item.' })] }) }) }))] }));
};
export default InventoryDashboard;
