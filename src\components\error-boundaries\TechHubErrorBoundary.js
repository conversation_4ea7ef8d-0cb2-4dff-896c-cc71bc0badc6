import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
import { Component } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { AlertTriangle, RefreshCw } from 'lucide-react';
class TechHubErrorBoundary extends Component {
    state = {
        hasError: false,
    };
    static getDerivedStateFromError(error) {
        return {
            hasError: true,
            error,
        };
    }
    componentDidCatch(error, errorInfo) {
        console.error('TechHub Error Boundary caught an error:', error, errorInfo);
        this.setState({
            error,
            errorInfo,
        });
        // Call the onError prop if provided
        if (this.props.onError) {
            this.props.onError(error, errorInfo);
        }
    }
    handleRetry = () => {
        this.setState({
            hasError: false,
            error: undefined,
            errorInfo: undefined,
        });
    };
    render() {
        if (this.state.hasError) {
            // You can render any custom fallback UI
            if (this.props.fallback) {
                return this.props.fallback;
            }
            return (_jsx("div", { className: "p-6", children: _jsxs(Card, { className: "border-red-200 bg-red-50", children: [_jsxs(CardHeader, { children: [_jsxs("div", { className: "flex items-center space-x-2", children: [_jsx(AlertTriangle, { className: "h-5 w-5 text-red-600" }), _jsx(CardTitle, { className: "text-red-800", children: "Tech Hub Module Error" })] }), _jsx(CardDescription, { className: "text-red-700", children: "Something went wrong loading this module. This might be due to a component error or missing dependency." })] }), _jsxs(CardContent, { className: "space-y-4", children: [this.state.error && (_jsxs("div", { className: "bg-red-100 p-3 rounded-lg", children: [_jsx("h4", { className: "font-medium text-red-800 mb-2", children: "Error Details:" }), _jsx("p", { className: "text-sm text-red-700 font-mono break-all", children: this.state.error.message }), process.env.NODE_ENV === 'development' && this.state.errorInfo && (_jsxs("details", { className: "mt-2", children: [_jsx("summary", { className: "cursor-pointer text-red-600 font-medium", children: "Stack Trace (Development Only)" }), _jsx("pre", { className: "text-xs mt-2 text-red-600 whitespace-pre-wrap", children: this.state.error.stack })] }))] })), _jsxs("div", { className: "flex space-x-3", children: [_jsxs(Button, { onClick: this.handleRetry, className: "flex items-center space-x-2", variant: "outline", children: [_jsx(RefreshCw, { className: "h-4 w-4" }), _jsx("span", { children: "Try Again" })] }), _jsx(Button, { onClick: () => window.location.reload(), variant: "outline", children: "Reload Page" })] }), _jsxs("div", { className: "text-xs text-gray-600 mt-4", children: [_jsx("p", { children: _jsx("strong", { children: "Troubleshooting Tips:" }) }), _jsxs("ul", { className: "list-disc list-inside space-y-1 mt-1", children: [_jsx("li", { children: "Check if all required dependencies are installed" }), _jsx("li", { children: "Verify that the component import paths are correct" }), _jsx("li", { children: "Ensure database connections are working" }), _jsx("li", { children: "Check the browser console for additional error details" })] })] })] })] }) }));
        }
        return this.props.children;
    }
}
export default TechHubErrorBoundary;
