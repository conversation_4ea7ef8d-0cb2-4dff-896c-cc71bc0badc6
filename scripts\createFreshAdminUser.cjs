const fetch = require('node-fetch');
const https = require('https');

// Create agent that accepts self-signed certificates
const agent = new https.Agent({
  rejectUnauthorized: false
});

const BASE_URL = 'https://nxtdotx.co.za/api/auth';

// Use a fresh email and known password
const ADMIN_EMAIL = "<EMAIL>";
const FIRST_NAME = "Dev";
const LAST_NAME = "Admin";
const PASSWORD = "DevPassword123!";

async function createFreshAdminUser() {
  try {
    console.log("Creating fresh admin user for development...");
    console.log("Base URL:", BASE_URL);

    // 1. Register new admin user
    console.log("\n1. Registering new admin user...");
    const registerResp = await fetch(`${BASE_URL}/sign-up/email`, {
      method: "POST",
      headers: { "Content-Type": "application/json" },
      agent: agent,
      body: JSON.stringify({
        email: ADMIN_EMAIL,
        password: PASSWORD,
        firstName: FIRST_NAME,
        lastName: LAST_NAME
      })
    });

    console.log("Registration response status:", registerResp.status);
    const registerText = await registerResp.text();
    console.log("Registration response:", registerText);

    if (!registerResp.ok) {
      if (registerText.includes("already exists")) {
        console.log("✅ User already exists, proceeding to login test...");
      } else {
        throw new Error(`Failed to register: ${registerResp.statusText} - ${registerText}`);
      }
    } else {
      console.log("✅ New admin user registered successfully");
    }

    // 2. Wait a moment for account activation
    console.log("\n2. Waiting for account activation...");
    await new Promise(resolve => setTimeout(resolve, 2000));

    // 3. Test login
    console.log("\n3. Testing login with new credentials...");
    const loginResp = await fetch(`${BASE_URL}/sign-in/email`, {
      method: "POST",
      headers: { "Content-Type": "application/json" },
      agent: agent,
      body: JSON.stringify({
        email: ADMIN_EMAIL,
        password: PASSWORD,
      })
    });

    console.log("Login response status:", loginResp.status);
    const loginText = await loginResp.text();
    console.log("Login response:", loginText);

    if (!loginResp.ok) {
        throw new Error(`Failed to login: ${loginResp.statusText} - ${loginText}`);
    }

    const loginData = JSON.parse(loginText);
    console.log("✅ Admin login successful!");

    console.log("\n🎉 Development admin user ready!");
    console.log("📧 Email:", ADMIN_EMAIL);
    console.log("🔑 Password:", PASSWORD);
    console.log("🌐 Use these credentials in your development environment");
    
  } catch (err) {
    console.error("❌ Failed to create fresh admin user:", err.message || err);
    process.exit(1);
  }
}

createFreshAdminUser();