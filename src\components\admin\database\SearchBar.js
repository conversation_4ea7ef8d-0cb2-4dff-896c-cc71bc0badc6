import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
import { Search } from "lucide-react";
import { Input } from "@/components/ui/input";
const SearchBar = ({ searchTerm, setSearchTerm }) => {
    return (_jsxs("div", { className: "relative w-64", children: [_jsx(Search, { className: "absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" }), _jsx(Input, { placeholder: "Search tables...", className: "pl-8", value: searchTerm, onChange: (e) => setSearchTerm(e.target.value) })] }));
};
export default SearchBar;
