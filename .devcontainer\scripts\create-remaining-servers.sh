#!/bin/bash

# Script to create the remaining MCP servers
set -e

echo "🚀 Creating remaining MCP servers..."

# Create Desktop Commander MCP Server
mkdir -p .devcontainer/mcp-servers/desktop-commander

cat > .devcontainer/mcp-servers/desktop-commander/Dockerfile << 'EOF'
FROM node:20-alpine

WORKDIR /app

# Install dependencies
RUN npm install -g @modelcontextprotocol/sdk

# Copy server files
COPY package.json ./
COPY server.js ./

# Install project dependencies
RUN npm install

# Create data directory
RUN mkdir -p /data && chmod 755 /data

# Expose port
EXPOSE 8085

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:8085/health || exit 1

# Start server
CMD ["node", "server.js"]
EOF

cat > .devcontainer/mcp-servers/desktop-commander/package.json << 'EOF'
{
  "name": "desktop-commander-mcp-server",
  "version": "1.0.0",
  "description": "Desktop Commander MCP Server",
  "main": "server.js",
  "scripts": {
    "start": "node server.js",
    "dev": "nodemon server.js"
  },
  "dependencies": {
    "@modelcontextprotocol/sdk": "^1.0.0",
    "express": "^4.18.0",
    "child_process": "^1.0.0"
  },
  "keywords": ["mcp", "desktop", "commander", "automation"],
  "author": "NXT Level Tech",
  "license": "MIT"
}
EOF

# Create Taskmaster MCP Server
mkdir -p .devcontainer/mcp-servers/taskmaster

cat > .devcontainer/mcp-servers/taskmaster/Dockerfile << 'EOF'
FROM node:20-alpine

WORKDIR /app

# Install dependencies
RUN npm install -g @modelcontextprotocol/sdk

# Copy server files
COPY package.json ./
COPY server.js ./

# Install project dependencies
RUN npm install

# Create data directory
RUN mkdir -p /data && chmod 755 /data

# Expose port
EXPOSE 8086

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:8086/health || exit 1

# Start server
CMD ["node", "server.js"]
EOF

cat > .devcontainer/mcp-servers/taskmaster/package.json << 'EOF'
{
  "name": "taskmaster-mcp-server",
  "version": "1.0.0",
  "description": "Taskmaster MCP Server for task management",
  "main": "server.js",
  "scripts": {
    "start": "node server.js",
    "dev": "nodemon server.js"
  },
  "dependencies": {
    "@modelcontextprotocol/sdk": "^1.0.0",
    "express": "^4.18.0",
    "fs": "^0.0.1-security"
  },
  "keywords": ["mcp", "taskmaster", "tasks", "management"],
  "author": "NXT Level Tech",
  "license": "MIT"
}
EOF

# Create Supabase MCP Server
mkdir -p .devcontainer/mcp-servers/supabase

cat > .devcontainer/mcp-servers/supabase/Dockerfile << 'EOF'
FROM node:20-alpine

WORKDIR /app

# Install dependencies
RUN npm install -g @modelcontextprotocol/sdk @supabase/mcp-server-supabase

# Copy server files
COPY package.json ./
COPY server.js ./

# Install project dependencies
RUN npm install

# Create data directory
RUN mkdir -p /data && chmod 755 /data

# Expose port
EXPOSE 8087

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:8087/health || exit 1

# Start server
CMD ["node", "server.js"]
EOF

cat > .devcontainer/mcp-servers/supabase/package.json << 'EOF'
{
  "name": "supabase-mcp-server",
  "version": "1.0.0",
  "description": "Supabase MCP Server",
  "main": "server.js",
  "scripts": {
    "start": "node server.js",
    "dev": "nodemon server.js"
  },
  "dependencies": {
    "@modelcontextprotocol/sdk": "^1.0.0",
    "@supabase/mcp-server-supabase": "^0.4.1",
    "express": "^4.18.0"
  },
  "keywords": ["mcp", "supabase", "database"],
  "author": "NXT Level Tech",
  "license": "MIT"
}
EOF

# Create Browser Tools MCP Server
mkdir -p .devcontainer/mcp-servers/browser-tools

cat > .devcontainer/mcp-servers/browser-tools/Dockerfile << 'EOF'
FROM node:20-alpine

WORKDIR /app

# Install dependencies
RUN npm install -g @modelcontextprotocol/sdk

# Copy server files
COPY package.json ./
COPY server.js ./

# Install project dependencies
RUN npm install

# Create data directory
RUN mkdir -p /data && chmod 755 /data

# Expose port
EXPOSE 8088

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:8088/health || exit 1

# Start server
CMD ["node", "server.js"]
EOF

cat > .devcontainer/mcp-servers/browser-tools/package.json << 'EOF'
{
  "name": "browser-tools-mcp-server",
  "version": "1.0.0",
  "description": "Browser Tools MCP Server",
  "main": "server.js",
  "scripts": {
    "start": "node server.js",
    "dev": "nodemon server.js"
  },
  "dependencies": {
    "@modelcontextprotocol/sdk": "^1.0.0",
    "express": "^4.18.0",
    "puppeteer": "^21.0.0"
  },
  "keywords": ["mcp", "browser", "automation", "puppeteer"],
  "author": "NXT Level Tech",
  "license": "MIT"
}
EOF

# Create Magic MCP Server
mkdir -p .devcontainer/mcp-servers/magic

cat > .devcontainer/mcp-servers/magic/Dockerfile << 'EOF'
FROM node:20-alpine

WORKDIR /app

# Install dependencies
RUN npm install -g @modelcontextprotocol/sdk

# Copy server files
COPY package.json ./
COPY server.js ./

# Install project dependencies
RUN npm install

# Create data directory
RUN mkdir -p /data && chmod 755 /data

# Expose port
EXPOSE 8089

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:8089/health || exit 1

# Start server
CMD ["node", "server.js"]
EOF

cat > .devcontainer/mcp-servers/magic/package.json << 'EOF'
{
  "name": "magic-mcp-server",
  "version": "1.0.0",
  "description": "21st Dev Magic MCP Server",
  "main": "server.js",
  "scripts": {
    "start": "node server.js",
    "dev": "nodemon server.js"
  },
  "dependencies": {
    "@modelcontextprotocol/sdk": "^1.0.0",
    "express": "^4.18.0",
    "axios": "^1.6.0"
  },
  "keywords": ["mcp", "magic", "21st-dev"],
  "author": "NXT Level Tech",
  "license": "MIT"
}
EOF

# Create Neo4j MCP Server
mkdir -p .devcontainer/mcp-servers/neo4j

cat > .devcontainer/mcp-servers/neo4j/Dockerfile << 'EOF'
FROM node:20-alpine

WORKDIR /app

# Install dependencies
RUN npm install -g @modelcontextprotocol/sdk

# Copy server files
COPY package.json ./
COPY server.js ./

# Install project dependencies
RUN npm install

# Create data directory
RUN mkdir -p /data && chmod 755 /data

# Expose port
EXPOSE 8090

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:8090/health || exit 1

# Start server
CMD ["node", "server.js"]
EOF

cat > .devcontainer/mcp-servers/neo4j/package.json << 'EOF'
{
  "name": "neo4j-mcp-server",
  "version": "1.0.0",
  "description": "Neo4j Aura MCP Server",
  "main": "server.js",
  "scripts": {
    "start": "node server.js",
    "dev": "nodemon server.js"
  },
  "dependencies": {
    "@modelcontextprotocol/sdk": "^1.0.0",
    "express": "^4.18.0",
    "neo4j-driver": "^5.15.0"
  },
  "keywords": ["mcp", "neo4j", "graph", "database"],
  "author": "NXT Level Tech",
  "license": "MIT"
}
EOF

echo "✅ All remaining MCP server directories and configurations created!"
echo "📝 Next: Run the server implementation script to create the actual server.js files"
