import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
import { useState, useEffect } from 'react';
import { Search, X } from 'lucide-react';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
export const DocumentSearchBar = ({ onSearch }) => {
    const [searchTerm, setSearchTerm] = useState('');
    const [debouncedSearchTerm, setDebouncedSearchTerm] = useState('');
    useEffect(() => {
        const timer = setTimeout(() => {
            setDebouncedSearchTerm(searchTerm);
            onSearch(searchTerm);
        }, 300);
        return () => clearTimeout(timer);
    }, [searchTerm, onSearch]);
    const handleSearch = (e) => {
        e.preventDefault();
        onSearch(searchTerm);
    };
    const clearSearch = () => {
        setSearchTerm('');
        onSearch('');
    };
    return (_jsxs("form", { onSubmit: handleSearch, className: "relative w-full", children: [_jsx(Search, { className: "absolute left-2.5 top-2.5 h-4 w-4 text-gray-500 dark:text-gray-400" }), _jsx(Input, { type: "search", placeholder: "Search documentation...", className: "w-full pl-9 pr-10 bg-white/50 dark:bg-gray-900/50 border border-gray-300 dark:border-gray-700", value: searchTerm, onChange: (e) => setSearchTerm(e.target.value) }), searchTerm && (_jsxs(Button, { type: "button", variant: "ghost", size: "icon", className: "absolute right-1 top-1 h-7 w-7", onClick: clearSearch, children: [_jsx(X, { className: "h-4 w-4 text-gray-500 dark:text-gray-400" }), _jsx("span", { className: "sr-only", children: "Clear search" })] }))] }));
};
