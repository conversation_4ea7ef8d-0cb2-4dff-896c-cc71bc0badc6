import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
export const LandingBackground = ({ children, backgroundImageUrl }) => {
    return (_jsxs("div", { className: "h-screen w-full flex flex-col items-center justify-center relative overflow-hidden", children: [_jsx("div", { className: "absolute inset-0 z-0 bg-gradient-to-br from-blue-900 via-purple-900 to-indigo-900", style: {
                    backgroundImage: `url('${backgroundImageUrl}')`,
                    backgroundSize: 'cover',
                    backgroundPosition: 'center',
                    backgroundRepeat: 'no-repeat',
                    backgroundAttachment: 'fixed'
                } }), _jsx("div", { className: "absolute inset-0 z-0 bg-gradient-to-br from-black/40 via-transparent to-black/60" }), _jsx("div", { className: "absolute inset-0 z-0 opacity-10", children: _jsx("div", { className: "absolute top-0 left-0 w-full h-full bg-gradient-to-r from-transparent via-white/5 to-transparent animate-pulse" }) }), _jsx("div", { className: "relative z-10 flex flex-col items-center justify-center min-h-screen px-4", children: children })] }));
};
