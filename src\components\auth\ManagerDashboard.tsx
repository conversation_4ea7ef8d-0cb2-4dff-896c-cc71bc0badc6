import React from 'react';
import { useBetterAuth } from '@/providers/BetterAuthProvider';
import { UserRole } from '@/utils/rbac/permissions';

export function ManagerDashboard() {
  const { isRoleOrHigher, user } = useBetterAuth();

  // Check if user is manager or higher
  if (!isRoleOrHigher(UserRole.MANAGER)) {
    return <div>Access denied. Manager privileges required.</div>;
  }

  return (
    <div>
      <h1>Manager Dashboard</h1>
      <p>Welcome, {user?.username || 'Manager'}!</p>
    </div>
  );
}
