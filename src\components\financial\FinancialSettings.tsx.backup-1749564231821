import React from 'react';
import { Card, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { Separator } from '@/components/ui/separator';
import { Settings, Save, RefreshCw } from 'lucide-react';

/**
 * Financial Settings Component
 * Provides configuration options for the financial module
 */
const FinancialSettings: React.FC = () => {
  return (
    <div className="container mx-auto p-6 space-y-6">
      <div className="flex items-center gap-2 mb-6">
        <Settings className="h-6 w-6" />
        <h1 className="text-2xl font-bold">Financial Settings</h1>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* General Settings */}
        <Card>
          <CardHeader>
            <CardTitle>General Settings</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="company-name">Company Name</Label>
              <Input id="company-name" placeholder="Enter company name" />
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="fiscal-year">Fiscal Year Start</Label>
              <Input id="fiscal-year" type="date" />
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="currency">Default Currency</Label>
              <Input id="currency" placeholder="USD" />
            </div>
            
            <div className="flex items-center space-x-2">
              <Switch id="multi-currency" />
              <Label htmlFor="multi-currency">Enable Multi-Currency</Label>
            </div>
          </CardContent>
        </Card>

        {/* Accounting Settings */}
        <Card>
          <CardHeader>
            <CardTitle>Accounting Settings</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="chart-template">Chart of Accounts Template</Label>
              <Input id="chart-template" placeholder="Standard Business" />
            </div>
            
            <div className="flex items-center space-x-2">
              <Switch id="auto-reconcile" />
              <Label htmlFor="auto-reconcile">Auto-Reconciliation</Label>
            </div>
            
            <div className="flex items-center space-x-2">
              <Switch id="require-approval" />
              <Label htmlFor="require-approval">Require Journal Entry Approval</Label>
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="retention-period">Data Retention Period (Years)</Label>
              <Input id="retention-period" type="number" placeholder="7" />
            </div>
          </CardContent>
        </Card>

        {/* Tax Settings */}
        <Card>
          <CardHeader>
            <CardTitle>Tax Settings</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="tax-jurisdiction">Tax Jurisdiction</Label>
              <Input id="tax-jurisdiction" placeholder="Enter jurisdiction" />
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="default-tax-rate">Default Tax Rate (%)</Label>
              <Input id="default-tax-rate" type="number" placeholder="15" />
            </div>
            
            <div className="flex items-center space-x-2">
              <Switch id="auto-tax-calc" />
              <Label htmlFor="auto-tax-calc">Automatic Tax Calculation</Label>
            </div>
          </CardContent>
        </Card>

        {/* Reporting Settings */}
        <Card>
          <CardHeader>
            <CardTitle>Reporting Settings</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="report-format">Default Report Format</Label>
              <Input id="report-format" placeholder="PDF" />
            </div>
            
            <div className="flex items-center space-x-2">
              <Switch id="auto-backup" />
              <Label htmlFor="auto-backup">Automatic Report Backup</Label>
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="backup-frequency">Backup Frequency</Label>
              <Input id="backup-frequency" placeholder="Daily" />
            </div>
          </CardContent>
        </Card>
      </div>

      <Separator />

      {/* Action Buttons */}
      <div className="flex justify-end space-x-4">
        <Button variant="outline">
          <RefreshCw className="h-4 w-4 mr-2" />
          Reset to Defaults
        </Button>
        <Button>
          <Save className="h-4 w-4 mr-2" />
          Save Settings
        </Button>
      </div>
    </div>
  );
};

export default FinancialSettings;
