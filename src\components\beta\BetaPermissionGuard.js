import { jsx as _jsx, Fragment as _Fragment } from "react/jsx-runtime";
import { Navigate } from 'react-router-dom';
import { useBetterAuth } from '@/providers/BetterAuthProvider';
/**
 * Permission guard component that specifically checks for Beta module access
 * It restricts access to Beta features based on user permissions
 */
const BetaPermissionGuard = ({ betaModule, children, fallbackPath = '/unauthorized' }) => {
    const { isAuthenticated, hasPermission, user } = useBetterAuth();
    // Log access check for debugging
    console.log(`Beta permission check - Auth: ${isAuthenticated}, Module: ${betaModule}`);
    if (!isAuthenticated) {
        console.log("Not authenticated, redirecting to landing");
        return _jsx(Navigate, { to: "/landing", replace: true });
    }
    // Determine the required permission based on beta module
    const requiredPermission = betaModule === 'beta1'
        ? 'modules.data'
        : 'modules.loyalty';
    // Admin users bypass all permission checks
    if (user?.role === 'admin') {
        return _jsx(_Fragment, { children: children });
    }
    // For others, check specific module permission
    if (!hasPermission(requiredPermission)) {
        console.log(`Missing permission for beta module: ${requiredPermission}`);
        return _jsx(Navigate, { to: fallbackPath, replace: true });
    }
    return _jsx(_Fragment, { children: children });
};
export default BetaPermissionGuard;
