#!/usr/bin/env node

const { Client } = require('pg');
require('dotenv').config();

async function checkDatabaseDiscrepancy() {
  const client = new Client({
    connectionString: process.env.DATABASE_URL,
    ssl: process.env.NODE_ENV === 'production' ? { rejectUnauthorized: false } : false
  });

  try {
    await client.connect();
    console.log('✅ Connected to database\n');

    // First, let's check ALL users in the database
    console.log('🔍 Checking ALL users in the database:');
    const allUsersQuery = `
      SELECT u.id, u.email, u."emailVerified", u."createdAt",
             COUNT(a.id) as account_count,
             BOOL_OR(a.password IS NOT NULL) as has_password
      FROM "user" u
      LEFT JOIN "account" a ON u.id = a."userId"
      GROUP BY u.id, u.email, u."emailVerified", u."createdAt"
      ORDER BY u."createdAt" DESC
      LIMIT 20;
    `;
    
    const allUsersResult = await client.query(allUsersQuery);
    console.log(`Found ${allUsersResult.rows.length} users (showing latest 20):\n`);
    
    allUsersResult.rows.forEach(user => {
      console.log(`📧 ${user.email}`);
      console.log(`   ID: ${user.id}`);
      console.log(`   Verified: ${user.emailVerified}`);
      console.log(`   Has Password: ${user.has_password}`);
      console.log(`   Accounts: ${user.account_count}`);
      console.log(`   Created: ${user.createdAt}`);
      console.log('');
    });

    // Now check specifically for our test users
    console.log('\n🔍 Checking for specific test users:');
    const testEmails = [
      '<EMAIL>',
      '<EMAIL>', 
      '<EMAIL>'
    ];

    const testUsersQuery = `
      SELECT u.id, u.email, u."emailVerified", u."createdAt",
             COUNT(a.id) as account_count,
             BOOL_OR(a.password IS NOT NULL) as has_password
      FROM "user" u
      LEFT JOIN "account" a ON u.id = a."userId"
      WHERE u.email = ANY($1)
      GROUP BY u.id, u.email, u."emailVerified", u."createdAt"
      ORDER BY u.email;
    `;

    const testUsersResult = await client.query(testUsersQuery, [testEmails]);
    
    console.log(`Looking for: ${testEmails.join(', ')}\n`);
    
    if (testUsersResult.rows.length === 0) {
      console.log('❌ None of the test users were found!');
    } else {
      console.log(`Found ${testUsersResult.rows.length} of ${testEmails.length} test users:\n`);
      testUsersResult.rows.forEach(user => {
        console.log(`✅ Found: ${user.email}`);
        console.log(`   ID: ${user.id}`);
        console.log(`   Has Password: ${user.has_password}`);
      });
      
      // Check which ones are missing
      const foundEmails = testUsersResult.rows.map(r => r.email);
      const missingEmails = testEmails.filter(email => !foundEmails.includes(email));
      
      if (missingEmails.length > 0) {
        console.log(`\n❌ Missing users: ${missingEmails.join(', ')}`);
      }
    }

    // Check account providers for found users
    console.log('\n🔍 Checking account providers for test users:');
    const accountsQuery = `
      SELECT u.email, a."providerId", a."providerAccountId",
             a.password IS NOT NULL as has_password,
             LENGTH(a.password) as password_length
      FROM "account" a
      JOIN "user" u ON a."userId" = u.id
      WHERE u.email = ANY($1)
      ORDER BY u.email, a."providerId";
    `;

    const accountsResult = await client.query(accountsQuery, [testEmails]);
    
    if (accountsResult.rows.length === 0) {
      console.log('❌ No accounts found for test users');
    } else {
      accountsResult.rows.forEach(account => {
        console.log(`\n📧 ${account.email}`);
        console.log(`   Provider: ${account.providerId}`);
        console.log(`   Provider Account ID: ${account.providerAccountId || 'N/A'}`);
        console.log(`   Has Password: ${account.has_password}`);
        console.log(`   Password Length: ${account.password_length || 'N/A'}`);
      });
    }

    // Check for any users with similar emails (typos?)
    console.log('\n🔍 Checking for similar email patterns:');
    const similarQuery = `
      SELECT email FROM "user"
      WHERE email LIKE '%admin%' OR email LIKE '%test%' OR email LIKE '%example%'
      ORDER BY email;
    `;
    
    const similarResult = await client.query(similarQuery);
    if (similarResult.rows.length > 0) {
      console.log('Found users with similar patterns:');
      similarResult.rows.forEach(row => {
        console.log(`   - ${row.email}`);
      });
    }

  } catch (error) {
    console.error('❌ Database error:', error.message);
    console.error('Error details:', error);
  } finally {
    await client.end();
  }
}

checkDatabaseDiscrepancy().catch(console.error);