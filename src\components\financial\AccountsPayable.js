import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, } from '@/components/ui/dialog';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue, } from '@/components/ui/select';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Label } from '@/components/ui/label';
import { Plus, Search, DollarSign, Calendar, AlertTriangle, CheckCircle, Clock, Loader2, RefreshCw, FileText, CreditCard } from 'lucide-react';
import { InvoiceService, PaymentService } from '../../services/financial';
import { InvoiceStatus, PaymentStatus } from '../../types/financial';
const AccountsPayable = () => {
    const [bills, setBills] = useState([]);
    const [payments, setPayments] = useState([]);
    const [summary, setSummary] = useState({});
    const [loading, setLoading] = useState(true);
    const [searchTerm, setSearchTerm] = useState('');
    const [statusFilter, setStatusFilter] = useState('all');
    const [isPaymentDialogOpen, setIsPaymentDialogOpen] = useState(false);
    const [selectedBill, setSelectedBill] = useState(null);
    const [paymentAmount, setPaymentAmount] = useState(0);
    useEffect(() => {
        loadData();
    }, []);
    const loadData = async () => {
        try {
            setLoading(true);
            const [billList, paymentList] = await Promise.all([
                InvoiceService.getInvoices(),
                PaymentService.getPayments()
            ]);
            setBills(billList);
            setPayments(paymentList);
            // Calculate summary
            const totalOutstanding = billList
                .filter(bill => bill.status !== InvoiceStatus.PAID)
                .reduce((sum, bill) => sum + (bill.totalAmount - (bill.paidAmount || 0)), 0);
            const overdueBills = billList.filter(bill => bill.status === InvoiceStatus.OVERDUE);
            const dueThisWeek = billList.filter(bill => {
                const dueDate = new Date(bill.dueDate);
                const weekFromNow = new Date();
                weekFromNow.setDate(weekFromNow.getDate() + 7);
                return dueDate <= weekFromNow && bill.status !== InvoiceStatus.PAID;
            });
            setSummary({
                totalOutstanding,
                overdueCount: overdueBills.length,
                overdueAmount: overdueBills.reduce((sum, bill) => sum + (bill.totalAmount - (bill.paidAmount || 0)), 0),
                dueThisWeekCount: dueThisWeek.length,
                dueThisWeekAmount: dueThisWeek.reduce((sum, bill) => sum + (bill.totalAmount - (bill.paidAmount || 0)), 0)
            });
        }
        catch (error) {
            console.error('Error loading accounts payable data:', error);
        }
        finally {
            setLoading(false);
        }
    };
    const handlePayBill = async () => {
        if (!selectedBill)
            return;
        try {
            await PaymentService.createPayment({
                paymentNumber: `PAY-${Date.now()}`,
                type: 'OUTGOING',
                method: 'BANK_TRANSFER',
                status: 'COMPLETED',
                entityId: selectedBill.customerId,
                entityType: 'VENDOR',
                amount: paymentAmount,
                currency: 'USD',
                date: new Date(),
                allocations: [{
                        invoiceId: selectedBill.id,
                        amount: paymentAmount
                    }]
            });
            setIsPaymentDialogOpen(false);
            setSelectedBill(null);
            setPaymentAmount(0);
            loadData();
        }
        catch (error) {
            console.error('Error creating payment:', error);
        }
    };
    const formatCurrency = (amount) => {
        return new Intl.NumberFormat('en-US', {
            style: 'currency',
            currency: 'USD'
        }).format(amount);
    };
    const formatDate = (date) => {
        const dateObj = typeof date === 'string' ? new Date(date) : date;
        return new Intl.DateTimeFormat('en-US', {
            year: 'numeric',
            month: 'short',
            day: 'numeric'
        }).format(dateObj);
    };
    const getStatusColor = (status) => {
        switch (status) {
            case InvoiceStatus.PAID:
                return 'bg-green-100 text-green-800';
            case InvoiceStatus.OVERDUE:
                return 'bg-red-100 text-red-800';
            case InvoiceStatus.PARTIALLY_PAID:
                return 'bg-yellow-100 text-yellow-800';
            case InvoiceStatus.SENT:
                return 'bg-blue-100 text-blue-800';
            default:
                return 'bg-gray-100 text-gray-800';
        }
    };
    const getStatusIcon = (status) => {
        switch (status) {
            case InvoiceStatus.PAID:
                return _jsx(CheckCircle, { className: "h-4 w-4" });
            case InvoiceStatus.OVERDUE:
                return _jsx(AlertTriangle, { className: "h-4 w-4" });
            case InvoiceStatus.PARTIALLY_PAID:
                return _jsx(Clock, { className: "h-4 w-4" });
            default:
                return _jsx(FileText, { className: "h-4 w-4" });
        }
    };
    const filteredBills = bills.filter(bill => {
        const matchesSearch = bill.invoiceNumber.toLowerCase().includes(searchTerm.toLowerCase());
        const matchesStatus = statusFilter === 'all' || bill.status === statusFilter;
        return matchesSearch && matchesStatus;
    });
    if (loading) {
        return (_jsx("div", { className: "flex items-center justify-center h-64", children: _jsxs("div", { className: "flex items-center space-x-2", children: [_jsx(Loader2, { className: "h-6 w-6 animate-spin" }), _jsx("span", { children: "Loading accounts payable data..." })] }) }));
    }
    return (_jsxs("div", { className: "space-y-6", children: [_jsxs("div", { className: "flex justify-between items-center", children: [_jsxs("div", { children: [_jsx("h1", { className: "text-3xl font-bold tracking-tight", children: "Accounts Payable" }), _jsx("p", { className: "text-muted-foreground", children: "Manage vendor bills, payments, and outstanding obligations" })] }), _jsxs("div", { className: "flex space-x-2", children: [_jsxs(Button, { variant: "outline", onClick: loadData, children: [_jsx(RefreshCw, { className: "h-4 w-4 mr-2" }), "Refresh"] }), _jsxs(Button, { children: [_jsx(Plus, { className: "h-4 w-4 mr-2" }), "New Bill"] })] })] }), _jsxs("div", { className: "grid grid-cols-1 md:grid-cols-4 gap-4", children: [_jsxs(Card, { children: [_jsxs(CardHeader, { className: "flex flex-row items-center justify-between space-y-0 pb-2", children: [_jsx(CardTitle, { className: "text-sm font-medium", children: "Total Outstanding" }), _jsx(DollarSign, { className: "h-4 w-4 text-muted-foreground" })] }), _jsxs(CardContent, { children: [_jsx("div", { className: "text-2xl font-bold", children: formatCurrency(summary.totalOutstanding || 0) }), _jsxs("p", { className: "text-xs text-muted-foreground", children: ["Across ", bills.filter(b => b.status !== InvoiceStatus.PAID).length, " bills"] })] })] }), _jsxs(Card, { children: [_jsxs(CardHeader, { className: "flex flex-row items-center justify-between space-y-0 pb-2", children: [_jsx(CardTitle, { className: "text-sm font-medium", children: "Overdue" }), _jsx(AlertTriangle, { className: "h-4 w-4 text-red-500" })] }), _jsxs(CardContent, { children: [_jsx("div", { className: "text-2xl font-bold text-red-600", children: formatCurrency(summary.overdueAmount || 0) }), _jsxs("p", { className: "text-xs text-muted-foreground", children: [summary.overdueCount || 0, " overdue bills"] })] })] }), _jsxs(Card, { children: [_jsxs(CardHeader, { className: "flex flex-row items-center justify-between space-y-0 pb-2", children: [_jsx(CardTitle, { className: "text-sm font-medium", children: "Due This Week" }), _jsx(Calendar, { className: "h-4 w-4 text-orange-500" })] }), _jsxs(CardContent, { children: [_jsx("div", { className: "text-2xl font-bold text-orange-600", children: formatCurrency(summary.dueThisWeekAmount || 0) }), _jsxs("p", { className: "text-xs text-muted-foreground", children: [summary.dueThisWeekCount || 0, " bills due"] })] })] }), _jsxs(Card, { children: [_jsxs(CardHeader, { className: "flex flex-row items-center justify-between space-y-0 pb-2", children: [_jsx(CardTitle, { className: "text-sm font-medium", children: "Paid This Month" }), _jsx(CheckCircle, { className: "h-4 w-4 text-green-500" })] }), _jsxs(CardContent, { children: [_jsx("div", { className: "text-2xl font-bold text-green-600", children: formatCurrency(payments.reduce((sum, p) => sum + p.amount, 0)) }), _jsxs("p", { className: "text-xs text-muted-foreground", children: [payments.length, " payments made"] })] })] })] }), _jsxs(Tabs, { defaultValue: "bills", className: "space-y-4", children: [_jsxs(TabsList, { children: [_jsx(TabsTrigger, { value: "bills", children: "Outstanding Bills" }), _jsx(TabsTrigger, { value: "payments", children: "Recent Payments" }), _jsx(TabsTrigger, { value: "aged", children: "Aged Payables" })] }), _jsxs(TabsContent, { value: "bills", className: "space-y-4", children: [_jsxs("div", { className: "flex space-x-4", children: [_jsx("div", { className: "flex-1", children: _jsxs("div", { className: "relative", children: [_jsx(Search, { className: "absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" }), _jsx(Input, { placeholder: "Search bills...", value: searchTerm, onChange: (e) => setSearchTerm(e.target.value), className: "pl-8" })] }) }), _jsxs(Select, { value: statusFilter, onValueChange: setStatusFilter, children: [_jsx(SelectTrigger, { className: "w-[180px]", children: _jsx(SelectValue, { placeholder: "Filter by status" }) }), _jsxs(SelectContent, { children: [_jsx(SelectItem, { value: "all", children: "All Statuses" }), _jsx(SelectItem, { value: InvoiceStatus.SENT, children: "Sent" }), _jsx(SelectItem, { value: InvoiceStatus.PARTIALLY_PAID, children: "Partially Paid" }), _jsx(SelectItem, { value: InvoiceStatus.OVERDUE, children: "Overdue" }), _jsx(SelectItem, { value: InvoiceStatus.PAID, children: "Paid" })] })] })] }), _jsxs(Card, { children: [_jsxs(CardHeader, { children: [_jsx(CardTitle, { children: "Vendor Bills" }), _jsx(CardDescription, { children: "Outstanding bills and payment obligations" })] }), _jsx(CardContent, { children: _jsxs(Table, { children: [_jsx(TableHeader, { children: _jsxs(TableRow, { children: [_jsx(TableHead, { children: "Bill Number" }), _jsx(TableHead, { children: "Vendor" }), _jsx(TableHead, { children: "Issue Date" }), _jsx(TableHead, { children: "Due Date" }), _jsx(TableHead, { className: "text-right", children: "Amount" }), _jsx(TableHead, { className: "text-right", children: "Outstanding" }), _jsx(TableHead, { children: "Status" }), _jsx(TableHead, { children: "Actions" })] }) }), _jsx(TableBody, { children: filteredBills.map((bill) => (_jsxs(TableRow, { children: [_jsx(TableCell, { className: "font-medium", children: bill.invoiceNumber }), _jsx(TableCell, { children: bill.customerId }), _jsx(TableCell, { children: formatDate(bill.issueDate) }), _jsx(TableCell, { children: formatDate(bill.dueDate) }), _jsx(TableCell, { className: "text-right", children: formatCurrency(bill.totalAmount) }), _jsx(TableCell, { className: "text-right font-medium", children: formatCurrency(bill.totalAmount - (bill.paidAmount || 0)) }), _jsx(TableCell, { children: _jsx(Badge, { className: getStatusColor(bill.status), children: _jsxs("div", { className: "flex items-center space-x-1", children: [getStatusIcon(bill.status), _jsx("span", { children: bill.status })] }) }) }), _jsx(TableCell, { children: bill.status !== InvoiceStatus.PAID && (_jsxs(Button, { variant: "outline", size: "sm", onClick: () => {
                                                                        setSelectedBill(bill);
                                                                        setPaymentAmount(bill.totalAmount - (bill.paidAmount || 0));
                                                                        setIsPaymentDialogOpen(true);
                                                                    }, children: [_jsx(CreditCard, { className: "h-4 w-4 mr-1" }), "Pay"] })) })] }, bill.id))) })] }) })] })] }), _jsx(TabsContent, { value: "payments", className: "space-y-4", children: _jsxs(Card, { children: [_jsxs(CardHeader, { children: [_jsx(CardTitle, { children: "Recent Payments" }), _jsx(CardDescription, { children: "Recently made vendor payments" })] }), _jsx(CardContent, { children: _jsxs(Table, { children: [_jsx(TableHeader, { children: _jsxs(TableRow, { children: [_jsx(TableHead, { children: "Payment Number" }), _jsx(TableHead, { children: "Vendor" }), _jsx(TableHead, { children: "Date" }), _jsx(TableHead, { children: "Method" }), _jsx(TableHead, { className: "text-right", children: "Amount" }), _jsx(TableHead, { children: "Status" })] }) }), _jsx(TableBody, { children: payments.map((payment) => (_jsxs(TableRow, { children: [_jsx(TableCell, { className: "font-medium", children: payment.paymentNumber }), _jsx(TableCell, { children: payment.entityId }), _jsx(TableCell, { children: formatDate(payment.date) }), _jsx(TableCell, { children: payment.method }), _jsx(TableCell, { className: "text-right", children: formatCurrency(payment.amount) }), _jsx(TableCell, { children: _jsx(Badge, { variant: payment.status === PaymentStatus.COMPLETED ? "default" : "secondary", children: payment.status }) })] }, payment.id))) })] }) })] }) }), _jsx(TabsContent, { value: "aged", className: "space-y-4", children: _jsxs(Card, { children: [_jsxs(CardHeader, { children: [_jsx(CardTitle, { children: "Aged Payables Report" }), _jsx(CardDescription, { children: "Bills categorized by age for better cash flow management" })] }), _jsx(CardContent, { children: _jsxs("div", { className: "text-center py-8 text-muted-foreground", children: [_jsx(Calendar, { className: "h-12 w-12 mx-auto mb-4 opacity-50" }), _jsx("p", { children: "Aged payables report will be displayed here" }), _jsx("p", { className: "text-sm", children: "Breakdown by 30, 60, 90+ days" })] }) })] }) })] }), _jsx(Dialog, { open: isPaymentDialogOpen, onOpenChange: setIsPaymentDialogOpen, children: _jsxs(DialogContent, { className: "sm:max-w-[425px]", children: [_jsxs(DialogHeader, { children: [_jsx(DialogTitle, { children: "Record Payment" }), _jsxs(DialogDescription, { children: ["Record a payment for bill ", selectedBill?.invoiceNumber] })] }), _jsxs("div", { className: "grid gap-4 py-4", children: [_jsxs("div", { className: "grid grid-cols-4 items-center gap-4", children: [_jsx(Label, { htmlFor: "amount", className: "text-right", children: "Amount" }), _jsx(Input, { id: "amount", type: "number", value: paymentAmount, onChange: (e) => setPaymentAmount(parseFloat(e.target.value) || 0), className: "col-span-3" })] }), _jsxs("div", { className: "grid grid-cols-4 items-center gap-4", children: [_jsx(Label, { className: "text-right", children: "Outstanding" }), _jsx("div", { className: "col-span-3 text-sm text-muted-foreground", children: selectedBill && formatCurrency(selectedBill.totalAmount - (selectedBill.paidAmount || 0)) })] })] }), _jsx(DialogFooter, { children: _jsx(Button, { onClick: handlePayBill, children: "Record Payment" }) })] }) })] }));
};
export default AccountsPayable;
