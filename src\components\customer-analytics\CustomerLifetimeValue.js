import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
import { useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, BarChart, Bar, ComposedChart, Area } from 'recharts';
import { DollarSign, TrendingUp, Calculator, Target, Users, Calendar } from 'lucide-react';
const CustomerLifetimeValue = ({ clvData = [], trendData = [] }) => {
    const [selectedSegment, setSelectedSegment] = useState('all');
    // Default CLV data if none provided
    const defaultCLVData = [
        {
            segment: 'VIP Champions',
            currentCLV: 4200,
            projectedCLV: 5800,
            customerCount: 78,
            avgOrderValue: 680,
            purchaseFrequency: 8.2,
            customerLifespan: 36,
            totalValue: 327600
        },
        {
            segment: 'Loyal Customers',
            currentCLV: 2850,
            projectedCLV: 3400,
            customerCount: 156,
            avgOrderValue: 425,
            purchaseFrequency: 6.7,
            customerLifespan: 24,
            totalValue: 444600
        },
        {
            segment: 'Potential Loyalists',
            currentCLV: 1650,
            projectedCLV: 2200,
            customerCount: 342,
            avgOrderValue: 285,
            purchaseFrequency: 4.2,
            customerLifespan: 18,
            totalValue: 564300
        },
        {
            segment: 'New Customers',
            currentCLV: 850,
            projectedCLV: 1400,
            customerCount: 189,
            avgOrderValue: 145,
            purchaseFrequency: 1.8,
            customerLifespan: 12,
            totalValue: 160650
        }
    ];
    // Default trend data if none provided
    const defaultTrendData = [
        { month: 'Jan', clv: 1250, newCustomers: 45, retainedCustomers: 420 },
        { month: 'Feb', clv: 1320, newCustomers: 52, retainedCustomers: 435 },
        { month: 'Mar', clv: 1180, newCustomers: 38, retainedCustomers: 410 },
        { month: 'Apr', clv: 1450, newCustomers: 67, retainedCustomers: 465 },
        { month: 'May', clv: 1380, newCustomers: 58, retainedCustomers: 445 },
        { month: 'Jun', clv: 1520, newCustomers: 72, retainedCustomers: 485 }
    ];
    const clvSegments = clvData.length > 0 ? clvData : defaultCLVData;
    const clvTrends = trendData.length > 0 ? trendData : defaultTrendData;
    // Calculate overall metrics
    const totalCustomers = clvSegments.reduce((sum, segment) => sum + segment.customerCount, 0);
    const totalCurrentValue = clvSegments.reduce((sum, segment) => sum + segment.totalValue, 0);
    const averageCLV = totalCurrentValue / totalCustomers;
    const totalProjectedValue = clvSegments.reduce((sum, segment) => sum + (segment.projectedCLV * segment.customerCount), 0);
    // CLV calculation formula display
    const calculateCLV = (aov, frequency, lifespan) => {
        return aov * frequency * lifespan;
    };
    const clvMetrics = [
        {
            title: 'Average CLV',
            value: `$${averageCLV.toFixed(0)}`,
            change: '+12.5%',
            icon: DollarSign,
            color: 'text-green-600',
            bgColor: 'bg-green-100'
        },
        {
            title: 'Total Customer Value',
            value: `$${(totalCurrentValue / 1000000).toFixed(1)}M`,
            change: '+8.3%',
            icon: Target,
            color: 'text-blue-600',
            bgColor: 'bg-blue-100'
        },
        {
            title: 'Projected Growth',
            value: `$${((totalProjectedValue - totalCurrentValue) / 1000).toFixed(0)}K`,
            change: '+15.2%',
            icon: TrendingUp,
            color: 'text-purple-600',
            bgColor: 'bg-purple-100'
        },
        {
            title: 'High-Value Customers',
            value: `${clvSegments.filter(s => s.currentCLV > 2000).reduce((sum, s) => sum + s.customerCount, 0)}`,
            change: '+6.7%',
            icon: Users,
            color: 'text-orange-600',
            bgColor: 'bg-orange-100'
        }
    ];
    return (_jsxs("div", { className: "space-y-6", children: [_jsx("div", { className: "grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4", children: clvMetrics.map((metric, index) => (_jsx(Card, { children: _jsx(CardContent, { className: "p-4", children: _jsxs("div", { className: "flex items-center justify-between", children: [_jsxs("div", { children: [_jsx("p", { className: "text-xs font-medium text-gray-600", children: metric.title }), _jsx("p", { className: "text-xl font-bold text-gray-900", children: metric.value }), _jsxs("div", { className: "flex items-center mt-1", children: [_jsx(TrendingUp, { className: "h-3 w-3 text-green-500 mr-1" }), _jsx("span", { className: "text-xs text-green-600", children: metric.change })] })] }), _jsx("div", { className: `p-2 rounded-full ${metric.bgColor}`, children: _jsx(metric.icon, { className: `h-4 w-4 ${metric.color}` }) })] }) }) }, index))) }), _jsxs("div", { className: "grid grid-cols-1 lg:grid-cols-2 gap-6", children: [_jsxs(Card, { children: [_jsx(CardHeader, { children: _jsx(CardTitle, { children: "CLV by Customer Segment" }) }), _jsx(CardContent, { children: _jsx(ResponsiveContainer, { width: "100%", height: 300, children: _jsxs(BarChart, { data: clvSegments, children: [_jsx(CartesianGrid, { strokeDasharray: "3 3" }), _jsx(XAxis, { dataKey: "segment", angle: -45, textAnchor: "end", height: 80 }), _jsx(YAxis, {}), _jsx(Tooltip, { formatter: (value) => [`$${value}`, 'CLV'] }), _jsx(Bar, { dataKey: "currentCLV", fill: "#3b82f6", name: "Current CLV" }), _jsx(Bar, { dataKey: "projectedCLV", fill: "#8b5cf6", name: "Projected CLV" })] }) }) })] }), _jsxs(Card, { children: [_jsx(CardHeader, { children: _jsx(CardTitle, { children: "CLV Trends Over Time" }) }), _jsx(CardContent, { children: _jsx(ResponsiveContainer, { width: "100%", height: 300, children: _jsxs(ComposedChart, { data: clvTrends, children: [_jsx(CartesianGrid, { strokeDasharray: "3 3" }), _jsx(XAxis, { dataKey: "month" }), _jsx(YAxis, { yAxisId: "left" }), _jsx(YAxis, { yAxisId: "right", orientation: "right" }), _jsx(Tooltip, {}), _jsx(Area, { yAxisId: "left", type: "monotone", dataKey: "clv", fill: "#3b82f6", fillOpacity: 0.3, stroke: "#3b82f6" }), _jsx(Bar, { yAxisId: "right", dataKey: "newCustomers", fill: "#10b981" })] }) }) })] })] }), _jsxs(Card, { children: [_jsx(CardHeader, { children: _jsxs(CardTitle, { className: "flex items-center", children: [_jsx(Calculator, { className: "h-5 w-5 mr-2" }), "CLV Calculation Formula"] }) }), _jsxs(CardContent, { children: [_jsxs("div", { className: "bg-blue-50 rounded-lg p-4 mb-4", children: [_jsx("h3", { className: "font-semibold text-blue-900 mb-2", children: "Customer Lifetime Value Formula:" }), _jsx("div", { className: "text-blue-800 font-mono text-lg", children: "CLV = Average Order Value \u00D7 Purchase Frequency \u00D7 Customer Lifespan" })] }), _jsxs("div", { className: "grid grid-cols-1 md:grid-cols-3 gap-4", children: [_jsxs("div", { className: "text-center p-4 bg-gray-50 rounded-lg", children: [_jsx(DollarSign, { className: "h-8 w-8 mx-auto mb-2 text-green-600" }), _jsx("h4", { className: "font-semibold", children: "Average Order Value" }), _jsx("p", { className: "text-sm text-gray-600", children: "Total revenue \u00F7 Number of orders" })] }), _jsxs("div", { className: "text-center p-4 bg-gray-50 rounded-lg", children: [_jsx(Calendar, { className: "h-8 w-8 mx-auto mb-2 text-blue-600" }), _jsx("h4", { className: "font-semibold", children: "Purchase Frequency" }), _jsx("p", { className: "text-sm text-gray-600", children: "Number of orders \u00F7 Number of customers" })] }), _jsxs("div", { className: "text-center p-4 bg-gray-50 rounded-lg", children: [_jsx(Users, { className: "h-8 w-8 mx-auto mb-2 text-purple-600" }), _jsx("h4", { className: "font-semibold", children: "Customer Lifespan" }), _jsx("p", { className: "text-sm text-gray-600", children: "Average time customer remains active" })] })] })] })] }), _jsxs(Card, { children: [_jsx(CardHeader, { children: _jsx(CardTitle, { children: "Detailed CLV Analysis by Segment" }) }), _jsx(CardContent, { children: _jsx("div", { className: "overflow-x-auto", children: _jsxs("table", { className: "w-full text-sm", children: [_jsx("thead", { children: _jsxs("tr", { className: "border-b", children: [_jsx("th", { className: "text-left py-3 px-4 font-semibold", children: "Segment" }), _jsx("th", { className: "text-center py-3 px-4 font-semibold", children: "Customers" }), _jsx("th", { className: "text-center py-3 px-4 font-semibold", children: "Current CLV" }), _jsx("th", { className: "text-center py-3 px-4 font-semibold", children: "Projected CLV" }), _jsx("th", { className: "text-center py-3 px-4 font-semibold", children: "AOV" }), _jsx("th", { className: "text-center py-3 px-4 font-semibold", children: "Frequency" }), _jsx("th", { className: "text-center py-3 px-4 font-semibold", children: "Lifespan" }), _jsx("th", { className: "text-center py-3 px-4 font-semibold", children: "Total Value" })] }) }), _jsx("tbody", { children: clvSegments.map((segment, index) => (_jsxs("tr", { className: "border-b hover:bg-gray-50", children: [_jsx("td", { className: "py-3 px-4", children: _jsx("div", { className: "font-medium text-gray-900", children: segment.segment }) }), _jsx("td", { className: "py-3 px-4 text-center", children: _jsx("span", { className: "text-gray-600", children: segment.customerCount }) }), _jsx("td", { className: "py-3 px-4 text-center", children: _jsxs("span", { className: "font-semibold text-blue-600", children: ["$", segment.currentCLV] }) }), _jsxs("td", { className: "py-3 px-4 text-center", children: [_jsxs("span", { className: "font-semibold text-purple-600", children: ["$", segment.projectedCLV] }), _jsxs("div", { className: "text-xs text-green-600", children: ["+", (((segment.projectedCLV - segment.currentCLV) / segment.currentCLV) * 100).toFixed(1), "%"] })] }), _jsx("td", { className: "py-3 px-4 text-center", children: _jsxs("span", { className: "text-gray-600", children: ["$", segment.avgOrderValue] }) }), _jsx("td", { className: "py-3 px-4 text-center", children: _jsxs("span", { className: "text-gray-600", children: [segment.purchaseFrequency, "x"] }) }), _jsx("td", { className: "py-3 px-4 text-center", children: _jsxs("span", { className: "text-gray-600", children: [segment.customerLifespan, "mo"] }) }), _jsx("td", { className: "py-3 px-4 text-center", children: _jsxs("span", { className: "font-semibold text-green-600", children: ["$", (segment.totalValue / 1000).toFixed(0), "K"] }) })] }, index))) })] }) }) })] }), _jsxs(Card, { children: [_jsx(CardHeader, { children: _jsx(CardTitle, { children: "CLV Optimization Recommendations" }) }), _jsx(CardContent, { children: _jsxs("div", { className: "grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4", children: [_jsxs("div", { className: "p-4 bg-green-50 rounded-lg", children: [_jsx("h4", { className: "font-semibold text-green-900 mb-2", children: "Increase AOV" }), _jsxs("ul", { className: "text-sm text-green-700 space-y-1", children: [_jsx("li", { children: "\u2022 Cross-sell complementary products" }), _jsx("li", { children: "\u2022 Implement bundle offers" }), _jsx("li", { children: "\u2022 Offer volume discounts" }), _jsx("li", { children: "\u2022 Upsell premium versions" })] }), _jsx(Badge, { className: "mt-2 bg-green-100 text-green-800", children: "+15% potential increase" })] }), _jsxs("div", { className: "p-4 bg-blue-50 rounded-lg", children: [_jsx("h4", { className: "font-semibold text-blue-900 mb-2", children: "Boost Frequency" }), _jsxs("ul", { className: "text-sm text-blue-700 space-y-1", children: [_jsx("li", { children: "\u2022 Loyalty program rewards" }), _jsx("li", { children: "\u2022 Subscription models" }), _jsx("li", { children: "\u2022 Personalized recommendations" }), _jsx("li", { children: "\u2022 Regular engagement campaigns" })] }), _jsx(Badge, { className: "mt-2 bg-blue-100 text-blue-800", children: "+20% potential increase" })] }), _jsxs("div", { className: "p-4 bg-purple-50 rounded-lg", children: [_jsx("h4", { className: "font-semibold text-purple-900 mb-2", children: "Extend Lifespan" }), _jsxs("ul", { className: "text-sm text-purple-700 space-y-1", children: [_jsx("li", { children: "\u2022 Improve customer service" }), _jsx("li", { children: "\u2022 Regular satisfaction surveys" }), _jsx("li", { children: "\u2022 Proactive support" }), _jsx("li", { children: "\u2022 Community building" })] }), _jsx(Badge, { className: "mt-2 bg-purple-100 text-purple-800", children: "+25% potential increase" })] })] }) })] })] }));
};
export default CustomerLifetimeValue;
