import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
import { useMemo, useCallback, useEffect } from 'react';
import { SidebarItem } from './SidebarItem';
import { cn } from '@/lib/utils';
export function SidebarNavList({ categories = [], items = [], activeItemKey, onItemClick, isCollapsed, textColor = "text-gray-600", textHoverColor = "hover:text-gray-900", activeBgColor = "bg-gray-100", activeTextColor = "text-gray-900", hoverBgColor = "hover:bg-gray-50", expandedCategories = [], onCategoryToggle, userRole, expandedItems, onToggleExpand }) {
    // Memoized function to check if an item or any of its children has the active path
    const isItemActive = useCallback((item) => {
        // Get current location from window if activeItemKey isn't provided
        const currentPath = activeItemKey || globalThis.location.pathname;
        if (currentPath === item.path || currentPath === item.href) {
            return true;
        }
        // Check children if they exist
        if (item.children && item.children.length > 0) {
            return item.children.some(child => isItemActive(child));
        }
        return false;
    }, [activeItemKey]);
    // Memoized function to filter items by role
    const filterItemsByRole = useCallback((items, role) => {
        if (!role)
            return items;
        return items.filter(item => {
            // If no roles specified, everyone can see it
            if (!item.roles || item.roles.length === 0)
                return true;
            // Otherwise, check if user role is in the allowed roles
            return item.roles.includes(role);
        });
    }, []);
    // Memoized computation of all categories
    const allCategories = useMemo(() => {
        return categories.length > 0 ? categories :
            items.length > 0 ? [{
                    name: 'default',
                    label: 'Navigation',
                    items: items
                }] : [];
    }, [categories, items]);
    // Memoized processing of categories - SHOW ALL ITEMS REGARDLESS OF PERMISSIONS
    const processedCategories = useMemo(() => {
        // Return all categories and items without filtering by role
        return allCategories;
    }, [allCategories]);
    // Auto-expand categories that contain active items
    useEffect(() => {
        if (!onCategoryToggle || expandedCategories.length > 0)
            return;
        // Find categories with active items and expand them
        processedCategories.forEach(category => {
            if (category.items && category.items.some(item => isItemActive(item))) {
                if (category.name) {
                    onCategoryToggle(category.name);
                }
            }
        });
    }, [processedCategories, isItemActive, onCategoryToggle, expandedCategories]);
    // Debug logging
    console.log('SidebarNavList - processedCategories:', processedCategories);
    console.log('SidebarNavList - categories length:', processedCategories.length);
    if (processedCategories.length === 0) {
        console.log('SidebarNavList - No categories to render');
        return null;
    }
    return (_jsx("div", { className: cn("space-y-4", isCollapsed && "items-center"), children: processedCategories.map((category) => {
            const isExpanded = expandedCategories.includes(category.name || '') ||
                (expandedItems && category.label && expandedItems.includes(category.label));
            return (_jsxs("div", { className: "space-y-1", children: [!isCollapsed && (_jsxs("h3", { className: cn("flex items-center justify-between text-sm font-medium px-3 py-1.5 rounded-md cursor-pointer", textColor, textHoverColor), onClick: () => {
                            if (onCategoryToggle && category.name) {
                                onCategoryToggle(category.name);
                            }
                            else if (onToggleExpand && category.label) {
                                onToggleExpand(category.label);
                            }
                        }, children: [category.label || category.name, category.items && category.items.length > 0 && (_jsx("span", { className: cn("transform transition-transform", isExpanded ? "rotate-180" : ""), children: "\u25BC" }))] })), (!isCollapsed || !onCategoryToggle) && (isExpanded || !onCategoryToggle) && category.items && (_jsxs("div", { className: "pt-1 pl-1", children: [import.meta.env.DEV && false && (_jsxs("div", { className: "text-xs text-gray-400 italic", children: ["Debug: ", category.items.length, " items"] })), category.items.map((item) => {
                                const isActive = isItemActive(item);
                                // Determine if this item has an active child
                                const hasActiveChild = item.children?.some(child => isItemActive(child)) || false;
                                return (_jsx(SidebarItem, { item: item, isActive: isActive || hasActiveChild, textColor: textColor, textHoverColor: textHoverColor, activeBgColor: activeBgColor, activeTextColor: activeTextColor, hoverBgColor: hoverBgColor, onClick: () => onItemClick && onItemClick(item.path || item.href || item.label) }, item.label));
                            })] }))] }, category.name || category.label));
        }) }));
}
