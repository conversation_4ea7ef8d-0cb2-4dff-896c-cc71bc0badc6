{"name": "taskmaster-mcp-server", "version": "1.0.0", "description": "Taskmaster MCP Server for task management", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js"}, "dependencies": {"@modelcontextprotocol/sdk": "^1.0.0", "express": "^4.18.0", "fs": "^0.0.1-security"}, "keywords": ["mcp", "taskmaster", "tasks", "management"], "author": "NXT Level Tech", "license": "MIT"}