import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
import { Card, CardContent } from '@/components/ui/card';
import { ArrowUpIcon, ArrowDownIcon } from 'lucide-react';
const KpiCard = ({ title, value, change }) => {
    const isPositive = change >= 0;
    return (_jsx(Card, { children: _jsxs(CardContent, { className: "p-4", children: [_jsx("h3", { className: "text-sm font-medium text-muted-foreground", children: title }), _jsxs("div", { className: "flex items-center justify-between mt-2", children: [_jsx("p", { className: "text-2xl font-bold", children: value }), _jsxs("div", { className: "flex items-center", children: [isPositive ? (_jsx(ArrowUpIcon, { className: "w-4 h-4 text-green-500" })) : (_jsx(ArrowDownIcon, { className: "w-4 h-4 text-red-500" })), _jsxs("span", { className: `ml-1 text-sm ${isPositive ? 'text-green-500' : 'text-red-500'}`, children: [Math.abs(change), "%"] })] })] })] }) }));
};
export default KpiCard;
