import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";
import { 
  Mail, 
  MessageSquare, 
  Plus, 
  Edit, 
  Trash2, 
  Copy, 
  Eye,
  Save,
  ArrowLeft,
  FileText,
  Search
} from "lucide-react";
import { EmailTemplate, SMSTemplate } from "@/services/customer-communication";

interface TemplateManagerProps {
  onBack?: () => void;
  onSelectTemplate?: (template: EmailTemplate | SMSTemplate) => void;
}

const TemplateManager: React.FC<TemplateManagerProps> = ({
  onBack,
  onSelectTemplate
}) => {
  const [activeTab, setActiveTab] = useState<'email' | 'sms'>('email');
  const [emailTemplates, setEmailTemplates] = useState<EmailTemplate[]>([]);
  const [smsTemplates, setSmsTemplates] = useState<SMSTemplate[]>([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedTemplate, setSelectedTemplate] = useState<EmailTemplate | SMSTemplate | null>(null);
  const [isEditing, setIsEditing] = useState(false);
  const [isCreating, setIsCreating] = useState(false);
  const [loading, setLoading] = useState(false);

  // Form state for creating/editing templates
  const [formData, setFormData] = useState({
    name: '',
    subject: '',
    content: '',
    type: 'marketing' as 'marketing' | 'transactional' | 'notification',
    variables: [] as string[]
  });

  useEffect(() => {
    loadTemplates();
  }, []);

  const loadTemplates = async () => {
    try {
      setLoading(true);
      
      // Mock data - replace with actual API calls
      const mockEmailTemplates: EmailTemplate[] = [
        {
          id: 'welcome-email',
          name: 'Welcome Email',
          subject: 'Welcome to {{company_name}}!',
          content: `
            <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
              <h1 style="color: #333;">Welcome {{customer_name}}!</h1>
              <p>Thank you for joining {{company_name}}. We're excited to have you on board.</p>
              <div style="background: #f8f9fa; padding: 20px; border-radius: 8px; margin: 20px 0;">
                <h3>Here's what you can expect:</h3>
                <ul>
                  <li>Exclusive offers and discounts</li>
                  <li>Product updates and news</li>
                  <li>Priority customer support</li>
                </ul>
              </div>
              <p>Best regards,<br>The {{company_name}} Team</p>
              <div style="margin-top: 30px; padding-top: 20px; border-top: 1px solid #eee; font-size: 12px; color: #666;">
                <p>You received this email because you signed up for {{company_name}}.</p>
              </div>
            </div>
          `,
          type: 'transactional',
          variables: ['customer_name', 'company_name'],
          isActive: true,
          createdAt: '2025-05-20T10:00:00Z',
          updatedAt: '2025-05-20T10:00:00Z'
        },
        {
          id: 'product-update',
          name: 'Product Update Announcement',
          subject: 'New Features Available - {{product_name}}',
          content: `
            <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
              <h1 style="color: #333;">Exciting Updates to {{product_name}}!</h1>
              <p>Hi {{customer_name}},</p>
              <p>We've just released some amazing new features that we think you'll love:</p>
              <div style="background: #f8f9fa; padding: 20px; border-radius: 8px; margin: 20px 0;">
                {{feature_list}}
              </div>
              <div style="text-align: center; margin: 30px 0;">
                <a href="{{update_link}}" style="background: #007bff; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; display: inline-block;">Learn More</a>
              </div>
              <p>Thanks for being a valued customer!</p>
              <p>Best regards,<br>The {{company_name}} Team</p>
            </div>
          `,
          type: 'marketing',
          variables: ['customer_name', 'product_name', 'feature_list', 'update_link', 'company_name'],
          isActive: true,
          createdAt: '2025-05-22T14:00:00Z',
          updatedAt: '2025-05-22T14:00:00Z'
        },
        {
          id: 'order-confirmation',
          name: 'Order Confirmation',
          subject: 'Order Confirmation - #{{order_number}}',
          content: `
            <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
              <h1 style="color: #333;">Order Confirmation</h1>
              <p>Hi {{customer_name}},</p>
              <p>Thank you for your order! Here are the details:</p>
              <div style="background: #f8f9fa; padding: 20px; border-radius: 8px; margin: 20px 0;">
                <h3>Order #{{order_number}}</h3>
                <p><strong>Order Date:</strong> {{order_date}}</p>
                <p><strong>Total Amount:</strong> {{order_total}}</p>
                <div style="margin-top: 15px;">
                  {{order_items}}
                </div>
              </div>
              <p>We'll send you another email when your order ships.</p>
              <p>Best regards,<br>The {{company_name}} Team</p>
            </div>
          `,
          type: 'transactional',
          variables: ['customer_name', 'order_number', 'order_date', 'order_total', 'order_items', 'company_name'],
          isActive: true,
          createdAt: '2025-05-23T09:00:00Z',
          updatedAt: '2025-05-23T09:00:00Z'
        }
      ];

      const mockSmsTemplates: SMSTemplate[] = [
        {
          id: 'welcome-sms',
          name: 'Welcome SMS',
          content: 'Welcome to {{company_name}}, {{customer_name}}! Your account is ready. Reply STOP to opt out.',
          type: 'transactional',
          variables: ['customer_name', 'company_name'],
          isActive: true,
          createdAt: '2025-05-20T10:00:00Z',
          updatedAt: '2025-05-20T10:00:00Z'
        },
        {
          id: 'flash-sale',
          name: 'Flash Sale Alert',
          content: '🔥 FLASH SALE: {{discount}}% off {{product_name}}! Use code {{promo_code}}. Valid until {{expiry}}. Shop now: {{link}}',
          type: 'marketing',
          variables: ['discount', 'product_name', 'promo_code', 'expiry', 'link'],
          isActive: true,
          createdAt: '2025-05-24T09:00:00Z',
          updatedAt: '2025-05-24T09:00:00Z'
        },
        {
          id: 'order-shipped',
          name: 'Order Shipped',
          content: 'Great news {{customer_name}}! Your order #{{order_number}} has shipped. Track it here: {{tracking_link}}',
          type: 'notification',
          variables: ['customer_name', 'order_number', 'tracking_link'],
          isActive: true,
          createdAt: '2025-05-25T11:00:00Z',
          updatedAt: '2025-05-25T11:00:00Z'
        },
        {
          id: 'appointment-reminder',
          name: 'Appointment Reminder',
          content: 'Reminder: You have an appointment with {{company_name}} on {{appointment_date}} at {{appointment_time}}. Reply CONFIRM to confirm.',
          type: 'notification',
          variables: ['company_name', 'appointment_date', 'appointment_time'],
          isActive: true,
          createdAt: '2025-05-25T15:00:00Z',
          updatedAt: '2025-05-25T15:00:00Z'
        }
      ];

      setEmailTemplates(mockEmailTemplates);
      setSmsTemplates(mockSmsTemplates);
    } catch (error) {
      console.error('Failed to load templates:', error);
    } finally {
      setLoading(false);
    }
  };

  const filteredTemplates = () => {
    const templates = activeTab === 'email' ? emailTemplates : smsTemplates;
    return templates.filter(template =>
      template.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      template.type.toLowerCase().includes(searchTerm.toLowerCase())
    );
  };

  const handleCreateNew = () => {
    setFormData({
      name: '',
      subject: activeTab === 'email' ? '' : '',
      content: '',
      type: 'marketing',
      variables: []
    });
    setIsCreating(true);
    setIsEditing(false);
    setSelectedTemplate(null);
  };

  const handleEdit = (template: EmailTemplate | SMSTemplate) => {
    setFormData({
      name: template.name,
      subject: 'subject' in template ? template.subject : '',
      content: template.content,
      type: template.type,
      variables: template.variables
    });
    setSelectedTemplate(template);
    setIsEditing(true);
    setIsCreating(false);
  };

  const handleSave = async () => {
    if (!formData.name.trim() || !formData.content.trim()) {
      alert('Please fill in all required fields');
      return;
    }

    try {
      setLoading(true);
      
      // Mock save logic - replace with actual API call
      const templateData = {
        id: isCreating ? `${activeTab}_${Date.now()}` : selectedTemplate?.id,
        name: formData.name,
        content: formData.content,
        type: formData.type,
        variables: formData.variables,
        isActive: true,
        createdAt: isCreating ? new Date().toISOString() : selectedTemplate?.createdAt,
        updatedAt: new Date().toISOString(),
        ...(activeTab === 'email' && { subject: formData.subject })
      };

      if (activeTab === 'email') {
        if (isCreating) {
          setEmailTemplates(prev => [...prev, templateData as EmailTemplate]);
        } else {
          setEmailTemplates(prev => prev.map(t => t.id === templateData.id ? templateData as EmailTemplate : t));
        }
      } else {
        if (isCreating) {
          setSmsTemplates(prev => [...prev, templateData as SMSTemplate]);
        } else {
          setSmsTemplates(prev => prev.map(t => t.id === templateData.id ? templateData as SMSTemplate : t));
        }
      }

      setIsCreating(false);
      setIsEditing(false);
      setSelectedTemplate(null);
    } catch (error) {
      console.error('Failed to save template:', error);
      alert('Failed to save template. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const handleDelete = async (templateId: string) => {
    const confirmed = globalThis.confirm('Are you sure you want to delete this template?');
    if (!confirmed) return;

    try {
      if (activeTab === 'email') {
        setEmailTemplates(prev => prev.filter(t => t.id !== templateId));
      } else {
        setSmsTemplates(prev => prev.filter(t => t.id !== templateId));
      }
    } catch (error) {
      console.error('Failed to delete template:', error);
      alert('Failed to delete template. Please try again.');
    }
  };

  const handleDuplicate = (template: EmailTemplate | SMSTemplate) => {
    const duplicatedTemplate = {
      ...template,
      id: `${template.id}_copy_${Date.now()}`,
      name: `${template.name} (Copy)`,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };

    if (activeTab === 'email') {
      setEmailTemplates(prev => [...prev, duplicatedTemplate as EmailTemplate]);
    } else {
      setSmsTemplates(prev => [...prev, duplicatedTemplate as SMSTemplate]);
    }
  };

  const extractVariables = (content: string): string[] => {
    const regex = /\{\{([^}]+)\}\}/g;
    const variables = new Set<string>();
    let match;
    
    while ((match = regex.exec(content)) !== null) {
      variables.add(match[1].trim());
    }
    
    return Array.from(variables);
  };

  const handleContentChange = (content: string) => {
    setFormData(prev => ({
      ...prev,
      content,
      variables: extractVariables(content)
    }));
  };

  const getTypeColor = (type: string) => {
    switch (type) {
      case 'marketing': return 'bg-blue-100 text-blue-800';
      case 'transactional': return 'bg-green-100 text-green-800';
      case 'notification': return 'bg-yellow-100 text-yellow-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  if (isCreating || isEditing) {
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-4">
            <Button variant="ghost" onClick={() => { setIsCreating(false); setIsEditing(false); }}>
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Templates
            </Button>
            <div>
              <h1 className="text-3xl font-bold tracking-tight">
                {isCreating ? 'Create' : 'Edit'} {activeTab.toUpperCase()} Template
              </h1>
            </div>
          </div>
          <Button onClick={handleSave} disabled={loading}>
            <Save className="h-4 w-4 mr-2" />
            Save Template
          </Button>
        </div>

        <div className="grid gap-6 lg:grid-cols-3">
          <div className="lg:col-span-2">
            <Card>
              <CardHeader>
                <CardTitle>Template Details</CardTitle>
                <CardDescription>Configure your template settings and content</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <Label htmlFor="template-name">Template Name</Label>
                  <Input
                    id="template-name"
                    value={formData.name}
                    onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
                    placeholder="Enter template name"
                  />
                </div>

                <div>
                  <Label htmlFor="template-type">Template Type</Label>
                  <Select value={formData.type} onValueChange={(value) => setFormData(prev => ({ ...prev, type: value as any }))}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="marketing">Marketing</SelectItem>
                      <SelectItem value="transactional">Transactional</SelectItem>
                      <SelectItem value="notification">Notification</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                {activeTab === 'email' && (
                  <div>
                    <Label htmlFor="template-subject">Subject Line</Label>
                    <Input
                      id="template-subject"
                      value={formData.subject}
                      onChange={(e) => setFormData(prev => ({ ...prev, subject: e.target.value }))}
                      placeholder="Enter email subject"
                    />
                  </div>
                )}

                <div>
                  <Label htmlFor="template-content">Content</Label>
                  <Textarea
                    id="template-content"
                    value={formData.content}
                    onChange={(e) => handleContentChange(e.target.value)}
                    placeholder={activeTab === 'email' ? 'Enter email content (HTML supported)' : 'Enter SMS content'}
                    rows={activeTab === 'email' ? 15 : 6}
                    className="font-mono"
                  />
                  <p className="text-xs text-gray-500 mt-1">
                    Use variables like &#123;&#123;customer_name&#125;&#125; for personalization
                  </p>
                </div>
              </CardContent>
            </Card>
          </div>

          <div className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Variables</CardTitle>
                <CardDescription>Detected template variables</CardDescription>
              </CardHeader>
              <CardContent>
                {formData.variables.length > 0 ? (
                  <div className="flex flex-wrap gap-2">
                    {formData.variables.map((variable) => (
                      <Badge key={variable} variant="secondary">
                        {variable}
                      </Badge>
                    ))}
                  </div>
                ) : (
                  <p className="text-sm text-gray-500">No variables detected</p>
                )}
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Preview</CardTitle>
                <CardDescription>How your template will appear</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="p-3 bg-gray-50 rounded border min-h-[100px] text-sm">
                  {activeTab === 'email' && formData.subject && (
                    <div className="mb-3 pb-2 border-b">
                      <strong>Subject:</strong> {formData.subject}
                    </div>
                  )}
                  {activeTab === 'email' ? (
                    <div dangerouslySetInnerHTML={{ __html: formData.content || 'Enter content to see preview...' }} />
                  ) : (
                    <div className="whitespace-pre-wrap">{formData.content || 'Enter content to see preview...'}</div>
                  )}
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          {onBack && (
            <Button variant="ghost" onClick={onBack}>
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back
            </Button>
          )}
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Template Manager</h1>
            <p className="text-gray-600">Create and manage email and SMS templates</p>
          </div>
        </div>
        <Button onClick={handleCreateNew}>
          <Plus className="h-4 w-4 mr-2" />
          Create Template
        </Button>
      </div>

      <div className="flex items-center gap-4">
        <Tabs value={activeTab} onValueChange={(value) => setActiveTab(value as 'email' | 'sms')}>
          <TabsList>
            <TabsTrigger value="email" className="flex items-center gap-2">
              <Mail className="h-4 w-4" />
              Email Templates
            </TabsTrigger>
            <TabsTrigger value="sms" className="flex items-center gap-2">
              <MessageSquare className="h-4 w-4" />
              SMS Templates
            </TabsTrigger>
          </TabsList>
        </Tabs>

        <div className="flex-1 max-w-md">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
            <Input
              placeholder="Search templates..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10"
            />
          </div>
        </div>
      </div>

      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
        {filteredTemplates().map((template) => (
          <Card key={template.id} className="hover:shadow-md transition-shadow">
            <CardHeader>
              <div className="flex items-start justify-between">
                <div className="flex-1">
                  <CardTitle className="text-lg">{template.name}</CardTitle>
                  <CardDescription className="mt-1">
                    {'subject' in template && template.subject}
                  </CardDescription>
                </div>
                <Badge className={getTypeColor(template.type)}>
                  {template.type}
                </Badge>
              </div>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                <div className="text-sm text-gray-600 line-clamp-3">
                  {template.content.substring(0, 150)}...
                </div>
                
                {template.variables.length > 0 && (
                  <div className="flex flex-wrap gap-1">
                    {template.variables.slice(0, 3).map((variable) => (
                      <Badge key={variable} variant="outline" className="text-xs">
                        {variable}
                      </Badge>
                    ))}
                    {template.variables.length > 3 && (
                      <Badge variant="outline" className="text-xs">
                        +{template.variables.length - 3} more
                      </Badge>
                    )}
                  </div>
                )}

                <div className="flex items-center justify-between pt-2">
                  <span className="text-xs text-gray-500">
                    Updated {new Date(template.updatedAt).toLocaleDateString()}
                  </span>
                  <div className="flex gap-1">
                    <Button variant="ghost" size="sm" onClick={() => onSelectTemplate?.(template)}>
                      <Eye className="h-4 w-4" />
                    </Button>
                    <Button variant="ghost" size="sm" onClick={() => handleEdit(template)}>
                      <Edit className="h-4 w-4" />
                    </Button>
                    <Button variant="ghost" size="sm" onClick={() => handleDuplicate(template)}>
                      <Copy className="h-4 w-4" />
                    </Button>
                    <Button variant="ghost" size="sm" onClick={() => handleDelete(template.id)}>
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {filteredTemplates().length === 0 && (
        <div className="text-center py-12">
          <FileText className="h-12 w-12 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">No templates found</h3>
          <p className="text-gray-600 mb-4">
            {searchTerm ? 'No templates match your search criteria.' : `No ${activeTab} templates available.`}
          </p>
          <Button onClick={handleCreateNew}>
            <Plus className="h-4 w-4 mr-2" />
            Create Your First Template
          </Button>
        </div>
      )}
    </div>
  );
};

export default TemplateManager;
