version: '3.8'

services:
  nxt-platform-dev:
    image: ghcr.io/nxtleveltech1/dev-xxx:latest
    container_name: nxt-platform-dev
    restart: unless-stopped
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=production
      - PORT=3000
      - DATABASE_URL=${DATABASE_URL}
      - BETTER_AUTH_SECRET=${BETTER_AUTH_SECRET}
      - BETTER_AUTH_URL=http://localhost:3000/api/auth
    command: node server/production-better-auth-server.mjs
    user: nextjs
    networks:
      - nxt-network-dev

networks:
  nxt-network-dev:
    driver: bridge