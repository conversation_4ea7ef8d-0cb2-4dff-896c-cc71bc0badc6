{"name": "NXT-WEB-DEV-X with MCP Servers", "dockerComposeFile": "docker-compose.yml", "service": "app", "workspaceFolder": "/workspace", "shutdownAction": "stopCompose", "features": {"ghcr.io/devcontainers/features/node:1": {"version": "20"}, "ghcr.io/devcontainers/features/docker-in-docker:2": {"version": "latest", "dockerDashComposeVersion": "v2"}, "ghcr.io/devcontainers/features/git:1": {"version": "latest"}}, "customizations": {"vscode": {"extensions": ["ms-vscode.vscode-typescript-next", "bradlc.vscode-tailwindcss", "esbenp.prettier-vscode", "ms-vscode.vscode-json", "ms-vscode.vscode-eslint", "formulahendry.auto-rename-tag", "christian-kohler.path-intellisense", "ms-vscode.vscode-docker", "ms-vscode-remote.remote-containers"], "settings": {"terminal.integrated.defaultProfile.linux": "bash", "typescript.preferences.includePackageJsonAutoImports": "auto", "editor.formatOnSave": true, "editor.defaultFormatter": "esbenp.prettier-vscode", "tailwindCSS.includeLanguages": {"typescript": "javascript", "typescriptreact": "javascript"}, "files.associations": {"*.css": "tailwindcss"}}}}, "forwardPorts": [3000, 5432, 8080, 8081, 8082, 8083, 8084, 8085, 8086, 8087, 8088, 8089, 8090], "portsAttributes": {"3000": {"label": "Frontend", "onAutoForward": "notify"}, "5432": {"label": "PostgreSQL"}, "8080": {"label": "Brave Search MCP"}, "8081": {"label": "<PERSON><PERSON>"}, "8082": {"label": "FireCrawl MCP"}, "8083": {"label": "Context7 MCP"}, "8084": {"label": "Notion MCP"}, "8085": {"label": "Desktop Commander MCP"}, "8086": {"label": "Taskmaster MC<PERSON>"}, "8087": {"label": "Supabase MCP"}, "8088": {"label": "Browser Tools MCP"}, "8089": {"label": "Magic MCP"}, "8090": {"label": "Neo4j MCP"}}, "postCreateCommand": "bash .devcontainer/scripts/setup.sh", "postStartCommand": "bash .devcontainer/scripts/start-mcp-servers.sh", "remoteEnv": {"PATH": "${containerEnv:PATH}:/workspace/node_modules/.bin"}, "mounts": ["source=${localWorkspaceFolder}/.env,target=/workspace/.env,type=bind,consistency=cached", "source=mcp-data,target=/mcp-data,type=volume"]}