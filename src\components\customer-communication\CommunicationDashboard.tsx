import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Tabs, TabsContent, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { 
  Mail, 
  MessageSquare, 
  Users, 
  TrendingUp, 
  Send, 
  Calendar,
  BarChart3,
  Settings,
  Plus,
  Eye,
  Edit,
  Trash2
} from "lucide-react";
import { CustomerCommunicationService, Campaign, CommunicationMetrics } from "@/services/customer-communication";

interface CommunicationDashboardProps {
  onCreateCampaign?: () => void;
  onViewTemplates?: () => void;
  onViewContacts?: () => void;
  onViewAnalytics?: () => void;
}

const CommunicationDashboard: React.FC<CommunicationDashboardProps> = ({
  onCreateCampaign,
  onViewTemplates,
  onViewContacts,
  onViewAnalytics
}) => {
  const [metrics, setMetrics] = useState<CommunicationMetrics | null>(null);
  const [recentCampaigns, setRecentCampaigns] = useState<Campaign[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    loadDashboardData();
  }, []);

  const loadDashboardData = async () => {
    try {
      setLoading(true);
      
      // Mock data - replace with actual API calls
      const mockMetrics: CommunicationMetrics = {
        totalCampaigns: 24,
        activeCampaigns: 3,
        totalContacts: 2847,
        emailMetrics: {
          sent: 15420,
          delivered: 14892,
          opened: 7446,
          clicked: 1489,
          bounced: 528,
          deliveryRate: 96.6,
          openRate: 50.0,
          clickRate: 20.0,
          bounceRate: 3.4
        },
        smsMetrics: {
          sent: 8340,
          delivered: 8173,
          clicked: 817,
          failed: 167,
          deliveryRate: 98.0,
          clickRate: 10.0
        }
      };

      const mockCampaigns: Campaign[] = [
        {
          id: '1',
          name: 'Welcome Series - New Customers',
          type: 'email',
          status: 'sending',
          templateId: 'welcome-template',
          targetSegments: ['new-customers'],
          contactIds: [],
          metrics: {
            sent: 245,
            delivered: 238,
            opened: 119,
            clicked: 24,
            bounced: 7,
            unsubscribed: 2
          },
          createdAt: '2025-05-25T10:00:00Z',
          updatedAt: '2025-05-26T08:30:00Z'
        },
        {
          id: '2',
          name: 'Product Update Announcement',
          type: 'email',
          status: 'sent',
          templateId: 'product-update',
          targetSegments: ['active-customers'],
          contactIds: [],
          metrics: {
            sent: 1823,
            delivered: 1756,
            opened: 878,
            clicked: 175,
            bounced: 67,
            unsubscribed: 12
          },
          createdAt: '2025-05-24T14:00:00Z',
          updatedAt: '2025-05-24T16:45:00Z'
        },
        {
          id: '3',
          name: 'Flash Sale Alert',
          type: 'sms',
          status: 'scheduled',
          templateId: 'flash-sale-sms',
          targetSegments: ['vip-customers'],
          contactIds: [],
          scheduledAt: '2025-05-27T09:00:00Z',
          metrics: {
            sent: 0,
            delivered: 0,
            opened: 0,
            clicked: 0,
            bounced: 0,
            unsubscribed: 0
          },
          createdAt: '2025-05-26T11:00:00Z',
          updatedAt: '2025-05-26T11:00:00Z'
        }
      ];

      setMetrics(mockMetrics);
      setRecentCampaigns(mockCampaigns);
    } catch (error) {
      console.error('Failed to load dashboard data:', error);
    } finally {
      setLoading(false);
    }
  };

  const getStatusColor = (status: Campaign['status']) => {
    switch (status) {
      case 'sending': return 'text-blue-600 bg-blue-100';
      case 'sent': return 'text-green-600 bg-green-100';
      case 'scheduled': return 'text-yellow-600 bg-yellow-100';
      case 'paused': return 'text-orange-600 bg-orange-100';
      case 'cancelled': return 'text-red-600 bg-red-100';
      default: return 'text-gray-600 bg-gray-100';
    }
  };

  const formatPercentage = (value: number) => `${value.toFixed(1)}%`;
  const formatNumber = (value: number) => value.toLocaleString();

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-2 text-gray-600">Loading communication dashboard...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Communication Dashboard</h1>
          <p className="text-gray-600">Manage email and SMS campaigns, templates, and analytics</p>
        </div>
        <div className="flex gap-2">
          <Button onClick={onCreateCampaign} className="flex items-center gap-2">
            <Plus className="h-4 w-4" />
            Create Campaign
          </Button>
          <Button variant="outline" onClick={onViewTemplates}>
            <Edit className="h-4 w-4 mr-2" />
            Templates
          </Button>
        </div>
      </div>

      {/* Key Metrics */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Campaigns</CardTitle>
            <Send className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{metrics?.totalCampaigns || 0}</div>
            <p className="text-xs text-muted-foreground">
              {metrics?.activeCampaigns || 0} active campaigns
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Contacts</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{formatNumber(metrics?.totalContacts || 0)}</div>
            <p className="text-xs text-muted-foreground">Across all segments</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Email Open Rate</CardTitle>
            <Mail className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{formatPercentage(metrics?.emailMetrics.openRate || 0)}</div>
            <p className="text-xs text-muted-foreground">
              {formatNumber(metrics?.emailMetrics.opened || 0)} emails opened
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">SMS Delivery Rate</CardTitle>
            <MessageSquare className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{formatPercentage(metrics?.smsMetrics.deliveryRate || 0)}</div>
            <p className="text-xs text-muted-foreground">
              {formatNumber(metrics?.smsMetrics.delivered || 0)} SMS delivered
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Communication Channels Performance */}
      <div className="grid gap-6 md:grid-cols-2">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Mail className="h-5 w-5" />
              Email Performance
            </CardTitle>
            <CardDescription>Email campaign metrics and engagement</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex justify-between items-center">
                <span className="text-sm text-gray-600">Delivery Rate</span>
                <span className="font-semibold">{formatPercentage(metrics?.emailMetrics.deliveryRate || 0)}</span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-sm text-gray-600">Open Rate</span>
                <span className="font-semibold">{formatPercentage(metrics?.emailMetrics.openRate || 0)}</span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-sm text-gray-600">Click Rate</span>
                <span className="font-semibold">{formatPercentage(metrics?.emailMetrics.clickRate || 0)}</span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-sm text-gray-600">Bounce Rate</span>
                <span className="font-semibold text-red-600">{formatPercentage(metrics?.emailMetrics.bounceRate || 0)}</span>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <MessageSquare className="h-5 w-5" />
              SMS Performance
            </CardTitle>
            <CardDescription>SMS campaign metrics and engagement</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex justify-between items-center">
                <span className="text-sm text-gray-600">Delivery Rate</span>
                <span className="font-semibold">{formatPercentage(metrics?.smsMetrics.deliveryRate || 0)}</span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-sm text-gray-600">Click Rate</span>
                <span className="font-semibold">{formatPercentage(metrics?.smsMetrics.clickRate || 0)}</span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-sm text-gray-600">Messages Sent</span>
                <span className="font-semibold">{formatNumber(metrics?.smsMetrics.sent || 0)}</span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-sm text-gray-600">Failed Deliveries</span>
                <span className="font-semibold text-red-600">{formatNumber(metrics?.smsMetrics.failed || 0)}</span>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Recent Campaigns */}
      <Card>
        <CardHeader>
          <CardTitle>Recent Campaigns</CardTitle>
          <CardDescription>Latest email and SMS campaigns</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {recentCampaigns.map((campaign) => (
              <div key={campaign.id} className="flex items-center justify-between p-4 border rounded-lg">
                <div className="flex items-center space-x-4">
                  <div className="w-10 h-10 rounded-full bg-blue-100 flex items-center justify-center">
                    {campaign.type === 'email' ? (
                      <Mail className="h-5 w-5 text-blue-600" />
                    ) : (
                      <MessageSquare className="h-5 w-5 text-blue-600" />
                    )}
                  </div>
                  <div>
                    <p className="font-medium">{campaign.name}</p>
                    <p className="text-sm text-gray-600">
                      {campaign.type.toUpperCase()} • Created {new Date(campaign.createdAt).toLocaleDateString()}
                    </p>
                  </div>
                </div>
                <div className="flex items-center space-x-4">
                  <div className="text-right">
                    <p className="text-sm font-medium">
                      {formatNumber(campaign.metrics.sent)} sent
                    </p>
                    <p className="text-xs text-gray-600">
                      {campaign.metrics.delivered > 0 && 
                        `${formatPercentage((campaign.metrics.opened / campaign.metrics.delivered) * 100)} opened`
                      }
                    </p>
                  </div>
                  <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(campaign.status)}`}>
                    {campaign.status}
                  </span>
                  <div className="flex space-x-1">
                    <Button variant="ghost" size="sm">
                      <Eye className="h-4 w-4" />
                    </Button>
                    <Button variant="ghost" size="sm">
                      <Edit className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Quick Actions */}
      <div className="grid gap-4 md:grid-cols-3">
        <Card className="cursor-pointer hover:shadow-md transition-shadow" onClick={onViewContacts}>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Users className="h-5 w-5" />
              Contact Management
            </CardTitle>
            <CardDescription>Manage contacts and segments</CardDescription>
          </CardHeader>
          <CardContent>
            <p className="text-sm text-gray-600">
              View and organize your contact lists, create segments, and manage preferences.
            </p>
          </CardContent>
        </Card>

        <Card className="cursor-pointer hover:shadow-md transition-shadow" onClick={onViewAnalytics}>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <BarChart3 className="h-5 w-5" />
              Analytics & Reports
            </CardTitle>
            <CardDescription>Detailed communication analytics</CardDescription>
          </CardHeader>
          <CardContent>
            <p className="text-sm text-gray-600">
              Access detailed reports, performance metrics, and communication insights.
            </p>
          </CardContent>
        </Card>

        <Card className="cursor-pointer hover:shadow-md transition-shadow">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Settings className="h-5 w-5" />
              Communication Settings
            </CardTitle>
            <CardDescription>Configure communication preferences</CardDescription>
          </CardHeader>
          <CardContent>
            <p className="text-sm text-gray-600">
              Set up email/SMS providers, configure templates, and manage automation rules.
            </p>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default CommunicationDashboard;
