"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.supabaseAdmin = exports.supabase = void 0;
const supabase_js_1 = require("@supabase/supabase-js");
// Use Node-based environment variables from process.env
const supabaseUrl = process.env.SUPABASE_URL || 'https://utxxvdztmbbjcwdkqxcc.supabase.co';
const supabaseAnonKey = process.env.SUPABASE_ANON_KEY || 'eyJhbGciOiJIUzI1NiIs...dummyKey';
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY || 'eyJhbGciOiJIUzDummyServiceKey';
// Export a basic supabase client for non-admin usage
exports.supabase = (0, supabase_js_1.createClient)(supabaseUrl, supabaseAnonKey, {
    auth: {
        autoRefreshToken: true,
        persistSession: false,
        detectSessionInUrl: false,
    },
    db: {
        schema: 'public'
    }
});
// Export an admin supabase client
exports.supabaseAdmin = (0, supabase_js_1.createClient)(supabaseUrl, supabaseServiceKey, {
    auth: {
        autoRefreshToken: false,
        persistSession: false,
        detectSessionInUrl: false,
    },
    db: {
        schema: 'public'
    },
    global: {
        headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${supabaseServiceKey}`,
            'apikey': supabaseServiceKey,
        }
    }
});
console.log('🔧 Supabase Admin Client initialized for Node environment');
console.log('🔧 Supabase URL:', supabaseUrl);
console.log('🔧 Service Key Present?', !!supabaseServiceKey);
