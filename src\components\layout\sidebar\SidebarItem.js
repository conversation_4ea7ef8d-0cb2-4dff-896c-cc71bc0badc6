import { jsx as _jsx, Fragment as _Fragment, jsxs as _jsxs } from "react/jsx-runtime";
import { useState } from 'react';
import { NavLink } from 'react-router-dom';
import { cn } from '@/lib/utils';
import { ChevronDown, ChevronRight } from 'lucide-react';
export function SidebarItem({ item, isActive, textColor, textHoverColor, activeBgColor, activeTextColor, hoverBgColor, onClick }) {
    const Icon = item.icon;
    const hasChildren = !!(item.children && item.children.length > 0);
    const [isExpanded, setIsExpanded] = useState(false);
    // Route path, using href with path as fallback for backwards compatibility
    const to = item.href || item.path || '#';
    const handleClick = (e) => {
        if (hasChildren) {
            e.preventDefault(); // Prevent navigation for parent items with children
            e.stopPropagation(); // Stop event from bubbling up to parent handlers
            setIsExpanded(!isExpanded);
        }
        else if (onClick) {
            onClick();
        }
    };
    // Content to render inside the item
    const itemContent = (_jsxs(_Fragment, { children: [Icon && _jsx(Icon, { className: "mr-2 h-4 w-4" }), _jsx("span", { className: "flex-1 truncate", children: item.label }), hasChildren && (_jsx("span", { className: "ml-auto flex items-center", children: isExpanded ?
                    _jsx(ChevronDown, { className: "h-4 w-4" }) :
                    _jsx(ChevronRight, { className: "h-4 w-4" }) }))] }));
    return (_jsxs("div", { className: "mb-1", children: [hasChildren || to === '#' ? (_jsx("div", { className: cn("flex items-center rounded-md px-3 py-2 text-sm cursor-pointer", isActive ? cn(activeBgColor, activeTextColor) : cn(textColor, textHoverColor, hoverBgColor), hasChildren && "justify-between font-medium"), onClick: handleClick, children: itemContent })) : (
            /* Otherwise use NavLink for navigation */
            _jsx(NavLink, { to: to, className: ({ isActive: routeActive }) => cn("flex items-center rounded-md px-3 py-2 text-sm cursor-pointer", (isActive || routeActive) ? cn(activeBgColor, activeTextColor) : cn(textColor, textHoverColor, hoverBgColor), hasChildren && "justify-between font-medium"), onClick: (e) => {
                    // Only trigger onClick if this item doesn't have children
                    if (!hasChildren && onClick) {
                        onClick();
                    }
                }, children: itemContent })), hasChildren && isExpanded && item.children && (_jsx("div", { className: "pl-6 mt-1 space-y-1", children: item.children.map((child) => (_jsx(SidebarItem, { item: child, isActive: false, textColor: textColor, textHoverColor: textHoverColor, activeBgColor: activeBgColor, activeTextColor: activeTextColor, hoverBgColor: hoverBgColor, onClick: () => {
                        // For child items, always navigate
                        if (onClick)
                            onClick();
                    } }, child.label))) }))] }));
}
