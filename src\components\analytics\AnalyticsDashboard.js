import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
import { useState, useEffect } from 'react';
import { AdvancedChart } from './AdvancedChart';
import { analyticsService } from '../../services/analytics/analyticsService';
import { dataQualityService } from '../../services/analytics/dataQualityService';
import { performanceAnalyticsService } from '../../services/analytics/performanceAnalyticsService';
export const AnalyticsDashboard = ({ dashboardId, timeRange, refreshInterval = 30000, editable = false }) => {
    const [dashboard, setDashboard] = useState(null);
    const [widgets, setWidgets] = useState([]);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState(null);
    const [selectedTimeRange, setSelectedTimeRange] = useState(timeRange || {
        start: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000), // Last 7 days
        end: new Date()
    });
    useEffect(() => {
        loadDashboard();
    }, [dashboardId]);
    useEffect(() => {
        if (refreshInterval > 0) {
            const interval = setInterval(loadDashboard, refreshInterval);
            return () => clearInterval(interval);
        }
    }, [refreshInterval]);
    const loadDashboard = async () => {
        try {
            setLoading(true);
            setError(null);
            if (dashboardId) {
                // Load existing dashboard
                const dashboards = await analyticsService.getDashboards();
                const foundDashboard = dashboards.data.find(d => d.id === dashboardId);
                if (foundDashboard) {
                    setDashboard(foundDashboard);
                    setWidgets(foundDashboard.widgets);
                }
                else {
                    throw new Error('Dashboard not found');
                }
            }
            else {
                // Create default dashboard
                await createDefaultDashboard();
            }
            setLoading(false);
        }
        catch (err) {
            setError(err instanceof Error ? err.message : 'Failed to load dashboard');
            setLoading(false);
        }
    };
    const createDefaultDashboard = async () => {
        const defaultWidgets = [
            {
                id: 'revenue-chart',
                type: 'chart',
                title: 'Revenue Trend',
                description: 'Monthly revenue over time',
                dataSource: 'business_metrics',
                query: 'revenue',
                visualization: {
                    type: 'line',
                    xAxis: { label: 'Month', type: 'time' },
                    yAxis: { label: 'Revenue ($)', type: 'value' },
                    colors: ['#3b82f6']
                },
                position: { x: 0, y: 0 },
                size: { width: 6, height: 4 },
                filters: []
            },
            {
                id: 'user-metrics',
                type: 'metric',
                title: 'Active Users',
                description: 'Current active users',
                dataSource: 'user_behavior',
                query: 'active_users',
                visualization: {
                    type: 'gauge',
                    colors: ['#10b981']
                },
                position: { x: 6, y: 0 },
                size: { width: 3, height: 2 },
                filters: []
            },
            {
                id: 'performance-summary',
                type: 'table',
                title: 'Performance Summary',
                description: 'System performance metrics',
                dataSource: 'performance',
                query: 'summary',
                visualization: {
                    type: 'bar'
                },
                position: { x: 9, y: 0 },
                size: { width: 3, height: 4 },
                filters: []
            },
            {
                id: 'data-quality',
                type: 'metric',
                title: 'Data Quality Score',
                description: 'Overall data quality percentage',
                dataSource: 'data_quality',
                query: 'overall_score',
                visualization: {
                    type: 'gauge',
                    colors: ['#f59e0b', '#ef4444', '#10b981']
                },
                position: { x: 6, y: 2 },
                size: { width: 3, height: 2 },
                filters: []
            },
            {
                id: 'top-pages',
                type: 'chart',
                title: 'Top Pages',
                description: 'Most visited pages',
                dataSource: 'user_behavior',
                query: 'top_pages',
                visualization: {
                    type: 'bar',
                    xAxis: { label: 'Page', type: 'category' },
                    yAxis: { label: 'Views', type: 'value' },
                    colors: ['#8b5cf6']
                },
                position: { x: 0, y: 4 },
                size: { width: 6, height: 3 },
                filters: []
            },
            {
                id: 'error-rate',
                type: 'chart',
                title: 'Error Rate',
                description: 'Application error rate over time',
                dataSource: 'performance',
                query: 'error_rate',
                visualization: {
                    type: 'line',
                    xAxis: { label: 'Time', type: 'time' },
                    yAxis: { label: 'Error Rate (%)', type: 'value' },
                    colors: ['#ef4444']
                },
                position: { x: 6, y: 4 },
                size: { width: 6, height: 3 },
                filters: []
            }
        ];
        const defaultDashboard = {
            id: 'default-dashboard',
            name: 'Analytics Overview',
            description: 'Comprehensive analytics dashboard',
            widgets: defaultWidgets,
            layout: { columns: 12, rows: 8, gap: 16 },
            filters: [],
            refreshInterval: 30000,
            isPublic: false,
            owner: 'system',
            createdAt: new Date(),
            updatedAt: new Date()
        };
        setDashboard(defaultDashboard);
        setWidgets(defaultWidgets);
    };
    const loadWidgetData = async (widget) => {
        try {
            switch (widget.dataSource) {
                case 'business_metrics':
                    return await loadBusinessMetrics(widget);
                case 'user_behavior':
                    return await loadUserBehaviorData(widget);
                case 'performance':
                    return await loadPerformanceData(widget);
                case 'data_quality':
                    return await loadDataQualityData(widget);
                default:
                    return [];
            }
        }
        catch (error) {
            console.error(`Error loading data for widget ${widget.id}:`, error);
            return [];
        }
    };
    const loadBusinessMetrics = async (widget) => {
        const metrics = await analyticsService.getMetrics('business', selectedTimeRange.start, selectedTimeRange.end);
        if (widget.query === 'revenue') {
            return metrics.data
                .filter(m => m.name === 'revenue')
                .map(m => ({ x: m.timestamp, y: m.value, label: m.name }));
        }
        return metrics.data;
    };
    const loadUserBehaviorData = async (widget) => {
        if (widget.query === 'active_users') {
            const snapshot = await performanceAnalyticsService.getCurrentPerformanceSnapshot();
            return [{ value: snapshot.active_users || 0 }];
        }
        if (widget.query === 'top_pages') {
            // Mock data for top pages
            return [
                { name: '/dashboard', value: 1250 },
                { name: '/analytics', value: 980 },
                { name: '/reports', value: 750 },
                { name: '/settings', value: 420 },
                { name: '/profile', value: 380 }
            ];
        }
        return [];
    };
    const loadPerformanceData = async (widget) => {
        const report = await performanceAnalyticsService.getPerformanceReport(selectedTimeRange.start, selectedTimeRange.end);
        if (widget.query === 'summary') {
            return [
                { metric: 'Avg Response Time', value: `${report.summary.avg_response_time}ms` },
                { metric: 'Total Requests', value: report.summary.total_requests.toLocaleString() },
                { metric: 'Error Rate', value: `${report.summary.error_rate}%` },
                { metric: 'Uptime', value: `${report.summary.uptime_percentage}%` }
            ];
        }
        if (widget.query === 'error_rate') {
            return report.metrics
                .filter(m => m.type === 'error_rate')
                .map(m => ({ x: m.timestamp, y: m.value }));
        }
        return [];
    };
    const loadDataQualityData = async (widget) => {
        const reports = await dataQualityService.getQualityReports(undefined, selectedTimeRange.start, selectedTimeRange.end);
        const latestReport = reports.data[0];
        if (widget.query === 'overall_score') {
            return [{ value: latestReport?.overall_score || 100 }];
        }
        return [];
    };
    const renderWidget = (widget) => {
        const [widgetData, setWidgetData] = useState([]);
        const [widgetLoading, setWidgetLoading] = useState(true);
        useEffect(() => {
            const loadData = async () => {
                setWidgetLoading(true);
                const data = await loadWidgetData(widget);
                setWidgetData(data);
                setWidgetLoading(false);
            };
            loadData();
        }, [widget, selectedTimeRange]);
        const widgetStyle = {
            gridColumn: `span ${widget.size.width}`,
            gridRow: `span ${widget.size.height}`,
        };
        return (_jsxs("div", { className: "bg-white rounded-lg shadow-sm border p-4", style: widgetStyle, children: [_jsxs("div", { className: "flex justify-between items-center mb-4", children: [_jsxs("div", { children: [_jsx("h3", { className: "text-lg font-semibold text-gray-900", children: widget.title }), widget.description && (_jsx("p", { className: "text-sm text-gray-600", children: widget.description }))] }), editable && (_jsxs("div", { className: "flex space-x-2", children: [_jsx("button", { className: "text-gray-400 hover:text-gray-600", onClick: () => editWidget(widget), children: "\u2699\uFE0F" }), _jsx("button", { className: "text-gray-400 hover:text-red-600", onClick: () => removeWidget(widget.id), children: "\uD83D\uDDD1\uFE0F" })] }))] }), widgetLoading ? (_jsx("div", { className: "flex items-center justify-center h-32", children: _jsx("div", { className: "animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600" }) })) : (_jsxs("div", { className: "h-full", children: [widget.type === 'chart' && (_jsx(AdvancedChart, { type: widget.visualization.type, data: widgetData, config: widget.visualization, width: 300, height: 200, interactive: true })), widget.type === 'metric' && (_jsxs("div", { className: "text-center", children: [_jsx("div", { className: "text-3xl font-bold text-blue-600", children: widgetData[0]?.value || 0 }), _jsx("div", { className: "text-sm text-gray-500 mt-2", children: widget.title })] })), widget.type === 'table' && (_jsx("div", { className: "overflow-x-auto", children: _jsx("table", { className: "min-w-full divide-y divide-gray-200", children: _jsx("tbody", { className: "divide-y divide-gray-200", children: widgetData.map((row, index) => (_jsxs("tr", { children: [_jsx("td", { className: "px-3 py-2 text-sm font-medium text-gray-900", children: row.metric }), _jsx("td", { className: "px-3 py-2 text-sm text-gray-500", children: row.value })] }, index))) }) }) }))] }))] }, widget.id));
    };
    const editWidget = (widget) => {
        // This would open a widget editor modal
        console.log('Edit widget:', widget);
    };
    const removeWidget = (widgetId) => {
        setWidgets(widgets.filter(w => w.id !== widgetId));
    };
    const addWidget = () => {
        // This would open a widget creation modal
        console.log('Add new widget');
    };
    if (loading) {
        return (_jsx("div", { className: "flex items-center justify-center h-64", children: _jsx("div", { className: "animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600" }) }));
    }
    if (error) {
        return (_jsx("div", { className: "bg-red-50 border border-red-200 rounded-lg p-4", children: _jsxs("div", { className: "text-red-800", children: [_jsx("h3", { className: "font-medium", children: "Dashboard Error" }), _jsx("p", { className: "text-sm mt-1", children: error })] }) }));
    }
    return (_jsxs("div", { className: "space-y-6", children: [_jsxs("div", { className: "flex justify-between items-center", children: [_jsxs("div", { children: [_jsx("h1", { className: "text-2xl font-bold text-gray-900", children: dashboard?.name || 'Analytics Dashboard' }), _jsx("p", { className: "text-gray-600", children: dashboard?.description || 'Comprehensive analytics overview' })] }), _jsxs("div", { className: "flex items-center space-x-4", children: [_jsxs("select", { className: "border border-gray-300 rounded-md px-3 py-2 text-sm", title: "Select time range", "aria-label": "Select time range for dashboard data", onChange: (e) => {
                                    const days = parseInt(e.target.value);
                                    setSelectedTimeRange({
                                        start: new Date(Date.now() - days * 24 * 60 * 60 * 1000),
                                        end: new Date()
                                    });
                                }, children: [_jsx("option", { value: "7", children: "Last 7 days" }), _jsx("option", { value: "30", children: "Last 30 days" }), _jsx("option", { value: "90", children: "Last 90 days" })] }), editable && (_jsx("button", { onClick: addWidget, className: "bg-blue-600 text-white px-4 py-2 rounded-md text-sm hover:bg-blue-700", children: "Add Widget" })), _jsx("button", { onClick: loadDashboard, className: "bg-gray-100 text-gray-700 px-4 py-2 rounded-md text-sm hover:bg-gray-200", children: "Refresh" })] })] }), _jsx("div", { className: "grid gap-4", style: {
                    gridTemplateColumns: `repeat(${dashboard?.layout.columns || 12}, minmax(0, 1fr))`,
                    gridAutoRows: 'minmax(100px, auto)'
                }, children: widgets.map(renderWidget) }), _jsxs("div", { className: "text-center text-sm text-gray-500", children: ["Last updated: ", new Date().toLocaleString(), refreshInterval > 0 && (_jsxs("span", { className: "ml-2", children: ["\u2022 Auto-refresh every ", refreshInterval / 1000, "s"] }))] })] }));
};
export default AnalyticsDashboard;
