"use strict";
// <PERSON>rip<PERSON> to create a new admin user using Supabase admin client
Object.defineProperty(exports, "__esModule", { value: true });
const supabase_1 = require("../src/integrations/supabase"); // Use index export
async function createNewAdminUser() {
    try {
        const email = "<EMAIL>"; // Provided email
        const firstName = "Admin";
        const lastName = "Two";
        const temporaryPassword = Math.random().toString(36).slice(-8);
        // 1) Create user in Supabase Auth with admin client
        const { data: authData, error: authError } = await supabase_1.supabaseAdmin.auth.admin.createUser({
            email,
            password: temporaryPassword,
            email_confirm: true
        });
        if (authError) {
            throw new Error("Error creating user in Supabase Auth: " + authError.message);
        }
        // 2) Create enhanced user profile with role set to 'admin'
        const { error: rpcError } = await supabase_1.supabaseAdmin.rpc("create_enhanced_user_with_role", {
            user_email: email,
            user_username: firstName.toLowerCase() + "." + lastName.toLowerCase(),
            user_first_name: firstName,
            user_last_name: lastName,
            user_role: "admin",
            is_active_user: true
        });
        if (rpcError) {
            throw new Error("Error creating enhanced user profile: " + rpcError.message);
        }
        console.log("Admin user created successfully:", email);
        console.log("Temporary Password:", temporaryPassword);
    }
    catch (err) {
        console.error("Failed to create admin user:", err.message);
        process.exit(1);
    }
    finally {
        process.exit(0);
    }
}
createNewAdminUser();
