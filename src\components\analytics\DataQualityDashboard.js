import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
import { useState, useEffect, useCallback } from 'react';
import { dataQualityService } from '../../services/analytics/dataQualityService';
import { AdvancedChart } from './AdvancedChart';
export const DataQualityDashboard = () => {
    const [qualityReport, setQualityReport] = useState(null);
    const [rules, setRules] = useState([]);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState(null);
    const [selectedTable, setSelectedTable] = useState('all');
    const [showRuleModal, setShowRuleModal] = useState(false);
    const loadDashboardData = useCallback(async () => {
        try {
            setLoading(true);
            setError(null);
            const [rulesResponse, reportsResponse] = await Promise.all([
                dataQualityService.getRules(),
                dataQualityService.getQualityReports()
            ]);
            setRules(rulesResponse.data);
            setQualityReport(reportsResponse.data[0] || null);
            setLoading(false);
        }
        catch (err) {
            setError(err instanceof Error ? err.message : 'Failed to load data quality dashboard');
            setLoading(false);
        }
    }, []);
    useEffect(() => {
        loadDashboardData();
    }, [loadDashboardData]);
    const runQualityCheck = async () => {
        try {
            setLoading(true);
            const report = await dataQualityService.runQualityCheck('all_tables');
            setQualityReport(report);
            setLoading(false);
        }
        catch (err) {
            setError(err instanceof Error ? err.message : 'Failed to run quality check');
            setLoading(false);
        }
    };
    const getScoreColor = (score) => {
        if (score >= 90)
            return 'text-green-600';
        if (score >= 70)
            return 'text-yellow-600';
        return 'text-red-600';
    };
    const getScoreBgColor = (score) => {
        if (score >= 90)
            return 'bg-green-100';
        if (score >= 70)
            return 'bg-yellow-100';
        return 'bg-red-100';
    };
    const getSeverityColor = (severity) => {
        switch (severity) {
            case 'critical': return 'text-red-600 bg-red-100';
            case 'high': return 'text-orange-600 bg-orange-100';
            case 'medium': return 'text-yellow-600 bg-yellow-100';
            case 'low': return 'text-blue-600 bg-blue-100';
            default: return 'text-gray-600 bg-gray-100';
        }
    };
    const renderQualityOverview = () => {
        if (!qualityReport)
            return null;
        return (_jsxs("div", { className: "grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8", children: [_jsx("div", { className: `p-6 rounded-lg ${getScoreBgColor(qualityReport.overall_score)}`, children: _jsxs("div", { className: "flex items-center justify-between", children: [_jsxs("div", { children: [_jsx("p", { className: "text-sm font-medium text-gray-600", children: "Overall Score" }), _jsxs("p", { className: `text-3xl font-bold ${getScoreColor(qualityReport.overall_score)}`, children: [qualityReport.overall_score.toFixed(1), "%"] })] }), _jsx("div", { className: "text-2xl", children: "\uD83D\uDCCA" })] }) }), _jsx("div", { className: "bg-white p-6 rounded-lg shadow-sm border", children: _jsxs("div", { className: "flex items-center justify-between", children: [_jsxs("div", { children: [_jsx("p", { className: "text-sm font-medium text-gray-600", children: "Rules Evaluated" }), _jsx("p", { className: "text-3xl font-bold text-blue-600", children: qualityReport.rules_evaluated })] }), _jsx("div", { className: "text-2xl", children: "\uD83D\uDCCB" })] }) }), _jsx("div", { className: "bg-white p-6 rounded-lg shadow-sm border", children: _jsxs("div", { className: "flex items-center justify-between", children: [_jsxs("div", { children: [_jsx("p", { className: "text-sm font-medium text-gray-600", children: "Issues Found" }), _jsx("p", { className: "text-3xl font-bold text-orange-600", children: qualityReport.issues_found })] }), _jsx("div", { className: "text-2xl", children: "\u26A0\uFE0F" })] }) }), _jsx("div", { className: "bg-white p-6 rounded-lg shadow-sm border", children: _jsxs("div", { className: "flex items-center justify-between", children: [_jsxs("div", { children: [_jsx("p", { className: "text-sm font-medium text-gray-600", children: "Critical Issues" }), _jsx("p", { className: "text-3xl font-bold text-red-600", children: qualityReport.critical_issues })] }), _jsx("div", { className: "text-2xl", children: "\uD83D\uDEA8" })] }) })] }));
    };
    const renderRulesTable = () => {
        return (_jsxs("div", { className: "bg-white rounded-lg shadow-sm border", children: [_jsx("div", { className: "px-6 py-4 border-b border-gray-200", children: _jsxs("div", { className: "flex justify-between items-center", children: [_jsx("h3", { className: "text-lg font-medium text-gray-900", children: "Data Quality Rules" }), _jsx("button", { onClick: () => setShowRuleModal(true), className: "bg-blue-600 text-white px-4 py-2 rounded-md text-sm hover:bg-blue-700", children: "Add Rule" })] }) }), _jsx("div", { className: "overflow-x-auto", children: _jsxs("table", { className: "min-w-full divide-y divide-gray-200", children: [_jsx("thead", { className: "bg-gray-50", children: _jsxs("tr", { children: [_jsx("th", { className: "px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider", children: "Rule Name" }), _jsx("th", { className: "px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider", children: "Type" }), _jsx("th", { className: "px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider", children: "Field" }), _jsx("th", { className: "px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider", children: "Severity" }), _jsx("th", { className: "px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider", children: "Status" }), _jsx("th", { className: "px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider", children: "Actions" })] }) }), _jsx("tbody", { className: "bg-white divide-y divide-gray-200", children: rules.map((rule) => (_jsxs("tr", { className: "hover:bg-gray-50", children: [_jsxs("td", { className: "px-6 py-4 whitespace-nowrap", children: [_jsx("div", { className: "text-sm font-medium text-gray-900", children: rule.name }), _jsx("div", { className: "text-sm text-gray-500", children: rule.description })] }), _jsx("td", { className: "px-6 py-4 whitespace-nowrap", children: _jsx("span", { className: "inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800", children: rule.type }) }), _jsx("td", { className: "px-6 py-4 whitespace-nowrap text-sm text-gray-900", children: rule.field }), _jsx("td", { className: "px-6 py-4 whitespace-nowrap", children: _jsx("span", { className: `inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getSeverityColor(rule.severity)}`, children: rule.severity }) }), _jsx("td", { className: "px-6 py-4 whitespace-nowrap", children: _jsx("span", { className: `inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${rule.isActive ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'}`, children: rule.isActive ? 'Active' : 'Inactive' }) }), _jsxs("td", { className: "px-6 py-4 whitespace-nowrap text-sm font-medium", children: [_jsx("button", { className: "text-blue-600 hover:text-blue-900 mr-3", children: "Edit" }), _jsx("button", { className: "text-red-600 hover:text-red-900", children: "Delete" })] })] }, rule.id))) })] }) })] }));
    };
    const renderQualityTrends = () => {
        // Mock trend data - in real implementation, this would come from historical reports
        const trendData = [
            { x: new Date('2024-01-01'), y: 85 },
            { x: new Date('2024-01-15'), y: 87 },
            { x: new Date('2024-02-01'), y: 89 },
            { x: new Date('2024-02-15'), y: 91 },
            { x: new Date('2024-03-01'), y: 88 },
            { x: new Date('2024-03-15'), y: 92 },
        ];
        return (_jsxs("div", { className: "bg-white rounded-lg shadow-sm border p-6", children: [_jsx("h3", { className: "text-lg font-medium text-gray-900 mb-4", children: "Quality Score Trends" }), _jsx(AdvancedChart, { type: "line", data: trendData, config: {
                        type: 'line',
                        xAxis: { label: 'Date', type: 'time' },
                        yAxis: { label: 'Quality Score (%)', type: 'value', min: 0, max: 100 },
                        colors: ['#3b82f6']
                    }, width: 600, height: 300, interactive: true })] }));
    };
    const renderIssuesByType = () => {
        if (!qualityReport?.results)
            return null;
        const issuesByType = qualityReport.results.reduce((acc, result) => {
            result.issues.forEach(issue => {
                acc[issue.type] = (acc[issue.type] || 0) + 1;
            });
            return acc;
        }, {});
        const chartData = Object.entries(issuesByType).map(([type, count]) => ({
            name: type,
            value: count
        }));
        return (_jsxs("div", { className: "bg-white rounded-lg shadow-sm border p-6", children: [_jsx("h3", { className: "text-lg font-medium text-gray-900 mb-4", children: "Issues by Type" }), _jsx(AdvancedChart, { type: "pie", data: chartData, config: {
                        type: 'pie',
                        colors: ['#ef4444', '#f59e0b', '#10b981', '#3b82f6', '#8b5cf6']
                    }, width: 400, height: 300, interactive: true })] }));
    };
    const renderRecentIssues = () => {
        if (!qualityReport?.results)
            return null;
        const allIssues = qualityReport.results.flatMap(result => result.issues.map(issue => ({
            ...issue,
            ruleName: result.ruleName
        }))).slice(0, 10); // Show only recent 10 issues
        return (_jsxs("div", { className: "bg-white rounded-lg shadow-sm border", children: [_jsx("div", { className: "px-6 py-4 border-b border-gray-200", children: _jsx("h3", { className: "text-lg font-medium text-gray-900", children: "Recent Issues" }) }), _jsx("div", { className: "divide-y divide-gray-200", children: allIssues.map((issue, index) => (_jsx("div", { className: "px-6 py-4", children: _jsxs("div", { className: "flex items-start justify-between", children: [_jsxs("div", { className: "flex-1", children: [_jsxs("div", { className: "flex items-center", children: [_jsx("span", { className: `inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getSeverityColor(issue.severity)} mr-2`, children: issue.severity }), _jsx("h4", { className: "text-sm font-medium text-gray-900", children: issue.type })] }), _jsx("p", { className: "text-sm text-gray-600 mt-1", children: issue.description }), _jsxs("div", { className: "text-xs text-gray-500 mt-2", children: ["Field: ", issue.field, " | Rule: ", issue.ruleName] })] }), _jsx("button", { className: "text-blue-600 hover:text-blue-900 text-sm", children: "View Details" })] }) }, index))) })] }));
    };
    if (loading) {
        return (_jsx("div", { className: "flex items-center justify-center h-64", children: _jsx("div", { className: "animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600" }) }));
    }
    if (error) {
        return (_jsx("div", { className: "bg-red-50 border border-red-200 rounded-lg p-4", children: _jsxs("div", { className: "text-red-800", children: [_jsx("h3", { className: "font-medium", children: "Data Quality Dashboard Error" }), _jsx("p", { className: "text-sm mt-1", children: error })] }) }));
    }
    return (_jsxs("div", { className: "space-y-6", children: [_jsxs("div", { className: "flex justify-between items-center", children: [_jsxs("div", { children: [_jsx("h1", { className: "text-2xl font-bold text-gray-900", children: "Data Quality Dashboard" }), _jsx("p", { className: "text-gray-600", children: "Monitor and manage data quality across your organization" })] }), _jsxs("div", { className: "flex items-center space-x-4", children: [_jsxs("select", { className: "border border-gray-300 rounded-md px-3 py-2 text-sm", title: "Select table", "aria-label": "Select table for quality analysis", value: selectedTable, onChange: (e) => setSelectedTable(e.target.value), children: [_jsx("option", { value: "all", children: "All Tables" }), _jsx("option", { value: "users", children: "Users" }), _jsx("option", { value: "orders", children: "Orders" }), _jsx("option", { value: "products", children: "Products" })] }), _jsx("button", { onClick: runQualityCheck, className: "bg-blue-600 text-white px-4 py-2 rounded-md text-sm hover:bg-blue-700", children: "Run Quality Check" })] })] }), renderQualityOverview(), _jsxs("div", { className: "grid grid-cols-1 lg:grid-cols-2 gap-6", children: [renderQualityTrends(), renderIssuesByType()] }), _jsxs("div", { className: "grid grid-cols-1 lg:grid-cols-2 gap-6", children: [_jsx("div", { children: renderRulesTable() }), _jsx("div", { children: renderRecentIssues() })] }), showRuleModal && (_jsx("div", { className: "fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50", children: _jsxs("div", { className: "bg-white rounded-lg p-6 w-96", children: [_jsx("h3", { className: "text-lg font-medium mb-4", children: "Add Data Quality Rule" }), _jsx("p", { className: "text-gray-600", children: "Rule creation form would go here..." }), _jsxs("div", { className: "flex justify-end space-x-3 mt-6", children: [_jsx("button", { onClick: () => setShowRuleModal(false), className: "px-4 py-2 text-gray-700 border border-gray-300 rounded-md hover:bg-gray-50", children: "Cancel" }), _jsx("button", { onClick: () => setShowRuleModal(false), className: "px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700", children: "Create Rule" })] })] }) }))] }));
};
export default DataQualityDashboard;
