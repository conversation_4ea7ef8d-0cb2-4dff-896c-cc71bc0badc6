#!/usr/bin/env node

const https = require('https');
const { Client } = require('pg');
require('dotenv').config();

class AuthSystemTester {
  constructor() {
    this.baseUrl = process.env.VITE_BETTER_AUTH_URL?.replace('/api/auth', '') || 'https://nxtdotx.co.za';
    this.testCredentials = {
      email: '<EMAIL>',
      password: 'Admin123!@#'
    };
  }

  async httpRequest(url, method = 'GET', data = null, headers = {}) {
    return new Promise((resolve, reject) => {
      const urlObj = new URL(url);
      const options = {
        hostname: urlObj.hostname,
        port: urlObj.port || 443,
        path: urlObj.pathname + urlObj.search,
        method: method,
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
          ...headers
        }
      };

      if (data) {
        const postData = JSON.stringify(data);
        options.headers['Content-Length'] = Buffer.byteLength(postData);
      }

      const req = https.request(options, (res) => {
        let body = '';
        res.on('data', (chunk) => body += chunk);
        res.on('end', () => {
          resolve({
            status: res.statusCode,
            headers: res.headers,
            body: body
          });
        });
      });

      req.on('error', reject);
      if (data) req.write(JSON.stringify(data));
      req.end();
    });
  }

  async testServerConnectivity() {
    console.log('🔍 Testing Server Connectivity...');
    
    const tests = [
      { name: 'Health Check', path: '/health' },
      { name: 'API Version', path: '/api/version' },
      { name: 'Auth Session', path: '/api/auth/session' }
    ];

    for (const test of tests) {
      try {
        const response = await this.httpRequest(`${this.baseUrl}${test.path}`);
        console.log(`✅ ${test.name}: ${response.status}`);
      } catch (error) {
        console.log(`❌ ${test.name}: ${error.message}`);
      }
    }
  }

  async testDatabaseConnection() {
    console.log('\n🔍 Testing Database Connection...');
    
    const client = new Client({
      connectionString: process.env.DATABASE_URL,
      ssl: process.env.NODE_ENV === 'production' ? { rejectUnauthorized: false } : false
    });

    try {
      await client.connect();
      console.log('✅ Database connection successful');

      const result = await client.query('SELECT NOW() as current_time');
      console.log(`✅ Database query successful: ${result.rows[0].current_time}`);

      // Check for test user
      const userQuery = 'SELECT email FROM "user" WHERE email = $1';
      const userResult = await client.query(userQuery, [this.testCredentials.email]);
      
      if (userResult.rows.length > 0) {
        console.log(`✅ Test user exists: ${this.testCredentials.email}`);
      } else {
        console.log(`❌ Test user not found: ${this.testCredentials.email}`);
      }

    } catch (error) {
      console.log(`❌ Database error: ${error.message}`);
    } finally {
      await client.end();
    }
  }

  async testAuthentication() {
    console.log('\n🔍 Testing Authentication Flow...');

    try {
      const response = await this.httpRequest(
        `${this.baseUrl}/api/auth/sign-in`,
        'POST',
        this.testCredentials
      );

      console.log(`Response Status: ${response.status}`);
      console.log(`Response Headers:`, Object.keys(response.headers));
      
      if (response.status === 200) {
        console.log('✅ Authentication request successful');
        try {
          const data = JSON.parse(response.body);
          console.log('✅ Valid JSON response received');
          if (data.user) {
            console.log(`✅ User authenticated: ${data.user.email}`);
          }
        } catch (e) {
          console.log('⚠️  Non-JSON response:', response.body.substring(0, 100));
        }
      } else {
        console.log(`❌ Authentication failed: ${response.status}`);
        console.log(`Response: ${response.body.substring(0, 200)}`);
      }

    } catch (error) {
      console.log(`❌ Authentication error: ${error.message}`);
    }
  }

  async runAllTests() {
    console.log('🚀 Starting Complete Auth System Test\n');
    
    await this.testServerConnectivity();
    await this.testDatabaseConnection();
    await this.testAuthentication();
    
    console.log('\n✅ Test completed');
  }
}

const tester = new AuthSystemTester();
tester.runAllTests().catch(console.error);