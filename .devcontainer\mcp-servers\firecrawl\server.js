#!/usr/bin/env node

const { Server } = require('@modelcontextprotocol/sdk/server/index.js');
const { StdioServerTransport } = require('@modelcontextprotocol/sdk/server/stdio.js');
const { CallToolRequestSchema, ListToolsRequestSchema } = require('@modelcontextprotocol/sdk/types.js');
const axios = require('axios');
const express = require('express');

class FireCrawlServer {
  constructor() {
    this.server = new Server(
      {
        name: 'firecrawl-server',
        version: '1.0.0',
      },
      {
        capabilities: {
          tools: {},
        },
      }
    );

    this.apiKey = process.env.FIRECRAWL_API_KEY;
    this.baseUrl = 'https://api.firecrawl.dev';
    this.port = process.env.MCP_PORT || 8082;
    
    this.setupToolHandlers();
    this.setupHealthCheck();
  }

  setupToolHandlers() {
    this.server.setRequestHandler(ListToolsRequestSchema, async () => {
      return {
        tools: [
          {
            name: 'firecrawl_scrape',
            description: 'Scrape a single URL and extract its content',
            inputSchema: {
              type: 'object',
              properties: {
                url: {
                  type: 'string',
                  description: 'URL to scrape',
                },
                formats: {
                  type: 'array',
                  description: 'Output formats (markdown, html, rawHtml, links, screenshot)',
                  items: {
                    type: 'string',
                    enum: ['markdown', 'html', 'rawHtml', 'links', 'screenshot'],
                  },
                  default: ['markdown'],
                },
                includeTags: {
                  type: 'array',
                  description: 'HTML tags to include',
                  items: {
                    type: 'string',
                  },
                },
                excludeTags: {
                  type: 'array',
                  description: 'HTML tags to exclude',
                  items: {
                    type: 'string',
                  },
                },
                onlyMainContent: {
                  type: 'boolean',
                  description: 'Extract only main content',
                  default: true,
                },
                waitFor: {
                  type: 'number',
                  description: 'Time to wait before scraping (ms)',
                  default: 0,
                },
              },
              required: ['url'],
            },
          },
          {
            name: 'firecrawl_crawl',
            description: 'Crawl a website and extract content from multiple pages',
            inputSchema: {
              type: 'object',
              properties: {
                url: {
                  type: 'string',
                  description: 'Base URL to crawl',
                },
                limit: {
                  type: 'number',
                  description: 'Maximum number of pages to crawl',
                  default: 10,
                  maximum: 100,
                },
                formats: {
                  type: 'array',
                  description: 'Output formats',
                  items: {
                    type: 'string',
                    enum: ['markdown', 'html', 'rawHtml', 'links'],
                  },
                  default: ['markdown'],
                },
                includePaths: {
                  type: 'array',
                  description: 'URL patterns to include',
                  items: {
                    type: 'string',
                  },
                },
                excludePaths: {
                  type: 'array',
                  description: 'URL patterns to exclude',
                  items: {
                    type: 'string',
                  },
                },
                maxDepth: {
                  type: 'number',
                  description: 'Maximum crawl depth',
                  default: 2,
                },
                allowBackwardLinks: {
                  type: 'boolean',
                  description: 'Allow crawling backward links',
                  default: false,
                },
                allowExternalLinks: {
                  type: 'boolean',
                  description: 'Allow crawling external links',
                  default: false,
                },
              },
              required: ['url'],
            },
          },
          {
            name: 'firecrawl_search',
            description: 'Search for content across crawled pages',
            inputSchema: {
              type: 'object',
              properties: {
                query: {
                  type: 'string',
                  description: 'Search query',
                },
                limit: {
                  type: 'number',
                  description: 'Maximum number of results',
                  default: 10,
                },
                tbs: {
                  type: 'string',
                  description: 'Time-based search parameters',
                },
                filter: {
                  type: 'string',
                  description: 'Additional search filters',
                },
              },
              required: ['query'],
            },
          },
        ],
      };
    });

    this.server.setRequestHandler(CallToolRequestSchema, async (request) => {
      const { name, arguments: args } = request.params;

      if (name === 'firecrawl_scrape') {
        return await this.handleScrape(args);
      } else if (name === 'firecrawl_crawl') {
        return await this.handleCrawl(args);
      } else if (name === 'firecrawl_search') {
        return await this.handleSearch(args);
      }

      throw new Error(`Unknown tool: ${name}`);
    });
  }

  async handleScrape(args) {
    if (!this.apiKey) {
      throw new Error('FIRECRAWL_API_KEY environment variable is required');
    }

    try {
      const payload = {
        url: args.url,
        formats: args.formats || ['markdown'],
        onlyMainContent: args.onlyMainContent !== false,
      };

      if (args.includeTags) payload.includeTags = args.includeTags;
      if (args.excludeTags) payload.excludeTags = args.excludeTags;
      if (args.waitFor) payload.waitFor = args.waitFor;

      const response = await axios.post(`${this.baseUrl}/v1/scrape`, payload, {
        headers: {
          'Authorization': `Bearer ${this.apiKey}`,
          'Content-Type': 'application/json',
        },
      });

      return {
        content: [
          {
            type: 'text',
            text: JSON.stringify({
              success: response.data.success,
              data: response.data.data,
              metadata: response.data.metadata,
            }, null, 2),
          },
        ],
      };
    } catch (error) {
      throw new Error(`FireCrawl Scrape API error: ${error.message}`);
    }
  }

  async handleCrawl(args) {
    if (!this.apiKey) {
      throw new Error('FIRECRAWL_API_KEY environment variable is required');
    }

    try {
      const payload = {
        url: args.url,
        limit: args.limit || 10,
        formats: args.formats || ['markdown'],
        maxDepth: args.maxDepth || 2,
        allowBackwardLinks: args.allowBackwardLinks || false,
        allowExternalLinks: args.allowExternalLinks || false,
      };

      if (args.includePaths) payload.includePaths = args.includePaths;
      if (args.excludePaths) payload.excludePaths = args.excludePaths;

      const response = await axios.post(`${this.baseUrl}/v1/crawl`, payload, {
        headers: {
          'Authorization': `Bearer ${this.apiKey}`,
          'Content-Type': 'application/json',
        },
      });

      return {
        content: [
          {
            type: 'text',
            text: JSON.stringify({
              success: response.data.success,
              id: response.data.id,
              url: response.data.url,
              data: response.data.data,
            }, null, 2),
          },
        ],
      };
    } catch (error) {
      throw new Error(`FireCrawl Crawl API error: ${error.message}`);
    }
  }

  async handleSearch(args) {
    if (!this.apiKey) {
      throw new Error('FIRECRAWL_API_KEY environment variable is required');
    }

    try {
      const payload = {
        query: args.query,
        limit: args.limit || 10,
      };

      if (args.tbs) payload.tbs = args.tbs;
      if (args.filter) payload.filter = args.filter;

      const response = await axios.post(`${this.baseUrl}/v1/search`, payload, {
        headers: {
          'Authorization': `Bearer ${this.apiKey}`,
          'Content-Type': 'application/json',
        },
      });

      return {
        content: [
          {
            type: 'text',
            text: JSON.stringify({
              success: response.data.success,
              data: response.data.data,
            }, null, 2),
          },
        ],
      };
    } catch (error) {
      throw new Error(`FireCrawl Search API error: ${error.message}`);
    }
  }

  setupHealthCheck() {
    const app = express();
    
    app.get('/health', (req, res) => {
      res.json({ 
        status: 'healthy', 
        service: 'firecrawl-mcp',
        timestamp: new Date().toISOString(),
        api_key_configured: !!this.apiKey
      });
    });

    app.listen(this.port, () => {
      console.log(`FireCrawl MCP Server health check running on port ${this.port}`);
    });
  }

  async run() {
    const transport = new StdioServerTransport();
    await this.server.connect(transport);
    console.error('FireCrawl MCP server running on stdio');
  }
}

const server = new FireCrawlServer();
server.run().catch(console.error);
