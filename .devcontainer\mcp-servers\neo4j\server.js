#!/usr/bin/env node

const { Server } = require('@modelcontextprotocol/sdk/server/index.js');
const { StdioServerTransport } = require('@modelcontextprotocol/sdk/server/stdio.js');
const { CallToolRequestSchema, ListToolsRequestSchema } = require('@modelcontextprotocol/sdk/types.js');
const express = require('express');

class Neo4jServer {
  constructor() {
    this.server = new Server(
      {
        name: 'neo4j-server',
        version: '1.0.0',
      },
      {
        capabilities: {
          tools: {},
        },
      }
    );

    this.uri = process.env.NEO4J_URI;
    this.username = process.env.NEO4J_USERNAME;
    this.password = process.env.NEO4J_PASSWORD;
    this.port = process.env.MCP_PORT || 8090;
    
    this.setupToolHandlers();
    this.setupHealthCheck();
  }

  setupToolHandlers() {
    this.server.setRequestHandler(ListToolsRequestSchema, async () => {
      return {
        tools: [
          {
            name: 'neo4j_query',
            description: 'Execute a Cypher query against Neo4j database',
            inputSchema: {
              type: 'object',
              properties: {
                query: {
                  type: 'string',
                  description: 'Cypher query to execute',
                },
                parameters: {
                  type: 'object',
                  description: 'Query parameters',
                  default: {},
                },
                limit: {
                  type: 'number',
                  description: 'Limit number of results',
                  default: 100,
                },
              },
              required: ['query'],
            },
          },
          {
            name: 'neo4j_create_node',
            description: 'Create a new node in the graph',
            inputSchema: {
              type: 'object',
              properties: {
                label: {
                  type: 'string',
                  description: 'Node label',
                },
                properties: {
                  type: 'object',
                  description: 'Node properties',
                  default: {},
                },
              },
              required: ['label'],
            },
          },
          {
            name: 'neo4j_create_relationship',
            description: 'Create a relationship between two nodes',
            inputSchema: {
              type: 'object',
              properties: {
                from_node: {
                  type: 'object',
                  description: 'Source node identifier',
                  properties: {
                    label: { type: 'string' },
                    properties: { type: 'object' },
                  },
                  required: ['label'],
                },
                to_node: {
                  type: 'object',
                  description: 'Target node identifier',
                  properties: {
                    label: { type: 'string' },
                    properties: { type: 'object' },
                  },
                  required: ['label'],
                },
                relationship_type: {
                  type: 'string',
                  description: 'Type of relationship',
                },
                properties: {
                  type: 'object',
                  description: 'Relationship properties',
                  default: {},
                },
              },
              required: ['from_node', 'to_node', 'relationship_type'],
            },
          },
          {
            name: 'neo4j_find_path',
            description: 'Find shortest path between two nodes',
            inputSchema: {
              type: 'object',
              properties: {
                start_node: {
                  type: 'object',
                  description: 'Starting node',
                  properties: {
                    label: { type: 'string' },
                    properties: { type: 'object' },
                  },
                  required: ['label'],
                },
                end_node: {
                  type: 'object',
                  description: 'Ending node',
                  properties: {
                    label: { type: 'string' },
                    properties: { type: 'object' },
                  },
                  required: ['label'],
                },
                max_depth: {
                  type: 'number',
                  description: 'Maximum path depth',
                  default: 5,
                },
                relationship_types: {
                  type: 'array',
                  description: 'Allowed relationship types',
                  items: { type: 'string' },
                },
              },
              required: ['start_node', 'end_node'],
            },
          },
          {
            name: 'neo4j_get_schema',
            description: 'Get database schema information',
            inputSchema: {
              type: 'object',
              properties: {
                include_constraints: {
                  type: 'boolean',
                  description: 'Include constraint information',
                  default: true,
                },
                include_indexes: {
                  type: 'boolean',
                  description: 'Include index information',
                  default: true,
                },
              },
            },
          },
        ],
      };
    });

    this.server.setRequestHandler(CallToolRequestSchema, async (request) => {
      const { name, arguments: args } = request.params;

      switch (name) {
        case 'neo4j_query':
          return await this.handleQuery(args);
        case 'neo4j_create_node':
          return await this.handleCreateNode(args);
        case 'neo4j_create_relationship':
          return await this.handleCreateRelationship(args);
        case 'neo4j_find_path':
          return await this.handleFindPath(args);
        case 'neo4j_get_schema':
          return await this.handleGetSchema(args);
        default:
          throw new Error(`Unknown tool: ${name}`);
      }
    });
  }

  async handleQuery(args) {
    if (!this.uri || !this.username || !this.password) {
      throw new Error('Neo4j connection not configured. Please set NEO4J_URI, NEO4J_USERNAME, and NEO4J_PASSWORD');
    }

    try {
      // Simulated query execution (would use neo4j-driver in real implementation)
      const result = {
        query: args.query,
        parameters: args.parameters || {},
        records: [
          { id: 1, properties: { name: 'Sample Node 1' } },
          { id: 2, properties: { name: 'Sample Node 2' } },
        ],
        summary: {
          query_type: 'r',
          counters: {
            nodes_created: 0,
            nodes_deleted: 0,
            relationships_created: 0,
            relationships_deleted: 0,
            properties_set: 0,
          },
          result_available_after: 5,
          result_consumed_after: 10,
        },
        timestamp: new Date().toISOString(),
      };

      return {
        content: [
          {
            type: 'text',
            text: JSON.stringify(result, null, 2),
          },
        ],
      };
    } catch (error) {
      throw new Error(`Neo4j Query error: ${error.message}`);
    }
  }

  async handleCreateNode(args) {
    if (!this.uri || !this.username || !this.password) {
      throw new Error('Neo4j connection not configured');
    }

    try {
      // Simulated node creation
      const nodeId = Math.floor(Math.random() * 10000);
      const result = {
        node_id: nodeId,
        label: args.label,
        properties: args.properties || {},
        created: true,
        timestamp: new Date().toISOString(),
      };

      return {
        content: [
          {
            type: 'text',
            text: JSON.stringify(result, null, 2),
          },
        ],
      };
    } catch (error) {
      throw new Error(`Neo4j Create Node error: ${error.message}`);
    }
  }

  async handleCreateRelationship(args) {
    if (!this.uri || !this.username || !this.password) {
      throw new Error('Neo4j connection not configured');
    }

    try {
      // Simulated relationship creation
      const relationshipId = Math.floor(Math.random() * 10000);
      const result = {
        relationship_id: relationshipId,
        from_node: args.from_node,
        to_node: args.to_node,
        type: args.relationship_type,
        properties: args.properties || {},
        created: true,
        timestamp: new Date().toISOString(),
      };

      return {
        content: [
          {
            type: 'text',
            text: JSON.stringify(result, null, 2),
          },
        ],
      };
    } catch (error) {
      throw new Error(`Neo4j Create Relationship error: ${error.message}`);
    }
  }

  async handleFindPath(args) {
    if (!this.uri || !this.username || !this.password) {
      throw new Error('Neo4j connection not configured');
    }

    try {
      // Simulated path finding
      const result = {
        start_node: args.start_node,
        end_node: args.end_node,
        path: [
          { node: args.start_node, relationship: null },
          { node: { label: 'Intermediate', properties: {} }, relationship: 'CONNECTS_TO' },
          { node: args.end_node, relationship: 'LEADS_TO' },
        ],
        length: 2,
        found: true,
        timestamp: new Date().toISOString(),
      };

      return {
        content: [
          {
            type: 'text',
            text: JSON.stringify(result, null, 2),
          },
        ],
      };
    } catch (error) {
      throw new Error(`Neo4j Find Path error: ${error.message}`);
    }
  }

  async handleGetSchema(args) {
    if (!this.uri || !this.username || !this.password) {
      throw new Error('Neo4j connection not configured');
    }

    try {
      // Simulated schema information
      const result = {
        labels: ['Person', 'Company', 'Product'],
        relationship_types: ['WORKS_FOR', 'PRODUCES', 'USES'],
        property_keys: ['name', 'age', 'email', 'founded', 'price'],
        constraints: args.include_constraints ? [
          { label: 'Person', property: 'email', type: 'UNIQUE' },
        ] : [],
        indexes: args.include_indexes ? [
          { label: 'Person', property: 'name', type: 'BTREE' },
        ] : [],
        timestamp: new Date().toISOString(),
      };

      return {
        content: [
          {
            type: 'text',
            text: JSON.stringify(result, null, 2),
          },
        ],
      };
    } catch (error) {
      throw new Error(`Neo4j Get Schema error: ${error.message}`);
    }
  }

  setupHealthCheck() {
    const app = express();
    
    app.get('/health', (req, res) => {
      res.json({ 
        status: 'healthy', 
        service: 'neo4j-mcp',
        timestamp: new Date().toISOString(),
        connection_configured: !!(this.uri && this.username && this.password),
        note: 'Simulated Neo4j operations - would use neo4j-driver in production'
      });
    });

    app.listen(this.port, () => {
      console.log(`Neo4j MCP Server health check running on port ${this.port}`);
    });
  }

  async run() {
    const transport = new StdioServerTransport();
    await this.server.connect(transport);
    console.error('Neo4j MCP server running on stdio');
  }
}

const server = new Neo4jServer();
server.run().catch(console.error);
