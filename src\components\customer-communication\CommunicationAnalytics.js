import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { BarChart3, TrendingUp, Mail, MessageSquare, Eye, MousePointer, AlertTriangle, Download, ArrowLeft } from "lucide-react";
import { CustomerCommunicationService } from "@/services/customer-communication";
const CommunicationAnalytics = ({ onBack }) => {
    const [metrics, setMetrics] = useState(null);
    const [campaigns, setCampaigns] = useState([]);
    const [dateRange, setDateRange] = useState('30d');
    const [loading, setLoading] = useState(false);
    const [reportData, setReportData] = useState(null);
    useEffect(() => {
        loadAnalyticsData();
    }, [dateRange]);
    const loadAnalyticsData = async () => {
        try {
            setLoading(true);
            // Mock data - replace with actual API calls
            const mockMetrics = {
                totalCampaigns: 24,
                activeCampaigns: 3,
                totalContacts: 2847,
                emailMetrics: {
                    sent: 15420,
                    delivered: 14892,
                    opened: 7446,
                    clicked: 1489,
                    bounced: 528,
                    deliveryRate: 96.6,
                    openRate: 50.0,
                    clickRate: 20.0,
                    bounceRate: 3.4
                },
                smsMetrics: {
                    sent: 8340,
                    delivered: 8173,
                    clicked: 817,
                    failed: 167,
                    deliveryRate: 98.0,
                    clickRate: 10.0
                }
            };
            const mockCampaigns = [
                {
                    id: '1',
                    name: 'Welcome Series - New Customers',
                    type: 'email',
                    status: 'sent',
                    templateId: 'welcome-template',
                    targetSegments: ['new-customers'],
                    contactIds: [],
                    metrics: {
                        sent: 245,
                        delivered: 238,
                        opened: 119,
                        clicked: 24,
                        bounced: 7,
                        unsubscribed: 2
                    },
                    createdAt: '2025-05-25T10:00:00Z',
                    updatedAt: '2025-05-26T08:30:00Z'
                },
                {
                    id: '2',
                    name: 'Product Update Announcement',
                    type: 'email',
                    status: 'sent',
                    templateId: 'product-update',
                    targetSegments: ['active-customers'],
                    contactIds: [],
                    metrics: {
                        sent: 1823,
                        delivered: 1756,
                        opened: 878,
                        clicked: 175,
                        bounced: 67,
                        unsubscribed: 12
                    },
                    createdAt: '2025-05-24T14:00:00Z',
                    updatedAt: '2025-05-24T16:45:00Z'
                },
                {
                    id: '3',
                    name: 'Flash Sale Alert',
                    type: 'sms',
                    status: 'sent',
                    templateId: 'flash-sale-sms',
                    targetSegments: ['vip-customers'],
                    contactIds: [],
                    metrics: {
                        sent: 156,
                        delivered: 153,
                        opened: 0,
                        clicked: 15,
                        bounced: 3,
                        unsubscribed: 1
                    },
                    createdAt: '2025-05-26T09:00:00Z',
                    updatedAt: '2025-05-26T09:30:00Z'
                }
            ];
            // Generate mock report data
            const mockReportData = CustomerCommunicationService.generateCommunicationReport(mockCampaigns, [], // Mock logs would go here
            {
                start: getDateRangeStart(dateRange),
                end: new Date().toISOString()
            });
            setMetrics(mockMetrics);
            setCampaigns(mockCampaigns);
            setReportData(mockReportData);
        }
        catch (error) {
            console.error('Failed to load analytics data:', error);
        }
        finally {
            setLoading(false);
        }
    };
    const getDateRangeStart = (range) => {
        const now = new Date();
        switch (range) {
            case '7d':
                return new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000).toISOString();
            case '30d':
                return new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000).toISOString();
            case '90d':
                return new Date(now.getTime() - 90 * 24 * 60 * 60 * 1000).toISOString();
            default:
                return new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000).toISOString();
        }
    };
    const formatPercentage = (value) => `${value.toFixed(1)}%`;
    const formatNumber = (value) => value.toLocaleString();
    const getPerformanceColor = (rate, type) => {
        if (type === 'good') {
            return rate >= 40 ? 'text-green-600' : rate >= 20 ? 'text-yellow-600' : 'text-red-600';
        }
        else {
            return rate <= 5 ? 'text-green-600' : rate <= 10 ? 'text-yellow-600' : 'text-red-600';
        }
    };
    if (loading) {
        return (_jsx("div", { className: "flex items-center justify-center h-64", children: _jsxs("div", { className: "text-center", children: [_jsx("div", { className: "animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto" }), _jsx("p", { className: "mt-2 text-gray-600", children: "Loading analytics..." })] }) }));
    }
    return (_jsxs("div", { className: "space-y-6", children: [_jsxs("div", { className: "flex items-center justify-between", children: [_jsxs("div", { className: "flex items-center gap-4", children: [onBack && (_jsxs(Button, { variant: "ghost", onClick: onBack, children: [_jsx(ArrowLeft, { className: "h-4 w-4 mr-2" }), "Back"] })), _jsxs("div", { children: [_jsx("h1", { className: "text-3xl font-bold tracking-tight", children: "Communication Analytics" }), _jsx("p", { className: "text-gray-600", children: "Performance metrics and insights for your campaigns" })] })] }), _jsxs("div", { className: "flex gap-2", children: [_jsxs(Select, { value: dateRange, onValueChange: setDateRange, children: [_jsx(SelectTrigger, { className: "w-32", children: _jsx(SelectValue, {}) }), _jsxs(SelectContent, { children: [_jsx(SelectItem, { value: "7d", children: "Last 7 days" }), _jsx(SelectItem, { value: "30d", children: "Last 30 days" }), _jsx(SelectItem, { value: "90d", children: "Last 90 days" })] })] }), _jsxs(Button, { variant: "outline", children: [_jsx(Download, { className: "h-4 w-4 mr-2" }), "Export"] })] })] }), _jsxs("div", { className: "grid gap-4 md:grid-cols-2 lg:grid-cols-4", children: [_jsxs(Card, { children: [_jsxs(CardHeader, { className: "flex flex-row items-center justify-between space-y-0 pb-2", children: [_jsx(CardTitle, { className: "text-sm font-medium", children: "Total Campaigns" }), _jsx(BarChart3, { className: "h-4 w-4 text-muted-foreground" })] }), _jsxs(CardContent, { children: [_jsx("div", { className: "text-2xl font-bold", children: metrics?.totalCampaigns || 0 }), _jsxs("p", { className: "text-xs text-muted-foreground", children: [metrics?.activeCampaigns || 0, " currently active"] })] })] }), _jsxs(Card, { children: [_jsxs(CardHeader, { className: "flex flex-row items-center justify-between space-y-0 pb-2", children: [_jsx(CardTitle, { className: "text-sm font-medium", children: "Messages Sent" }), _jsx(Mail, { className: "h-4 w-4 text-muted-foreground" })] }), _jsxs(CardContent, { children: [_jsx("div", { className: "text-2xl font-bold", children: formatNumber((metrics?.emailMetrics.sent || 0) + (metrics?.smsMetrics.sent || 0)) }), _jsxs("p", { className: "text-xs text-muted-foreground", children: [formatNumber(metrics?.emailMetrics.sent || 0), " emails, ", formatNumber(metrics?.smsMetrics.sent || 0), " SMS"] })] })] }), _jsxs(Card, { children: [_jsxs(CardHeader, { className: "flex flex-row items-center justify-between space-y-0 pb-2", children: [_jsx(CardTitle, { className: "text-sm font-medium", children: "Avg. Open Rate" }), _jsx(Eye, { className: "h-4 w-4 text-muted-foreground" })] }), _jsxs(CardContent, { children: [_jsx("div", { className: `text-2xl font-bold ${getPerformanceColor(metrics?.emailMetrics.openRate || 0, 'good')}`, children: formatPercentage(metrics?.emailMetrics.openRate || 0) }), _jsxs("p", { className: "text-xs text-muted-foreground", children: [formatNumber(metrics?.emailMetrics.opened || 0), " emails opened"] })] })] }), _jsxs(Card, { children: [_jsxs(CardHeader, { className: "flex flex-row items-center justify-between space-y-0 pb-2", children: [_jsx(CardTitle, { className: "text-sm font-medium", children: "Avg. Click Rate" }), _jsx(MousePointer, { className: "h-4 w-4 text-muted-foreground" })] }), _jsxs(CardContent, { children: [_jsx("div", { className: `text-2xl font-bold ${getPerformanceColor(metrics?.emailMetrics.clickRate || 0, 'good')}`, children: formatPercentage(metrics?.emailMetrics.clickRate || 0) }), _jsxs("p", { className: "text-xs text-muted-foreground", children: [formatNumber(metrics?.emailMetrics.clicked || 0), " clicks total"] })] })] })] }), _jsxs("div", { className: "grid gap-6 md:grid-cols-2", children: [_jsxs(Card, { children: [_jsxs(CardHeader, { children: [_jsxs(CardTitle, { className: "flex items-center gap-2", children: [_jsx(Mail, { className: "h-5 w-5" }), "Email Performance"] }), _jsx(CardDescription, { children: "Email campaign metrics and trends" })] }), _jsx(CardContent, { children: _jsxs("div", { className: "space-y-4", children: [_jsxs("div", { className: "grid grid-cols-2 gap-4", children: [_jsxs("div", { className: "text-center p-3 bg-blue-50 rounded", children: [_jsx("div", { className: "text-2xl font-bold text-blue-600", children: formatPercentage(metrics?.emailMetrics.deliveryRate || 0) }), _jsx("div", { className: "text-sm text-gray-600", children: "Delivery Rate" })] }), _jsxs("div", { className: "text-center p-3 bg-green-50 rounded", children: [_jsx("div", { className: "text-2xl font-bold text-green-600", children: formatPercentage(metrics?.emailMetrics.openRate || 0) }), _jsx("div", { className: "text-sm text-gray-600", children: "Open Rate" })] })] }), _jsxs("div", { className: "grid grid-cols-2 gap-4", children: [_jsxs("div", { className: "text-center p-3 bg-purple-50 rounded", children: [_jsx("div", { className: "text-2xl font-bold text-purple-600", children: formatPercentage(metrics?.emailMetrics.clickRate || 0) }), _jsx("div", { className: "text-sm text-gray-600", children: "Click Rate" })] }), _jsxs("div", { className: "text-center p-3 bg-red-50 rounded", children: [_jsx("div", { className: "text-2xl font-bold text-red-600", children: formatPercentage(metrics?.emailMetrics.bounceRate || 0) }), _jsx("div", { className: "text-sm text-gray-600", children: "Bounce Rate" })] })] })] }) })] }), _jsxs(Card, { children: [_jsxs(CardHeader, { children: [_jsxs(CardTitle, { className: "flex items-center gap-2", children: [_jsx(MessageSquare, { className: "h-5 w-5" }), "SMS Performance"] }), _jsx(CardDescription, { children: "SMS campaign metrics and trends" })] }), _jsx(CardContent, { children: _jsxs("div", { className: "space-y-4", children: [_jsxs("div", { className: "grid grid-cols-2 gap-4", children: [_jsxs("div", { className: "text-center p-3 bg-blue-50 rounded", children: [_jsx("div", { className: "text-2xl font-bold text-blue-600", children: formatPercentage(metrics?.smsMetrics.deliveryRate || 0) }), _jsx("div", { className: "text-sm text-gray-600", children: "Delivery Rate" })] }), _jsxs("div", { className: "text-center p-3 bg-green-50 rounded", children: [_jsx("div", { className: "text-2xl font-bold text-green-600", children: formatPercentage(metrics?.smsMetrics.clickRate || 0) }), _jsx("div", { className: "text-sm text-gray-600", children: "Click Rate" })] })] }), _jsxs("div", { className: "grid grid-cols-2 gap-4", children: [_jsxs("div", { className: "text-center p-3 bg-gray-50 rounded", children: [_jsx("div", { className: "text-2xl font-bold text-gray-600", children: formatNumber(metrics?.smsMetrics.sent || 0) }), _jsx("div", { className: "text-sm text-gray-600", children: "Total Sent" })] }), _jsxs("div", { className: "text-center p-3 bg-red-50 rounded", children: [_jsx("div", { className: "text-2xl font-bold text-red-600", children: formatNumber(metrics?.smsMetrics.failed || 0) }), _jsx("div", { className: "text-sm text-gray-600", children: "Failed" })] })] })] }) })] })] }), _jsxs(Card, { children: [_jsxs(CardHeader, { children: [_jsx(CardTitle, { children: "Campaign Performance" }), _jsx(CardDescription, { children: "Individual campaign metrics and results" })] }), _jsx(CardContent, { children: _jsx("div", { className: "overflow-x-auto", children: _jsxs("table", { className: "w-full", children: [_jsx("thead", { className: "border-b", children: _jsxs("tr", { children: [_jsx("th", { className: "text-left p-3", children: "Campaign" }), _jsx("th", { className: "text-left p-3", children: "Type" }), _jsx("th", { className: "text-left p-3", children: "Sent" }), _jsx("th", { className: "text-left p-3", children: "Delivered" }), _jsx("th", { className: "text-left p-3", children: "Opened" }), _jsx("th", { className: "text-left p-3", children: "Clicked" }), _jsx("th", { className: "text-left p-3", children: "Open Rate" }), _jsx("th", { className: "text-left p-3", children: "Click Rate" })] }) }), _jsx("tbody", { children: campaigns.map((campaign) => {
                                            const openRate = campaign.metrics.delivered > 0
                                                ? (campaign.metrics.opened / campaign.metrics.delivered) * 100
                                                : 0;
                                            const clickRate = campaign.metrics.opened > 0
                                                ? (campaign.metrics.clicked / campaign.metrics.opened) * 100
                                                : 0;
                                            return (_jsxs("tr", { className: "border-b hover:bg-gray-50", children: [_jsx("td", { className: "p-3", children: _jsxs("div", { children: [_jsx("div", { className: "font-medium", children: campaign.name }), _jsx("div", { className: "text-sm text-gray-500", children: new Date(campaign.createdAt).toLocaleDateString() })] }) }), _jsx("td", { className: "p-3", children: _jsxs("div", { className: "flex items-center gap-2", children: [campaign.type === 'email' ? (_jsx(Mail, { className: "h-4 w-4" })) : (_jsx(MessageSquare, { className: "h-4 w-4" })), campaign.type.toUpperCase()] }) }), _jsx("td", { className: "p-3", children: formatNumber(campaign.metrics.sent) }), _jsx("td", { className: "p-3", children: formatNumber(campaign.metrics.delivered) }), _jsx("td", { className: "p-3", children: formatNumber(campaign.metrics.opened) }), _jsx("td", { className: "p-3", children: formatNumber(campaign.metrics.clicked) }), _jsx("td", { className: "p-3", children: _jsx("span", { className: getPerformanceColor(openRate, 'good'), children: formatPercentage(openRate) }) }), _jsx("td", { className: "p-3", children: _jsx("span", { className: getPerformanceColor(clickRate, 'good'), children: formatPercentage(clickRate) }) })] }, campaign.id));
                                        }) })] }) }) })] }), _jsxs("div", { className: "grid gap-6 md:grid-cols-2", children: [_jsxs(Card, { children: [_jsxs(CardHeader, { children: [_jsxs(CardTitle, { className: "flex items-center gap-2", children: [_jsx(TrendingUp, { className: "h-5 w-5" }), "Key Insights"] }), _jsx(CardDescription, { children: "Performance insights from your campaigns" })] }), _jsx(CardContent, { children: _jsxs("div", { className: "space-y-3", children: [_jsxs("div", { className: "flex items-start gap-3", children: [_jsx("div", { className: "w-2 h-2 bg-green-500 rounded-full mt-2" }), _jsxs("div", { children: [_jsx("p", { className: "text-sm font-medium", children: "Strong Email Performance" }), _jsxs("p", { className: "text-xs text-gray-600", children: ["Your email open rate of ", formatPercentage(metrics?.emailMetrics.openRate || 0), " is above industry average"] })] })] }), _jsxs("div", { className: "flex items-start gap-3", children: [_jsx("div", { className: "w-2 h-2 bg-blue-500 rounded-full mt-2" }), _jsxs("div", { children: [_jsx("p", { className: "text-sm font-medium", children: "High SMS Delivery" }), _jsxs("p", { className: "text-xs text-gray-600", children: ["SMS delivery rate of ", formatPercentage(metrics?.smsMetrics.deliveryRate || 0), " shows excellent reach"] })] })] }), _jsxs("div", { className: "flex items-start gap-3", children: [_jsx("div", { className: "w-2 h-2 bg-yellow-500 rounded-full mt-2" }), _jsxs("div", { children: [_jsx("p", { className: "text-sm font-medium", children: "Growing Audience" }), _jsxs("p", { className: "text-xs text-gray-600", children: ["Total contacts reached: ", formatNumber(metrics?.totalContacts || 0)] })] })] })] }) })] }), _jsxs(Card, { children: [_jsxs(CardHeader, { children: [_jsxs(CardTitle, { className: "flex items-center gap-2", children: [_jsx(AlertTriangle, { className: "h-5 w-5" }), "Recommendations"] }), _jsx(CardDescription, { children: "Suggestions to improve performance" })] }), _jsx(CardContent, { children: _jsxs("div", { className: "space-y-3", children: [_jsxs("div", { className: "flex items-start gap-3", children: [_jsx("div", { className: "w-2 h-2 bg-orange-500 rounded-full mt-2" }), _jsxs("div", { children: [_jsx("p", { className: "text-sm font-medium", children: "Optimize Subject Lines" }), _jsx("p", { className: "text-xs text-gray-600", children: "A/B test different subject lines to improve open rates further" })] })] }), _jsxs("div", { className: "flex items-start gap-3", children: [_jsx("div", { className: "w-2 h-2 bg-purple-500 rounded-full mt-2" }), _jsxs("div", { children: [_jsx("p", { className: "text-sm font-medium", children: "Segment Targeting" }), _jsx("p", { className: "text-xs text-gray-600", children: "Create more specific segments for better personalization" })] })] }), _jsxs("div", { className: "flex items-start gap-3", children: [_jsx("div", { className: "w-2 h-2 bg-red-500 rounded-full mt-2" }), _jsxs("div", { children: [_jsx("p", { className: "text-sm font-medium", children: "Reduce Bounce Rate" }), _jsx("p", { className: "text-xs text-gray-600", children: "Clean your email list to improve deliverability" })] })] })] }) })] })] })] }));
};
export default CommunicationAnalytics;
