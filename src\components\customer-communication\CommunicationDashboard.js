import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Mail, MessageSquare, Users, Send, BarChart3, Settings, Plus, Eye, Edit } from "lucide-react";
const CommunicationDashboard = ({ onCreateCampaign, onViewTemplates, onViewContacts, onViewAnalytics }) => {
    const [metrics, setMetrics] = useState(null);
    const [recentCampaigns, setRecentCampaigns] = useState([]);
    const [loading, setLoading] = useState(true);
    useEffect(() => {
        loadDashboardData();
    }, []);
    const loadDashboardData = async () => {
        try {
            setLoading(true);
            // Mock data - replace with actual API calls
            const mockMetrics = {
                totalCampaigns: 24,
                activeCampaigns: 3,
                totalContacts: 2847,
                emailMetrics: {
                    sent: 15420,
                    delivered: 14892,
                    opened: 7446,
                    clicked: 1489,
                    bounced: 528,
                    deliveryRate: 96.6,
                    openRate: 50.0,
                    clickRate: 20.0,
                    bounceRate: 3.4
                },
                smsMetrics: {
                    sent: 8340,
                    delivered: 8173,
                    clicked: 817,
                    failed: 167,
                    deliveryRate: 98.0,
                    clickRate: 10.0
                }
            };
            const mockCampaigns = [
                {
                    id: '1',
                    name: 'Welcome Series - New Customers',
                    type: 'email',
                    status: 'sending',
                    templateId: 'welcome-template',
                    targetSegments: ['new-customers'],
                    contactIds: [],
                    metrics: {
                        sent: 245,
                        delivered: 238,
                        opened: 119,
                        clicked: 24,
                        bounced: 7,
                        unsubscribed: 2
                    },
                    createdAt: '2025-05-25T10:00:00Z',
                    updatedAt: '2025-05-26T08:30:00Z'
                },
                {
                    id: '2',
                    name: 'Product Update Announcement',
                    type: 'email',
                    status: 'sent',
                    templateId: 'product-update',
                    targetSegments: ['active-customers'],
                    contactIds: [],
                    metrics: {
                        sent: 1823,
                        delivered: 1756,
                        opened: 878,
                        clicked: 175,
                        bounced: 67,
                        unsubscribed: 12
                    },
                    createdAt: '2025-05-24T14:00:00Z',
                    updatedAt: '2025-05-24T16:45:00Z'
                },
                {
                    id: '3',
                    name: 'Flash Sale Alert',
                    type: 'sms',
                    status: 'scheduled',
                    templateId: 'flash-sale-sms',
                    targetSegments: ['vip-customers'],
                    contactIds: [],
                    scheduledAt: '2025-05-27T09:00:00Z',
                    metrics: {
                        sent: 0,
                        delivered: 0,
                        opened: 0,
                        clicked: 0,
                        bounced: 0,
                        unsubscribed: 0
                    },
                    createdAt: '2025-05-26T11:00:00Z',
                    updatedAt: '2025-05-26T11:00:00Z'
                }
            ];
            setMetrics(mockMetrics);
            setRecentCampaigns(mockCampaigns);
        }
        catch (error) {
            console.error('Failed to load dashboard data:', error);
        }
        finally {
            setLoading(false);
        }
    };
    const getStatusColor = (status) => {
        switch (status) {
            case 'sending': return 'text-blue-600 bg-blue-100';
            case 'sent': return 'text-green-600 bg-green-100';
            case 'scheduled': return 'text-yellow-600 bg-yellow-100';
            case 'paused': return 'text-orange-600 bg-orange-100';
            case 'cancelled': return 'text-red-600 bg-red-100';
            default: return 'text-gray-600 bg-gray-100';
        }
    };
    const formatPercentage = (value) => `${value.toFixed(1)}%`;
    const formatNumber = (value) => value.toLocaleString();
    if (loading) {
        return (_jsx("div", { className: "flex items-center justify-center h-64", children: _jsxs("div", { className: "text-center", children: [_jsx("div", { className: "animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto" }), _jsx("p", { className: "mt-2 text-gray-600", children: "Loading communication dashboard..." })] }) }));
    }
    return (_jsxs("div", { className: "space-y-6", children: [_jsxs("div", { className: "flex justify-between items-center", children: [_jsxs("div", { children: [_jsx("h1", { className: "text-3xl font-bold tracking-tight", children: "Communication Dashboard" }), _jsx("p", { className: "text-gray-600", children: "Manage email and SMS campaigns, templates, and analytics" })] }), _jsxs("div", { className: "flex gap-2", children: [_jsxs(Button, { onClick: onCreateCampaign, className: "flex items-center gap-2", children: [_jsx(Plus, { className: "h-4 w-4" }), "Create Campaign"] }), _jsxs(Button, { variant: "outline", onClick: onViewTemplates, children: [_jsx(Edit, { className: "h-4 w-4 mr-2" }), "Templates"] })] })] }), _jsxs("div", { className: "grid gap-4 md:grid-cols-2 lg:grid-cols-4", children: [_jsxs(Card, { children: [_jsxs(CardHeader, { className: "flex flex-row items-center justify-between space-y-0 pb-2", children: [_jsx(CardTitle, { className: "text-sm font-medium", children: "Total Campaigns" }), _jsx(Send, { className: "h-4 w-4 text-muted-foreground" })] }), _jsxs(CardContent, { children: [_jsx("div", { className: "text-2xl font-bold", children: metrics?.totalCampaigns || 0 }), _jsxs("p", { className: "text-xs text-muted-foreground", children: [metrics?.activeCampaigns || 0, " active campaigns"] })] })] }), _jsxs(Card, { children: [_jsxs(CardHeader, { className: "flex flex-row items-center justify-between space-y-0 pb-2", children: [_jsx(CardTitle, { className: "text-sm font-medium", children: "Total Contacts" }), _jsx(Users, { className: "h-4 w-4 text-muted-foreground" })] }), _jsxs(CardContent, { children: [_jsx("div", { className: "text-2xl font-bold", children: formatNumber(metrics?.totalContacts || 0) }), _jsx("p", { className: "text-xs text-muted-foreground", children: "Across all segments" })] })] }), _jsxs(Card, { children: [_jsxs(CardHeader, { className: "flex flex-row items-center justify-between space-y-0 pb-2", children: [_jsx(CardTitle, { className: "text-sm font-medium", children: "Email Open Rate" }), _jsx(Mail, { className: "h-4 w-4 text-muted-foreground" })] }), _jsxs(CardContent, { children: [_jsx("div", { className: "text-2xl font-bold", children: formatPercentage(metrics?.emailMetrics.openRate || 0) }), _jsxs("p", { className: "text-xs text-muted-foreground", children: [formatNumber(metrics?.emailMetrics.opened || 0), " emails opened"] })] })] }), _jsxs(Card, { children: [_jsxs(CardHeader, { className: "flex flex-row items-center justify-between space-y-0 pb-2", children: [_jsx(CardTitle, { className: "text-sm font-medium", children: "SMS Delivery Rate" }), _jsx(MessageSquare, { className: "h-4 w-4 text-muted-foreground" })] }), _jsxs(CardContent, { children: [_jsx("div", { className: "text-2xl font-bold", children: formatPercentage(metrics?.smsMetrics.deliveryRate || 0) }), _jsxs("p", { className: "text-xs text-muted-foreground", children: [formatNumber(metrics?.smsMetrics.delivered || 0), " SMS delivered"] })] })] })] }), _jsxs("div", { className: "grid gap-6 md:grid-cols-2", children: [_jsxs(Card, { children: [_jsxs(CardHeader, { children: [_jsxs(CardTitle, { className: "flex items-center gap-2", children: [_jsx(Mail, { className: "h-5 w-5" }), "Email Performance"] }), _jsx(CardDescription, { children: "Email campaign metrics and engagement" })] }), _jsx(CardContent, { children: _jsxs("div", { className: "space-y-4", children: [_jsxs("div", { className: "flex justify-between items-center", children: [_jsx("span", { className: "text-sm text-gray-600", children: "Delivery Rate" }), _jsx("span", { className: "font-semibold", children: formatPercentage(metrics?.emailMetrics.deliveryRate || 0) })] }), _jsxs("div", { className: "flex justify-between items-center", children: [_jsx("span", { className: "text-sm text-gray-600", children: "Open Rate" }), _jsx("span", { className: "font-semibold", children: formatPercentage(metrics?.emailMetrics.openRate || 0) })] }), _jsxs("div", { className: "flex justify-between items-center", children: [_jsx("span", { className: "text-sm text-gray-600", children: "Click Rate" }), _jsx("span", { className: "font-semibold", children: formatPercentage(metrics?.emailMetrics.clickRate || 0) })] }), _jsxs("div", { className: "flex justify-between items-center", children: [_jsx("span", { className: "text-sm text-gray-600", children: "Bounce Rate" }), _jsx("span", { className: "font-semibold text-red-600", children: formatPercentage(metrics?.emailMetrics.bounceRate || 0) })] })] }) })] }), _jsxs(Card, { children: [_jsxs(CardHeader, { children: [_jsxs(CardTitle, { className: "flex items-center gap-2", children: [_jsx(MessageSquare, { className: "h-5 w-5" }), "SMS Performance"] }), _jsx(CardDescription, { children: "SMS campaign metrics and engagement" })] }), _jsx(CardContent, { children: _jsxs("div", { className: "space-y-4", children: [_jsxs("div", { className: "flex justify-between items-center", children: [_jsx("span", { className: "text-sm text-gray-600", children: "Delivery Rate" }), _jsx("span", { className: "font-semibold", children: formatPercentage(metrics?.smsMetrics.deliveryRate || 0) })] }), _jsxs("div", { className: "flex justify-between items-center", children: [_jsx("span", { className: "text-sm text-gray-600", children: "Click Rate" }), _jsx("span", { className: "font-semibold", children: formatPercentage(metrics?.smsMetrics.clickRate || 0) })] }), _jsxs("div", { className: "flex justify-between items-center", children: [_jsx("span", { className: "text-sm text-gray-600", children: "Messages Sent" }), _jsx("span", { className: "font-semibold", children: formatNumber(metrics?.smsMetrics.sent || 0) })] }), _jsxs("div", { className: "flex justify-between items-center", children: [_jsx("span", { className: "text-sm text-gray-600", children: "Failed Deliveries" }), _jsx("span", { className: "font-semibold text-red-600", children: formatNumber(metrics?.smsMetrics.failed || 0) })] })] }) })] })] }), _jsxs(Card, { children: [_jsxs(CardHeader, { children: [_jsx(CardTitle, { children: "Recent Campaigns" }), _jsx(CardDescription, { children: "Latest email and SMS campaigns" })] }), _jsx(CardContent, { children: _jsx("div", { className: "space-y-4", children: recentCampaigns.map((campaign) => (_jsxs("div", { className: "flex items-center justify-between p-4 border rounded-lg", children: [_jsxs("div", { className: "flex items-center space-x-4", children: [_jsx("div", { className: "w-10 h-10 rounded-full bg-blue-100 flex items-center justify-center", children: campaign.type === 'email' ? (_jsx(Mail, { className: "h-5 w-5 text-blue-600" })) : (_jsx(MessageSquare, { className: "h-5 w-5 text-blue-600" })) }), _jsxs("div", { children: [_jsx("p", { className: "font-medium", children: campaign.name }), _jsxs("p", { className: "text-sm text-gray-600", children: [campaign.type.toUpperCase(), " \u2022 Created ", new Date(campaign.createdAt).toLocaleDateString()] })] })] }), _jsxs("div", { className: "flex items-center space-x-4", children: [_jsxs("div", { className: "text-right", children: [_jsxs("p", { className: "text-sm font-medium", children: [formatNumber(campaign.metrics.sent), " sent"] }), _jsx("p", { className: "text-xs text-gray-600", children: campaign.metrics.delivered > 0 &&
                                                            `${formatPercentage((campaign.metrics.opened / campaign.metrics.delivered) * 100)} opened` })] }), _jsx("span", { className: `px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(campaign.status)}`, children: campaign.status }), _jsxs("div", { className: "flex space-x-1", children: [_jsx(Button, { variant: "ghost", size: "sm", children: _jsx(Eye, { className: "h-4 w-4" }) }), _jsx(Button, { variant: "ghost", size: "sm", children: _jsx(Edit, { className: "h-4 w-4" }) })] })] })] }, campaign.id))) }) })] }), _jsxs("div", { className: "grid gap-4 md:grid-cols-3", children: [_jsxs(Card, { className: "cursor-pointer hover:shadow-md transition-shadow", onClick: onViewContacts, children: [_jsxs(CardHeader, { children: [_jsxs(CardTitle, { className: "flex items-center gap-2", children: [_jsx(Users, { className: "h-5 w-5" }), "Contact Management"] }), _jsx(CardDescription, { children: "Manage contacts and segments" })] }), _jsx(CardContent, { children: _jsx("p", { className: "text-sm text-gray-600", children: "View and organize your contact lists, create segments, and manage preferences." }) })] }), _jsxs(Card, { className: "cursor-pointer hover:shadow-md transition-shadow", onClick: onViewAnalytics, children: [_jsxs(CardHeader, { children: [_jsxs(CardTitle, { className: "flex items-center gap-2", children: [_jsx(BarChart3, { className: "h-5 w-5" }), "Analytics & Reports"] }), _jsx(CardDescription, { children: "Detailed communication analytics" })] }), _jsx(CardContent, { children: _jsx("p", { className: "text-sm text-gray-600", children: "Access detailed reports, performance metrics, and communication insights." }) })] }), _jsxs(Card, { className: "cursor-pointer hover:shadow-md transition-shadow", children: [_jsxs(CardHeader, { children: [_jsxs(CardTitle, { className: "flex items-center gap-2", children: [_jsx(Settings, { className: "h-5 w-5" }), "Communication Settings"] }), _jsx(CardDescription, { children: "Configure communication preferences" })] }), _jsx(CardContent, { children: _jsx("p", { className: "text-sm text-gray-600", children: "Set up email/SMS providers, configure templates, and manage automation rules." }) })] })] })] }));
};
export default CommunicationDashboard;
