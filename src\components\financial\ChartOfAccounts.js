import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger, } from '@/components/ui/dialog';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue, } from '@/components/ui/select';
import { Label } from '@/components/ui/label';
import { Plus, Search, Edit, Trash2, DollarSign, TrendingUp, TrendingDown, Building, CreditCard, Loader2 } from 'lucide-react';
import { AccountService } from '../../services/financial';
import { AccountType, AccountCategory } from '../../types/financial';
export const ChartOfAccounts = () => {
    const [accounts, setAccounts] = useState([]);
    const [loading, setLoading] = useState(true);
    const [searchTerm, setSearchTerm] = useState('');
    const [selectedType, setSelectedType] = useState('ALL');
    const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
    const [newAccount, setNewAccount] = useState({
        code: '',
        name: '',
        type: AccountType.ASSET,
        category: AccountCategory.CURRENT_ASSET,
        currency: 'USD'
    });
    useEffect(() => {
        loadAccounts();
    }, []);
    const loadAccounts = async () => {
        try {
            setLoading(true);
            const data = await AccountService.getChartOfAccounts();
            setAccounts(data);
        }
        catch (error) {
            console.error('Error loading accounts:', error);
        }
        finally {
            setLoading(false);
        }
    };
    const handleCreateAccount = async () => {
        try {
            await AccountService.createAccount({
                ...newAccount,
                balance: 0,
                isActive: true
            });
            setIsCreateDialogOpen(false);
            setNewAccount({
                code: '',
                name: '',
                type: AccountType.ASSET,
                category: AccountCategory.CURRENT_ASSET,
                currency: 'USD'
            });
            loadAccounts();
        }
        catch (error) {
            console.error('Error creating account:', error);
        }
    };
    const getAccountTypeIcon = (type) => {
        switch (type) {
            case AccountType.ASSET:
                return _jsx(Building, { className: "h-4 w-4" });
            case AccountType.LIABILITY:
                return _jsx(CreditCard, { className: "h-4 w-4" });
            case AccountType.EQUITY:
                return _jsx(DollarSign, { className: "h-4 w-4" });
            case AccountType.REVENUE:
                return _jsx(TrendingUp, { className: "h-4 w-4" });
            case AccountType.EXPENSE:
                return _jsx(TrendingDown, { className: "h-4 w-4" });
            default:
                return _jsx(DollarSign, { className: "h-4 w-4" });
        }
    };
    const getAccountTypeColor = (type) => {
        switch (type) {
            case AccountType.ASSET:
                return 'bg-blue-100 text-blue-800';
            case AccountType.LIABILITY:
                return 'bg-red-100 text-red-800';
            case AccountType.EQUITY:
                return 'bg-purple-100 text-purple-800';
            case AccountType.REVENUE:
                return 'bg-green-100 text-green-800';
            case AccountType.EXPENSE:
                return 'bg-orange-100 text-orange-800';
            default:
                return 'bg-gray-100 text-gray-800';
        }
    };
    const formatCurrency = (amount) => {
        return new Intl.NumberFormat('en-US', {
            style: 'currency',
            currency: 'USD'
        }).format(amount);
    };
    const filteredAccounts = accounts.filter(account => {
        const matchesSearch = account.code.toLowerCase().includes(searchTerm.toLowerCase()) ||
            account.name.toLowerCase().includes(searchTerm.toLowerCase());
        const matchesType = selectedType === 'ALL' || account.type === selectedType;
        return matchesSearch && matchesType;
    });
    if (loading) {
        return (_jsx("div", { className: "flex items-center justify-center h-64", children: _jsxs("div", { className: "flex items-center space-x-2", children: [_jsx(Loader2, { className: "h-6 w-6 animate-spin" }), _jsx("span", { children: "Loading chart of accounts..." })] }) }));
    }
    return (_jsxs("div", { className: "space-y-6", children: [_jsxs("div", { className: "flex justify-between items-center", children: [_jsxs("div", { children: [_jsx("h1", { className: "text-3xl font-bold tracking-tight", children: "Chart of Accounts" }), _jsx("p", { className: "text-muted-foreground", children: "Manage your account structure and classifications" })] }), _jsxs(Dialog, { open: isCreateDialogOpen, onOpenChange: setIsCreateDialogOpen, children: [_jsx(DialogTrigger, { asChild: true, children: _jsxs(Button, { children: [_jsx(Plus, { className: "h-4 w-4 mr-2" }), "Add Account"] }) }), _jsxs(DialogContent, { className: "sm:max-w-[425px]", children: [_jsxs(DialogHeader, { children: [_jsx(DialogTitle, { children: "Create New Account" }), _jsx(DialogDescription, { children: "Add a new account to your chart of accounts." })] }), _jsxs("div", { className: "grid gap-4 py-4", children: [_jsxs("div", { className: "grid grid-cols-4 items-center gap-4", children: [_jsx(Label, { htmlFor: "code", className: "text-right", children: "Code" }), _jsx(Input, { id: "code", placeholder: "e.g., 1000", value: newAccount.code, onChange: (e) => setNewAccount({ ...newAccount, code: e.target.value }), className: "col-span-3" })] }), _jsxs("div", { className: "grid grid-cols-4 items-center gap-4", children: [_jsx(Label, { htmlFor: "name", className: "text-right", children: "Name" }), _jsx(Input, { id: "name", placeholder: "e.g., Cash", value: newAccount.name, onChange: (e) => setNewAccount({ ...newAccount, name: e.target.value }), className: "col-span-3" })] }), _jsxs("div", { className: "grid grid-cols-4 items-center gap-4", children: [_jsx(Label, { htmlFor: "type", className: "text-right", children: "Type" }), _jsxs(Select, { value: newAccount.type, onValueChange: (value) => setNewAccount({ ...newAccount, type: value }), children: [_jsx(SelectTrigger, { className: "col-span-3", children: _jsx(SelectValue, { placeholder: "Select type" }) }), _jsxs(SelectContent, { children: [_jsx(SelectItem, { value: AccountType.ASSET, children: "Asset" }), _jsx(SelectItem, { value: AccountType.LIABILITY, children: "Liability" }), _jsx(SelectItem, { value: AccountType.EQUITY, children: "Equity" }), _jsx(SelectItem, { value: AccountType.REVENUE, children: "Revenue" }), _jsx(SelectItem, { value: AccountType.EXPENSE, children: "Expense" })] })] })] }), _jsxs("div", { className: "grid grid-cols-4 items-center gap-4", children: [_jsx(Label, { htmlFor: "category", className: "text-right", children: "Category" }), _jsxs(Select, { value: newAccount.category, onValueChange: (value) => setNewAccount({ ...newAccount, category: value }), children: [_jsx(SelectTrigger, { className: "col-span-3", children: _jsx(SelectValue, { placeholder: "Select category" }) }), _jsxs(SelectContent, { children: [_jsx(SelectItem, { value: AccountCategory.CURRENT_ASSET, children: "Current Asset" }), _jsx(SelectItem, { value: AccountCategory.FIXED_ASSET, children: "Fixed Asset" }), _jsx(SelectItem, { value: AccountCategory.CURRENT_LIABILITY, children: "Current Liability" }), _jsx(SelectItem, { value: AccountCategory.LONG_TERM_LIABILITY, children: "Long Term Liability" }), _jsx(SelectItem, { value: AccountCategory.OWNERS_EQUITY, children: "Owner's Equity" }), _jsx(SelectItem, { value: AccountCategory.OPERATING_REVENUE, children: "Operating Revenue" }), _jsx(SelectItem, { value: AccountCategory.OPERATING_EXPENSE, children: "Operating Expense" })] })] })] })] }), _jsx(DialogFooter, { children: _jsx(Button, { onClick: handleCreateAccount, children: "Create Account" }) })] })] })] }), _jsxs(Card, { children: [_jsx(CardHeader, { children: _jsx(CardTitle, { children: "Filters" }) }), _jsx(CardContent, { children: _jsxs("div", { className: "flex space-x-4", children: [_jsx("div", { className: "flex-1", children: _jsxs("div", { className: "relative", children: [_jsx(Search, { className: "absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" }), _jsx(Input, { placeholder: "Search accounts...", value: searchTerm, onChange: (e) => setSearchTerm(e.target.value), className: "pl-8" })] }) }), _jsxs(Select, { value: selectedType, onValueChange: setSelectedType, children: [_jsx(SelectTrigger, { className: "w-[180px]", children: _jsx(SelectValue, { placeholder: "Account Type" }) }), _jsxs(SelectContent, { children: [_jsx(SelectItem, { value: "ALL", children: "All Types" }), _jsx(SelectItem, { value: AccountType.ASSET, children: "Asset" }), _jsx(SelectItem, { value: AccountType.LIABILITY, children: "Liability" }), _jsx(SelectItem, { value: AccountType.EQUITY, children: "Equity" }), _jsx(SelectItem, { value: AccountType.REVENUE, children: "Revenue" }), _jsx(SelectItem, { value: AccountType.EXPENSE, children: "Expense" })] })] })] }) })] }), _jsxs(Card, { children: [_jsxs(CardHeader, { children: [_jsx(CardTitle, { children: "Accounts" }), _jsxs(CardDescription, { children: [filteredAccounts.length, " accounts found"] })] }), _jsx(CardContent, { children: _jsxs(Table, { children: [_jsx(TableHeader, { children: _jsxs(TableRow, { children: [_jsx(TableHead, { children: "Code" }), _jsx(TableHead, { children: "Name" }), _jsx(TableHead, { children: "Type" }), _jsx(TableHead, { children: "Category" }), _jsx(TableHead, { className: "text-right", children: "Balance" }), _jsx(TableHead, { children: "Status" }), _jsx(TableHead, { className: "text-right", children: "Actions" })] }) }), _jsxs(TableBody, { children: [filteredAccounts.map((account) => (_jsxs(TableRow, { children: [_jsx(TableCell, { className: "font-medium", children: account.code }), _jsx(TableCell, { children: account.name }), _jsx(TableCell, { children: _jsx(Badge, { className: getAccountTypeColor(account.type), children: _jsxs("div", { className: "flex items-center space-x-1", children: [getAccountTypeIcon(account.type), _jsx("span", { children: account.type })] }) }) }), _jsx(TableCell, { children: _jsx("span", { className: "text-sm text-muted-foreground", children: account.category.replace(/_/g, ' ') }) }), _jsx(TableCell, { className: "text-right font-medium", children: formatCurrency(account.balance) }), _jsx(TableCell, { children: _jsx(Badge, { variant: account.isActive ? "default" : "secondary", children: account.isActive ? 'Active' : 'Inactive' }) }), _jsx(TableCell, { className: "text-right", children: _jsxs("div", { className: "flex justify-end space-x-2", children: [_jsx(Button, { variant: "ghost", size: "sm", children: _jsx(Edit, { className: "h-4 w-4" }) }), _jsx(Button, { variant: "ghost", size: "sm", children: _jsx(Trash2, { className: "h-4 w-4" }) })] }) })] }, account.id))), filteredAccounts.length === 0 && (_jsx(TableRow, { children: _jsx(TableCell, { colSpan: 7, className: "text-center py-8", children: _jsxs("div", { className: "text-muted-foreground", children: ["No accounts found. ", accounts.length === 0 ? 'Create your first account to get started.' : 'Try adjusting your search criteria.'] }) }) }))] })] }) })] })] }));
};
export default ChartOfAccounts;
