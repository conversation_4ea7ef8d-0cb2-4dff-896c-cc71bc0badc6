#!/usr/bin/env node

const { Server } = require('@modelcontextprotocol/sdk/server/index.js');
const { StdioServerTransport } = require('@modelcontextprotocol/sdk/server/stdio.js');
const { CallToolRequestSchema, ListToolsRequestSchema } = require('@modelcontextprotocol/sdk/types.js');
const { Client } = require('@notionhq/client');
const express = require('express');

class NotionServer {
  constructor() {
    this.server = new Server(
      {
        name: 'notion-server',
        version: '1.0.0',
      },
      {
        capabilities: {
          tools: {},
        },
      }
    );

    this.apiKey = process.env.NOTION_API_KEY;
    this.port = process.env.MCP_PORT || 8084;
    
    if (this.apiKey) {
      this.notion = new Client({
        auth: this.apiKey,
      });
    }
    
    this.setupToolHandlers();
    this.setupHealthCheck();
  }

  setupToolHandlers() {
    this.server.setRequestHandler(ListToolsRequestSchema, async () => {
      return {
        tools: [
          {
            name: 'notion_search',
            description: 'Search Notion pages and databases',
            inputSchema: {
              type: 'object',
              properties: {
                query: {
                  type: 'string',
                  description: 'Search query',
                },
                filter: {
                  type: 'object',
                  description: 'Search filter options',
                  properties: {
                    value: {
                      type: 'string',
                      enum: ['page', 'database'],
                    },
                    property: {
                      type: 'string',
                      enum: ['object'],
                    },
                  },
                },
                sort: {
                  type: 'object',
                  description: 'Sort options',
                  properties: {
                    direction: {
                      type: 'string',
                      enum: ['ascending', 'descending'],
                    },
                    timestamp: {
                      type: 'string',
                      enum: ['last_edited_time'],
                    },
                  },
                },
                page_size: {
                  type: 'number',
                  description: 'Number of results per page',
                  default: 10,
                  maximum: 100,
                },
              },
              required: ['query'],
            },
          },
          {
            name: 'notion_get_page',
            description: 'Get a specific Notion page by ID',
            inputSchema: {
              type: 'object',
              properties: {
                page_id: {
                  type: 'string',
                  description: 'Notion page ID',
                },
              },
              required: ['page_id'],
            },
          },
          {
            name: 'notion_get_page_content',
            description: 'Get the content blocks of a Notion page',
            inputSchema: {
              type: 'object',
              properties: {
                page_id: {
                  type: 'string',
                  description: 'Notion page ID',
                },
                page_size: {
                  type: 'number',
                  description: 'Number of blocks per page',
                  default: 100,
                },
              },
              required: ['page_id'],
            },
          },
          {
            name: 'notion_create_page',
            description: 'Create a new Notion page',
            inputSchema: {
              type: 'object',
              properties: {
                parent: {
                  type: 'object',
                  description: 'Parent page or database',
                  properties: {
                    type: {
                      type: 'string',
                      enum: ['page_id', 'database_id'],
                    },
                    page_id: {
                      type: 'string',
                    },
                    database_id: {
                      type: 'string',
                    },
                  },
                  required: ['type'],
                },
                properties: {
                  type: 'object',
                  description: 'Page properties',
                },
                children: {
                  type: 'array',
                  description: 'Page content blocks',
                  items: {
                    type: 'object',
                  },
                },
              },
              required: ['parent'],
            },
          },
          {
            name: 'notion_update_page',
            description: 'Update a Notion page',
            inputSchema: {
              type: 'object',
              properties: {
                page_id: {
                  type: 'string',
                  description: 'Notion page ID',
                },
                properties: {
                  type: 'object',
                  description: 'Properties to update',
                },
                archived: {
                  type: 'boolean',
                  description: 'Archive status',
                },
              },
              required: ['page_id'],
            },
          },
          {
            name: 'notion_query_database',
            description: 'Query a Notion database',
            inputSchema: {
              type: 'object',
              properties: {
                database_id: {
                  type: 'string',
                  description: 'Notion database ID',
                },
                filter: {
                  type: 'object',
                  description: 'Database filter',
                },
                sorts: {
                  type: 'array',
                  description: 'Sort criteria',
                  items: {
                    type: 'object',
                  },
                },
                page_size: {
                  type: 'number',
                  description: 'Number of results per page',
                  default: 100,
                },
              },
              required: ['database_id'],
            },
          },
        ],
      };
    });

    this.server.setRequestHandler(CallToolRequestSchema, async (request) => {
      const { name, arguments: args } = request.params;

      switch (name) {
        case 'notion_search':
          return await this.handleSearch(args);
        case 'notion_get_page':
          return await this.handleGetPage(args);
        case 'notion_get_page_content':
          return await this.handleGetPageContent(args);
        case 'notion_create_page':
          return await this.handleCreatePage(args);
        case 'notion_update_page':
          return await this.handleUpdatePage(args);
        case 'notion_query_database':
          return await this.handleQueryDatabase(args);
        default:
          throw new Error(`Unknown tool: ${name}`);
      }
    });
  }

  async handleSearch(args) {
    if (!this.notion) {
      throw new Error('NOTION_API_KEY environment variable is required');
    }

    try {
      const response = await this.notion.search({
        query: args.query,
        filter: args.filter,
        sort: args.sort,
        page_size: args.page_size || 10,
      });

      return {
        content: [
          {
            type: 'text',
            text: JSON.stringify({
              results: response.results,
              has_more: response.has_more,
              next_cursor: response.next_cursor,
            }, null, 2),
          },
        ],
      };
    } catch (error) {
      throw new Error(`Notion Search error: ${error.message}`);
    }
  }

  async handleGetPage(args) {
    if (!this.notion) {
      throw new Error('NOTION_API_KEY environment variable is required');
    }

    try {
      const response = await this.notion.pages.retrieve({
        page_id: args.page_id,
      });

      return {
        content: [
          {
            type: 'text',
            text: JSON.stringify(response, null, 2),
          },
        ],
      };
    } catch (error) {
      throw new Error(`Notion Get Page error: ${error.message}`);
    }
  }

  async handleGetPageContent(args) {
    if (!this.notion) {
      throw new Error('NOTION_API_KEY environment variable is required');
    }

    try {
      const response = await this.notion.blocks.children.list({
        block_id: args.page_id,
        page_size: args.page_size || 100,
      });

      return {
        content: [
          {
            type: 'text',
            text: JSON.stringify({
              results: response.results,
              has_more: response.has_more,
              next_cursor: response.next_cursor,
            }, null, 2),
          },
        ],
      };
    } catch (error) {
      throw new Error(`Notion Get Page Content error: ${error.message}`);
    }
  }

  async handleCreatePage(args) {
    if (!this.notion) {
      throw new Error('NOTION_API_KEY environment variable is required');
    }

    try {
      const response = await this.notion.pages.create({
        parent: args.parent,
        properties: args.properties || {},
        children: args.children || [],
      });

      return {
        content: [
          {
            type: 'text',
            text: JSON.stringify(response, null, 2),
          },
        ],
      };
    } catch (error) {
      throw new Error(`Notion Create Page error: ${error.message}`);
    }
  }

  async handleUpdatePage(args) {
    if (!this.notion) {
      throw new Error('NOTION_API_KEY environment variable is required');
    }

    try {
      const updateData = {
        page_id: args.page_id,
      };

      if (args.properties) updateData.properties = args.properties;
      if (args.archived !== undefined) updateData.archived = args.archived;

      const response = await this.notion.pages.update(updateData);

      return {
        content: [
          {
            type: 'text',
            text: JSON.stringify(response, null, 2),
          },
        ],
      };
    } catch (error) {
      throw new Error(`Notion Update Page error: ${error.message}`);
    }
  }

  async handleQueryDatabase(args) {
    if (!this.notion) {
      throw new Error('NOTION_API_KEY environment variable is required');
    }

    try {
      const queryData = {
        database_id: args.database_id,
        page_size: args.page_size || 100,
      };

      if (args.filter) queryData.filter = args.filter;
      if (args.sorts) queryData.sorts = args.sorts;

      const response = await this.notion.databases.query(queryData);

      return {
        content: [
          {
            type: 'text',
            text: JSON.stringify({
              results: response.results,
              has_more: response.has_more,
              next_cursor: response.next_cursor,
            }, null, 2),
          },
        ],
      };
    } catch (error) {
      throw new Error(`Notion Query Database error: ${error.message}`);
    }
  }

  setupHealthCheck() {
    const app = express();
    
    app.get('/health', (req, res) => {
      res.json({ 
        status: 'healthy', 
        service: 'notion-mcp',
        timestamp: new Date().toISOString(),
        api_key_configured: !!this.apiKey
      });
    });

    app.listen(this.port, () => {
      console.log(`Notion MCP Server health check running on port ${this.port}`);
    });
  }

  async run() {
    const transport = new StdioServerTransport();
    await this.server.connect(transport);
    console.error('Notion MCP server running on stdio');
  }
}

const server = new NotionServer();
server.run().catch(console.error);
