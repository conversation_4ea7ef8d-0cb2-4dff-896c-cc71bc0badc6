import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Badge } from '@/components/ui/badge';
import { toast } from 'sonner';
import { useBetterAuth } from '@/providers/BetterAuthProvider';
import { Permission } from '@/utils/rbac/permissions';
import AddRoleDialog from './AddRoleDialog';
import { useUserManagement } from '@/context/UserManagementContext';
const RolesTab = () => {
    const { hasPermission } = useBetterAuth();
    const { roles, permissions, addRole } = useUserManagement();
    const [roleDialogOpen, setRoleDialogOpen] = useState(false);
    const handleAddRole = (newRole) => {
        addRole(newRole);
        toast.success(`Role ${newRole.name} created successfully`);
        setRoleDialogOpen(false);
    };
    return (_jsxs(Card, { children: [_jsxs(CardHeader, { className: "pb-2", children: [_jsxs("div", { className: "flex justify-between items-center", children: [_jsx(CardTitle, { children: "All Roles" }), hasPermission(Permission.ROLE_MANAGEMENT_EDIT) && (_jsx(AddRoleDialog, { open: roleDialogOpen, onOpenChange: setRoleDialogOpen, onAddRole: handleAddRole, permissions: permissions }))] }), _jsx(CardDescription, { children: "Manage roles and their associated permissions." })] }), _jsx(CardContent, { children: _jsxs(Table, { children: [_jsx(TableHeader, { children: _jsxs(TableRow, { children: [_jsx(TableHead, { children: "Role Name" }), _jsx(TableHead, { children: "Description" }), _jsx(TableHead, { children: "Permissions" }), _jsx(TableHead, { className: "text-right", children: "Actions" })] }) }), _jsx(TableBody, { children: roles.map((role) => (_jsxs(TableRow, { children: [_jsx(TableCell, { className: "font-medium", children: role.name }), _jsx(TableCell, { children: role.description }), _jsx(TableCell, { children: _jsx("div", { className: "flex flex-wrap gap-1", children: role.permissions && role.permissions.map(p => (_jsx(Badge, { variant: "outline", className: "mr-1 mb-1", children: p }, p))) }) }), _jsx(TableCell, { className: "text-right", children: _jsx(Button, { size: "sm", variant: "outline", onClick: () => toast.info("Edit feature coming soon"), children: "Edit" }) })] }, role.id))) })] }) })] }));
};
export default RolesTab;
