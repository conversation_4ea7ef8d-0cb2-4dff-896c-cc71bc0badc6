import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Tabs, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";
import { Mail, MessageSquare, Plus, Edit, Trash2, Copy, Eye, Save, ArrowLeft, FileText, Search } from "lucide-react";
const TemplateManager = ({ onBack, onSelectTemplate }) => {
    const [activeTab, setActiveTab] = useState('email');
    const [emailTemplates, setEmailTemplates] = useState([]);
    const [smsTemplates, setSmsTemplates] = useState([]);
    const [searchTerm, setSearchTerm] = useState('');
    const [selectedTemplate, setSelectedTemplate] = useState(null);
    const [isEditing, setIsEditing] = useState(false);
    const [isCreating, setIsCreating] = useState(false);
    const [loading, setLoading] = useState(false);
    // Form state for creating/editing templates
    const [formData, setFormData] = useState({
        name: '',
        subject: '',
        content: '',
        type: 'marketing',
        variables: []
    });
    useEffect(() => {
        loadTemplates();
    }, []);
    const loadTemplates = async () => {
        try {
            setLoading(true);
            // Mock data - replace with actual API calls
            const mockEmailTemplates = [
                {
                    id: 'welcome-email',
                    name: 'Welcome Email',
                    subject: 'Welcome to {{company_name}}!',
                    content: `
            <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
              <h1 style="color: #333;">Welcome {{customer_name}}!</h1>
              <p>Thank you for joining {{company_name}}. We're excited to have you on board.</p>
              <div style="background: #f8f9fa; padding: 20px; border-radius: 8px; margin: 20px 0;">
                <h3>Here's what you can expect:</h3>
                <ul>
                  <li>Exclusive offers and discounts</li>
                  <li>Product updates and news</li>
                  <li>Priority customer support</li>
                </ul>
              </div>
              <p>Best regards,<br>The {{company_name}} Team</p>
              <div style="margin-top: 30px; padding-top: 20px; border-top: 1px solid #eee; font-size: 12px; color: #666;">
                <p>You received this email because you signed up for {{company_name}}.</p>
              </div>
            </div>
          `,
                    type: 'transactional',
                    variables: ['customer_name', 'company_name'],
                    isActive: true,
                    createdAt: '2025-05-20T10:00:00Z',
                    updatedAt: '2025-05-20T10:00:00Z'
                },
                {
                    id: 'product-update',
                    name: 'Product Update Announcement',
                    subject: 'New Features Available - {{product_name}}',
                    content: `
            <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
              <h1 style="color: #333;">Exciting Updates to {{product_name}}!</h1>
              <p>Hi {{customer_name}},</p>
              <p>We've just released some amazing new features that we think you'll love:</p>
              <div style="background: #f8f9fa; padding: 20px; border-radius: 8px; margin: 20px 0;">
                {{feature_list}}
              </div>
              <div style="text-align: center; margin: 30px 0;">
                <a href="{{update_link}}" style="background: #007bff; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; display: inline-block;">Learn More</a>
              </div>
              <p>Thanks for being a valued customer!</p>
              <p>Best regards,<br>The {{company_name}} Team</p>
            </div>
          `,
                    type: 'marketing',
                    variables: ['customer_name', 'product_name', 'feature_list', 'update_link', 'company_name'],
                    isActive: true,
                    createdAt: '2025-05-22T14:00:00Z',
                    updatedAt: '2025-05-22T14:00:00Z'
                },
                {
                    id: 'order-confirmation',
                    name: 'Order Confirmation',
                    subject: 'Order Confirmation - #{{order_number}}',
                    content: `
            <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
              <h1 style="color: #333;">Order Confirmation</h1>
              <p>Hi {{customer_name}},</p>
              <p>Thank you for your order! Here are the details:</p>
              <div style="background: #f8f9fa; padding: 20px; border-radius: 8px; margin: 20px 0;">
                <h3>Order #{{order_number}}</h3>
                <p><strong>Order Date:</strong> {{order_date}}</p>
                <p><strong>Total Amount:</strong> {{order_total}}</p>
                <div style="margin-top: 15px;">
                  {{order_items}}
                </div>
              </div>
              <p>We'll send you another email when your order ships.</p>
              <p>Best regards,<br>The {{company_name}} Team</p>
            </div>
          `,
                    type: 'transactional',
                    variables: ['customer_name', 'order_number', 'order_date', 'order_total', 'order_items', 'company_name'],
                    isActive: true,
                    createdAt: '2025-05-23T09:00:00Z',
                    updatedAt: '2025-05-23T09:00:00Z'
                }
            ];
            const mockSmsTemplates = [
                {
                    id: 'welcome-sms',
                    name: 'Welcome SMS',
                    content: 'Welcome to {{company_name}}, {{customer_name}}! Your account is ready. Reply STOP to opt out.',
                    type: 'transactional',
                    variables: ['customer_name', 'company_name'],
                    isActive: true,
                    createdAt: '2025-05-20T10:00:00Z',
                    updatedAt: '2025-05-20T10:00:00Z'
                },
                {
                    id: 'flash-sale',
                    name: 'Flash Sale Alert',
                    content: '🔥 FLASH SALE: {{discount}}% off {{product_name}}! Use code {{promo_code}}. Valid until {{expiry}}. Shop now: {{link}}',
                    type: 'marketing',
                    variables: ['discount', 'product_name', 'promo_code', 'expiry', 'link'],
                    isActive: true,
                    createdAt: '2025-05-24T09:00:00Z',
                    updatedAt: '2025-05-24T09:00:00Z'
                },
                {
                    id: 'order-shipped',
                    name: 'Order Shipped',
                    content: 'Great news {{customer_name}}! Your order #{{order_number}} has shipped. Track it here: {{tracking_link}}',
                    type: 'notification',
                    variables: ['customer_name', 'order_number', 'tracking_link'],
                    isActive: true,
                    createdAt: '2025-05-25T11:00:00Z',
                    updatedAt: '2025-05-25T11:00:00Z'
                },
                {
                    id: 'appointment-reminder',
                    name: 'Appointment Reminder',
                    content: 'Reminder: You have an appointment with {{company_name}} on {{appointment_date}} at {{appointment_time}}. Reply CONFIRM to confirm.',
                    type: 'notification',
                    variables: ['company_name', 'appointment_date', 'appointment_time'],
                    isActive: true,
                    createdAt: '2025-05-25T15:00:00Z',
                    updatedAt: '2025-05-25T15:00:00Z'
                }
            ];
            setEmailTemplates(mockEmailTemplates);
            setSmsTemplates(mockSmsTemplates);
        }
        catch (error) {
            console.error('Failed to load templates:', error);
        }
        finally {
            setLoading(false);
        }
    };
    const filteredTemplates = () => {
        const templates = activeTab === 'email' ? emailTemplates : smsTemplates;
        return templates.filter(template => template.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
            template.type.toLowerCase().includes(searchTerm.toLowerCase()));
    };
    const handleCreateNew = () => {
        setFormData({
            name: '',
            subject: activeTab === 'email' ? '' : '',
            content: '',
            type: 'marketing',
            variables: []
        });
        setIsCreating(true);
        setIsEditing(false);
        setSelectedTemplate(null);
    };
    const handleEdit = (template) => {
        setFormData({
            name: template.name,
            subject: 'subject' in template ? template.subject : '',
            content: template.content,
            type: template.type,
            variables: template.variables
        });
        setSelectedTemplate(template);
        setIsEditing(true);
        setIsCreating(false);
    };
    const handleSave = async () => {
        if (!formData.name.trim() || !formData.content.trim()) {
            alert('Please fill in all required fields');
            return;
        }
        try {
            setLoading(true);
            // Mock save logic - replace with actual API call
            const templateData = {
                id: isCreating ? `${activeTab}_${Date.now()}` : selectedTemplate?.id,
                name: formData.name,
                content: formData.content,
                type: formData.type,
                variables: formData.variables,
                isActive: true,
                createdAt: isCreating ? new Date().toISOString() : selectedTemplate?.createdAt,
                updatedAt: new Date().toISOString(),
                ...(activeTab === 'email' && { subject: formData.subject })
            };
            if (activeTab === 'email') {
                if (isCreating) {
                    setEmailTemplates(prev => [...prev, templateData]);
                }
                else {
                    setEmailTemplates(prev => prev.map(t => t.id === templateData.id ? templateData : t));
                }
            }
            else {
                if (isCreating) {
                    setSmsTemplates(prev => [...prev, templateData]);
                }
                else {
                    setSmsTemplates(prev => prev.map(t => t.id === templateData.id ? templateData : t));
                }
            }
            setIsCreating(false);
            setIsEditing(false);
            setSelectedTemplate(null);
        }
        catch (error) {
            console.error('Failed to save template:', error);
            alert('Failed to save template. Please try again.');
        }
        finally {
            setLoading(false);
        }
    };
    const handleDelete = async (templateId) => {
        const confirmed = globalThis.confirm('Are you sure you want to delete this template?');
        if (!confirmed)
            return;
        try {
            if (activeTab === 'email') {
                setEmailTemplates(prev => prev.filter(t => t.id !== templateId));
            }
            else {
                setSmsTemplates(prev => prev.filter(t => t.id !== templateId));
            }
        }
        catch (error) {
            console.error('Failed to delete template:', error);
            alert('Failed to delete template. Please try again.');
        }
    };
    const handleDuplicate = (template) => {
        const duplicatedTemplate = {
            ...template,
            id: `${template.id}_copy_${Date.now()}`,
            name: `${template.name} (Copy)`,
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString()
        };
        if (activeTab === 'email') {
            setEmailTemplates(prev => [...prev, duplicatedTemplate]);
        }
        else {
            setSmsTemplates(prev => [...prev, duplicatedTemplate]);
        }
    };
    const extractVariables = (content) => {
        const regex = /\{\{([^}]+)\}\}/g;
        const variables = new Set();
        let match;
        while ((match = regex.exec(content)) !== null) {
            variables.add(match[1].trim());
        }
        return Array.from(variables);
    };
    const handleContentChange = (content) => {
        setFormData(prev => ({
            ...prev,
            content,
            variables: extractVariables(content)
        }));
    };
    const getTypeColor = (type) => {
        switch (type) {
            case 'marketing': return 'bg-blue-100 text-blue-800';
            case 'transactional': return 'bg-green-100 text-green-800';
            case 'notification': return 'bg-yellow-100 text-yellow-800';
            default: return 'bg-gray-100 text-gray-800';
        }
    };
    if (isCreating || isEditing) {
        return (_jsxs("div", { className: "space-y-6", children: [_jsxs("div", { className: "flex items-center justify-between", children: [_jsxs("div", { className: "flex items-center gap-4", children: [_jsxs(Button, { variant: "ghost", onClick: () => { setIsCreating(false); setIsEditing(false); }, children: [_jsx(ArrowLeft, { className: "h-4 w-4 mr-2" }), "Back to Templates"] }), _jsx("div", { children: _jsxs("h1", { className: "text-3xl font-bold tracking-tight", children: [isCreating ? 'Create' : 'Edit', " ", activeTab.toUpperCase(), " Template"] }) })] }), _jsxs(Button, { onClick: handleSave, disabled: loading, children: [_jsx(Save, { className: "h-4 w-4 mr-2" }), "Save Template"] })] }), _jsxs("div", { className: "grid gap-6 lg:grid-cols-3", children: [_jsx("div", { className: "lg:col-span-2", children: _jsxs(Card, { children: [_jsxs(CardHeader, { children: [_jsx(CardTitle, { children: "Template Details" }), _jsx(CardDescription, { children: "Configure your template settings and content" })] }), _jsxs(CardContent, { className: "space-y-4", children: [_jsxs("div", { children: [_jsx(Label, { htmlFor: "template-name", children: "Template Name" }), _jsx(Input, { id: "template-name", value: formData.name, onChange: (e) => setFormData(prev => ({ ...prev, name: e.target.value })), placeholder: "Enter template name" })] }), _jsxs("div", { children: [_jsx(Label, { htmlFor: "template-type", children: "Template Type" }), _jsxs(Select, { value: formData.type, onValueChange: (value) => setFormData(prev => ({ ...prev, type: value })), children: [_jsx(SelectTrigger, { children: _jsx(SelectValue, {}) }), _jsxs(SelectContent, { children: [_jsx(SelectItem, { value: "marketing", children: "Marketing" }), _jsx(SelectItem, { value: "transactional", children: "Transactional" }), _jsx(SelectItem, { value: "notification", children: "Notification" })] })] })] }), activeTab === 'email' && (_jsxs("div", { children: [_jsx(Label, { htmlFor: "template-subject", children: "Subject Line" }), _jsx(Input, { id: "template-subject", value: formData.subject, onChange: (e) => setFormData(prev => ({ ...prev, subject: e.target.value })), placeholder: "Enter email subject" })] })), _jsxs("div", { children: [_jsx(Label, { htmlFor: "template-content", children: "Content" }), _jsx(Textarea, { id: "template-content", value: formData.content, onChange: (e) => handleContentChange(e.target.value), placeholder: activeTab === 'email' ? 'Enter email content (HTML supported)' : 'Enter SMS content', rows: activeTab === 'email' ? 15 : 6, className: "font-mono" }), _jsx("p", { className: "text-xs text-gray-500 mt-1", children: "Use variables like {{customer_name}} for personalization" })] })] })] }) }), _jsxs("div", { className: "space-y-6", children: [_jsxs(Card, { children: [_jsxs(CardHeader, { children: [_jsx(CardTitle, { children: "Variables" }), _jsx(CardDescription, { children: "Detected template variables" })] }), _jsx(CardContent, { children: formData.variables.length > 0 ? (_jsx("div", { className: "flex flex-wrap gap-2", children: formData.variables.map((variable) => (_jsx(Badge, { variant: "secondary", children: variable }, variable))) })) : (_jsx("p", { className: "text-sm text-gray-500", children: "No variables detected" })) })] }), _jsxs(Card, { children: [_jsxs(CardHeader, { children: [_jsx(CardTitle, { children: "Preview" }), _jsx(CardDescription, { children: "How your template will appear" })] }), _jsx(CardContent, { children: _jsxs("div", { className: "p-3 bg-gray-50 rounded border min-h-[100px] text-sm", children: [activeTab === 'email' && formData.subject && (_jsxs("div", { className: "mb-3 pb-2 border-b", children: [_jsx("strong", { children: "Subject:" }), " ", formData.subject] })), activeTab === 'email' ? (_jsx("div", { dangerouslySetInnerHTML: { __html: formData.content || 'Enter content to see preview...' } })) : (_jsx("div", { className: "whitespace-pre-wrap", children: formData.content || 'Enter content to see preview...' }))] }) })] })] })] })] }));
    }
    return (_jsxs("div", { className: "space-y-6", children: [_jsxs("div", { className: "flex items-center justify-between", children: [_jsxs("div", { className: "flex items-center gap-4", children: [onBack && (_jsxs(Button, { variant: "ghost", onClick: onBack, children: [_jsx(ArrowLeft, { className: "h-4 w-4 mr-2" }), "Back"] })), _jsxs("div", { children: [_jsx("h1", { className: "text-3xl font-bold tracking-tight", children: "Template Manager" }), _jsx("p", { className: "text-gray-600", children: "Create and manage email and SMS templates" })] })] }), _jsxs(Button, { onClick: handleCreateNew, children: [_jsx(Plus, { className: "h-4 w-4 mr-2" }), "Create Template"] })] }), _jsxs("div", { className: "flex items-center gap-4", children: [_jsx(Tabs, { value: activeTab, onValueChange: (value) => setActiveTab(value), children: _jsxs(TabsList, { children: [_jsxs(TabsTrigger, { value: "email", className: "flex items-center gap-2", children: [_jsx(Mail, { className: "h-4 w-4" }), "Email Templates"] }), _jsxs(TabsTrigger, { value: "sms", className: "flex items-center gap-2", children: [_jsx(MessageSquare, { className: "h-4 w-4" }), "SMS Templates"] })] }) }), _jsx("div", { className: "flex-1 max-w-md", children: _jsxs("div", { className: "relative", children: [_jsx(Search, { className: "absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" }), _jsx(Input, { placeholder: "Search templates...", value: searchTerm, onChange: (e) => setSearchTerm(e.target.value), className: "pl-10" })] }) })] }), _jsx("div", { className: "grid gap-4 md:grid-cols-2 lg:grid-cols-3", children: filteredTemplates().map((template) => (_jsxs(Card, { className: "hover:shadow-md transition-shadow", children: [_jsx(CardHeader, { children: _jsxs("div", { className: "flex items-start justify-between", children: [_jsxs("div", { className: "flex-1", children: [_jsx(CardTitle, { className: "text-lg", children: template.name }), _jsx(CardDescription, { className: "mt-1", children: 'subject' in template && template.subject })] }), _jsx(Badge, { className: getTypeColor(template.type), children: template.type })] }) }), _jsx(CardContent, { children: _jsxs("div", { className: "space-y-3", children: [_jsxs("div", { className: "text-sm text-gray-600 line-clamp-3", children: [template.content.substring(0, 150), "..."] }), template.variables.length > 0 && (_jsxs("div", { className: "flex flex-wrap gap-1", children: [template.variables.slice(0, 3).map((variable) => (_jsx(Badge, { variant: "outline", className: "text-xs", children: variable }, variable))), template.variables.length > 3 && (_jsxs(Badge, { variant: "outline", className: "text-xs", children: ["+", template.variables.length - 3, " more"] }))] })), _jsxs("div", { className: "flex items-center justify-between pt-2", children: [_jsxs("span", { className: "text-xs text-gray-500", children: ["Updated ", new Date(template.updatedAt).toLocaleDateString()] }), _jsxs("div", { className: "flex gap-1", children: [_jsx(Button, { variant: "ghost", size: "sm", onClick: () => onSelectTemplate?.(template), children: _jsx(Eye, { className: "h-4 w-4" }) }), _jsx(Button, { variant: "ghost", size: "sm", onClick: () => handleEdit(template), children: _jsx(Edit, { className: "h-4 w-4" }) }), _jsx(Button, { variant: "ghost", size: "sm", onClick: () => handleDuplicate(template), children: _jsx(Copy, { className: "h-4 w-4" }) }), _jsx(Button, { variant: "ghost", size: "sm", onClick: () => handleDelete(template.id), children: _jsx(Trash2, { className: "h-4 w-4" }) })] })] })] }) })] }, template.id))) }), filteredTemplates().length === 0 && (_jsxs("div", { className: "text-center py-12", children: [_jsx(FileText, { className: "h-12 w-12 text-gray-400 mx-auto mb-4" }), _jsx("h3", { className: "text-lg font-medium text-gray-900 mb-2", children: "No templates found" }), _jsx("p", { className: "text-gray-600 mb-4", children: searchTerm ? 'No templates match your search criteria.' : `No ${activeTab} templates available.` }), _jsxs(Button, { onClick: handleCreateNew, children: [_jsx(Plus, { className: "h-4 w-4 mr-2" }), "Create Your First Template"] })] }))] }));
};
export default TemplateManager;
