import React, { createContext, useContext, useEffect, useState } from 'react'
import { authClient } from '@/lib/auth-client'
import type { Session, User } from '@/lib/auth-client'

// Debug logging
const DEBUG = true
const log = (...args: any[]) => DEBUG && console.log('[BetterAuth]:', ...args)

interface AuthContextType {
  user: User | null
  session: Session | null
  isAuthenticated: boolean
  isLoading: boolean
  signIn: (email: string, password: string) => Promise<{ error?: Error }>
  signOut: () => Promise<void>
}

const AuthContext = createContext<AuthContextType | null>(null)

export function BetterAuthProvider({ children }: { children: React.ReactNode }): JSX.Element {
  const [user, setUser] = useState<User | null>(null)
  const [session, setSession] = useState<Session | null>(null)
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    log('Initializing auth state')
    const checkSession = async () => {
      try {
        const result = await authClient.getSession()
        log('Session check result:', result)
        
        if (result?.data?.user) {
          setUser(result.data.user)
          setSession(result.data)
        } else {
          setUser(null)
          setSession(null)
        }
      } catch (error) {
        log('Session check error:', error)
        setUser(null)
        setSession(null)
      } finally {
        setIsLoading(false)
      }
    }

    checkSession()
  }, [])

  const signIn = async (email: string, password: string) => {
    log('Attempting sign in:', email)
    try {
      try {
        // Use the same resolution logic as auth-client.ts
        const baseURL =
          import.meta.env.VITE_BETTER_AUTH_URL ||
          (import.meta.env.DEV
            ? 'http://localhost:3000/api/auth'
            : 'https://nxtdotx.co.za/api/auth');

        const response = await fetch(`${baseURL}/sign-in/email`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({ email, password }),
          credentials: 'include'
        });

        const text = await response.text();
        const data = text ? JSON.parse(text) : {};
        log('Auth response:', { status: response.status, data });

        if (response.ok && data.user) {
          setUser(data.user);
          setSession(data);
          return {};
        }
        return { error: new Error(data.error || 'Invalid credentials') };
      } catch (error) {
        log('Auth error:', error);
        return { error: error as Error };
      }
    } catch (error) {
      log('Sign in error:', error)
      return { error: error as Error }
    }
  }

  const signOut = async () => {
    log('Signing out')
    try {
      await authClient.signOut()
      setUser(null)
      setSession(null)
    } catch (error) {
      log('Sign out error:', error)
      throw error
    }
  }

  const value = {
    user,
    session,
    isAuthenticated: !!user,
    isLoading,
    signIn,
    signOut
  }

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>
}

export function useBetterAuth() {
  const context = useContext(AuthContext)
  if (!context) {
    throw new Error('useBetterAuth must be used within a BetterAuthProvider')
  }
  return context
}
