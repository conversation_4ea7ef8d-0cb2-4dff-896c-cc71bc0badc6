import { betterAuth } from "better-auth";
import { drizzleAdapter } from "better-auth/adapters/drizzle";
import { drizzle } from "drizzle-orm/postgres-js";
import postgres from "postgres";
import { schema } from "../../lib/better-auth-schema";

// Create database connection
const client = postgres(process.env.DATABASE_URL);
const db = drizzle(client);

// Initialize Better Auth
const auth = betterAuth({
  database: drizzleAdapter(db, {
    provider: "pg",
    schema: schema,
  }),
  secret: process.env.BETTER_AUTH_SECRET,
  baseURL: process.env.BETTER_AUTH_URL,
});

export async function POST(request: Request) {
  console.log('🔍 [AUTH API] Incoming POST request:', request.url);
  console.log('Request headers:', request.headers);
  
  if (request.body) {
    let body = await request.json();
    console.log('Request body:', body);
  }

  try {
    const response = await auth.handler(request);
    console.log('Auth response:', response);
    return response;
  } catch (error) {
    console.error('❌ [AUTH API] Error handling request:', error);
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    return new Response(JSON.stringify({ error: errorMessage }), { status: 500 });
  }
}

export async function GET(request: Request) {
  console.log('🔍 [AUTH API] Incoming GET request:', request.url);
  console.log('Request headers:', request.headers);

  try {
    const response = await auth.handler(request);
    console.log('Auth response:', response);
    return response;
  } catch (error) {
    console.error('❌ [AUTH API] Error handling request:', error);
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    return new Response(JSON.stringify({ error: errorMessage }), { status: 500 });
  }
}