import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Book,
  Search,
  Filter,
  Download,
  DollarSign,
  FileText,
  RefreshCw,
  AlertCircle,
  CheckCircle,
  Eye,
  Calculator
} from 'lucide-react';
import {
  GeneralLedgerService,
  type GeneralLedgerEntry,
  type TrialBalanceEntry,
  type AccountBalance
} from '@/services/financial';

const GeneralLedger: React.FC = () => {
  const [entries, setEntries] = useState<GeneralLedgerEntry[]>([]);
  const [trialBalance, setTrialBalance] = useState<TrialBalanceEntry[]>([]);
  const [accountBalances, setAccountBalances] = useState<AccountBalance[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedAccount, setSelectedAccount] = useState<string>('ALL');
  const [startDate, setStartDate] = useState('');
  const [endDate, setEndDate] = useState('');
  const [activeTab, setActiveTab] = useState('entries');

  useEffect(() => {
    loadGeneralLedgerData();
  }, [selectedAccount, startDate, endDate]);

  const loadGeneralLedgerData = async () => {
    try {
      setLoading(true);
      setError(null);

      await Promise.all([
        loadLedgerEntries(),
        loadTrialBalance(),
        loadAccountBalances()
      ]);
    } catch (error) {
      console.error('Error loading general ledger data:', error);
      setError('Failed to load general ledger data. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const loadLedgerEntries = async () => {
    try {
      const data = await GeneralLedgerService.getLedgerEntries('default-org-id', {
        accountId: selectedAccount,
        startDate,
        endDate,
        limit: 1000
      });
      setEntries(data);
    } catch (error) {
      console.error('Error loading ledger entries:', error);
      throw error;
    }
  };

  const loadTrialBalance = async () => {
    try {
      const data = await GeneralLedgerService.getTrialBalance(
        'default-org-id',
        endDate || undefined
      );
      setTrialBalance(data);
    } catch (error) {
      console.error('Error loading trial balance:', error);
      throw error;
    }
  };

  const loadAccountBalances = async () => {
    try {
      const data = await GeneralLedgerService.getAccountBalances(
        'default-org-id',
        endDate || undefined
      );
      setAccountBalances(data);
    } catch (error) {
      console.error('Error loading account balances:', error);
      throw error;
    }
  };

  // Utility functions
  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(amount);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  const getAccountTypeColor = (type: string) => {
    switch (type) {
      case 'Asset':
        return 'bg-blue-100 text-blue-800';
      case 'Liability':
        return 'bg-red-100 text-red-800';
      case 'Equity':
        return 'bg-purple-100 text-purple-800';
      case 'Revenue':
        return 'bg-green-100 text-green-800';
      case 'Expense':
        return 'bg-orange-100 text-orange-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const exportToCSV = (data: any[], filename: string) => {
    if (data.length === 0) return;

    const headers = Object.keys(data[0]);
    const csvContent = [
      headers.join(','),
      ...data.map(row =>
        headers.map(header => {
          const value = row[header];
          return typeof value === 'string' && value.includes(',')
            ? `"${value}"`
            : value;
        }).join(',')
      )
    ].join('\n');

    const blob = new Blob([csvContent], { type: 'text/csv' });
    const url = window.URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = filename;
    link.click();
    window.URL.revokeObjectURL(url);
  };

  // Filter functions
  const filteredEntries = entries.filter(entry => {
    const matchesSearch = searchTerm === '' ||
      entry.account_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      entry.account_code.toLowerCase().includes(searchTerm.toLowerCase()) ||
      entry.description.toLowerCase().includes(searchTerm.toLowerCase());

    return matchesSearch;
  });

  const filteredTrialBalance = trialBalance.filter(entry => {
    const matchesSearch = searchTerm === '' ||
      entry.account_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      entry.account_code.toLowerCase().includes(searchTerm.toLowerCase());

    return matchesSearch;
  });

  const filteredAccountBalances = accountBalances.filter(account => {
    const matchesSearch = searchTerm === '' ||
      account.account_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      account.account_code.toLowerCase().includes(searchTerm.toLowerCase());

    return matchesSearch;
  });

  // Calculate totals for trial balance
  const trialBalanceTotals = filteredTrialBalance.reduce(
    (totals, entry) => ({
      totalDebits: totals.totalDebits + entry.debit_balance,
      totalCredits: totals.totalCredits + entry.credit_balance
    }),
    { totalDebits: 0, totalCredits: 0 }
  );

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="flex flex-col items-center space-y-4">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
          <p className="text-muted-foreground">Loading general ledger data...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex items-center justify-center h-64">
        <Card className="w-full max-w-md">
          <CardContent className="pt-6">
            <div className="flex flex-col items-center space-y-4">
              <AlertCircle className="h-12 w-12 text-red-500" />
              <div className="text-center">
                <h3 className="text-lg font-semibold">Error Loading General Ledger</h3>
                <p className="text-muted-foreground mt-2">{error}</p>
              </div>
              <Button onClick={loadGeneralLedgerData} className="mt-4">
                Try Again
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold tracking-tight flex items-center">
            <Book className="h-8 w-8 mr-3 text-blue-600" />
            General Ledger
          </h1>
          <p className="text-muted-foreground">
            Complete record of all financial transactions and account balances
          </p>
        </div>
        <div className="flex space-x-2">
          <Button variant="outline" onClick={loadGeneralLedgerData}>
            <RefreshCw className="h-4 w-4 mr-2" />
            Refresh Data
          </Button>
          <Button
            variant="outline"
            onClick={() => {
              if (activeTab === 'entries') {
                exportToCSV(filteredEntries, 'general-ledger-entries.csv');
              } else if (activeTab === 'trial-balance') {
                exportToCSV(filteredTrialBalance, 'trial-balance.csv');
              } else {
                exportToCSV(filteredAccountBalances, 'account-balances.csv');
              }
            }}
          >
            <Download className="h-4 w-4 mr-2" />
            Export CSV
          </Button>
        </div>
      </div>

      {/* Filters */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Filter className="h-5 w-5 mr-2" />
            Filters
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid gap-4 md:grid-cols-4">
            <div className="space-y-2">
              <Label htmlFor="search">Search</Label>
              <div className="relative">
                <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                <Input
                  id="search"
                  placeholder="Search accounts, descriptions..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-8"
                />
              </div>
            </div>
            <div className="space-y-2">
              <Label htmlFor="account">Account</Label>
              <Select value={selectedAccount} onValueChange={setSelectedAccount}>
                <SelectTrigger>
                  <SelectValue placeholder="All Accounts" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="ALL">All Accounts</SelectItem>
                  {accountBalances.map((account) => (
                    <SelectItem key={account.account_id} value={account.account_id}>
                      {account.account_code} - {account.account_name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            <div className="space-y-2">
              <Label htmlFor="start-date">Start Date</Label>
              <Input
                id="start-date"
                type="date"
                value={startDate}
                onChange={(e) => setStartDate(e.target.value)}
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="end-date">End Date</Label>
              <Input
                id="end-date"
                type="date"
                value={endDate}
                onChange={(e) => setEndDate(e.target.value)}
              />
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Main Content Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-4">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="entries">Ledger Entries</TabsTrigger>
          <TabsTrigger value="trial-balance">Trial Balance</TabsTrigger>
          <TabsTrigger value="balances">Account Balances</TabsTrigger>
        </TabsList>

        {/* Ledger Entries Tab */}
        <TabsContent value="entries" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center justify-between">
                <span className="flex items-center">
                  <FileText className="h-5 w-5 mr-2" />
                  General Ledger Entries
                </span>
                <Badge variant="secondary">
                  {filteredEntries.length} entries
                </Badge>
              </CardTitle>
              <CardDescription>
                Detailed view of all transactions affecting each account
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="rounded-md border">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Date</TableHead>
                      <TableHead>Account</TableHead>
                      <TableHead>Description</TableHead>
                      <TableHead className="text-right">Debit</TableHead>
                      <TableHead className="text-right">Credit</TableHead>
                      <TableHead className="text-right">Balance</TableHead>
                      <TableHead>Reference</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {filteredEntries.length === 0 ? (
                      <TableRow>
                        <TableCell colSpan={7} className="text-center py-8">
                          <div className="flex flex-col items-center space-y-2">
                            <FileText className="h-8 w-8 text-muted-foreground" />
                            <p className="text-muted-foreground">No ledger entries found</p>
                            <p className="text-sm text-muted-foreground">
                              Try adjusting your filters or date range
                            </p>
                          </div>
                        </TableCell>
                      </TableRow>
                    ) : (
                      filteredEntries.map((entry) => (
                        <TableRow key={entry.id}>
                          <TableCell>{formatDate(entry.transaction_date)}</TableCell>
                          <TableCell>
                            <div className="flex flex-col">
                              <span className="font-medium">{entry.account_code}</span>
                              <span className="text-sm text-muted-foreground">
                                {entry.account_name}
                              </span>
                            </div>
                          </TableCell>
                          <TableCell className="max-w-xs">
                            <div className="truncate" title={entry.description}>
                              {entry.description}
                            </div>
                          </TableCell>
                          <TableCell className="text-right">
                            {entry.debit_amount > 0 ? (
                              <span className="font-medium text-blue-600">
                                {formatCurrency(entry.debit_amount)}
                              </span>
                            ) : (
                              <span className="text-muted-foreground">—</span>
                            )}
                          </TableCell>
                          <TableCell className="text-right">
                            {entry.credit_amount > 0 ? (
                              <span className="font-medium text-green-600">
                                {formatCurrency(entry.credit_amount)}
                              </span>
                            ) : (
                              <span className="text-muted-foreground">—</span>
                            )}
                          </TableCell>
                          <TableCell className="text-right">
                            <span className={`font-medium ${
                              entry.running_balance >= 0 ? 'text-green-600' : 'text-red-600'
                            }`}>
                              {formatCurrency(Math.abs(entry.running_balance))}
                            </span>
                          </TableCell>
                          <TableCell>
                            {entry.reference_type && (
                              <Badge variant="outline" className="text-xs">
                                {entry.reference_type}
                              </Badge>
                            )}
                          </TableCell>
                        </TableRow>
                      ))
                    )}
                  </TableBody>
                </Table>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Trial Balance Tab */}
        <TabsContent value="trial-balance" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center justify-between">
                <span className="flex items-center">
                  <Calculator className="h-5 w-5 mr-2" />
                  Trial Balance
                </span>
                <Badge variant="secondary">
                  {filteredTrialBalance.length} accounts
                </Badge>
              </CardTitle>
              <CardDescription>
                Summary of all account balances to verify debits equal credits
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="rounded-md border">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Account Code</TableHead>
                      <TableHead>Account Name</TableHead>
                      <TableHead>Type</TableHead>
                      <TableHead className="text-right">Debit Balance</TableHead>
                      <TableHead className="text-right">Credit Balance</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {filteredTrialBalance.length === 0 ? (
                      <TableRow>
                        <TableCell colSpan={5} className="text-center py-8">
                          <div className="flex flex-col items-center space-y-2">
                            <Calculator className="h-8 w-8 text-muted-foreground" />
                            <p className="text-muted-foreground">No trial balance data found</p>
                            <p className="text-sm text-muted-foreground">
                              Try adjusting your date range or refresh the data
                            </p>
                          </div>
                        </TableCell>
                      </TableRow>
                    ) : (
                      <>
                        {filteredTrialBalance.map((entry) => (
                          <TableRow key={entry.account_code}>
                            <TableCell className="font-medium">
                              {entry.account_code}
                            </TableCell>
                            <TableCell>{entry.account_name}</TableCell>
                            <TableCell>
                              <Badge className={getAccountTypeColor(entry.account_type)}>
                                {entry.account_type}
                              </Badge>
                            </TableCell>
                            <TableCell className="text-right">
                              {entry.debit_balance > 0 ? (
                                <span className="font-medium text-blue-600">
                                  {formatCurrency(entry.debit_balance)}
                                </span>
                              ) : (
                                <span className="text-muted-foreground">—</span>
                              )}
                            </TableCell>
                            <TableCell className="text-right">
                              {entry.credit_balance > 0 ? (
                                <span className="font-medium text-green-600">
                                  {formatCurrency(entry.credit_balance)}
                                </span>
                              ) : (
                                <span className="text-muted-foreground">—</span>
                              )}
                            </TableCell>
                          </TableRow>
                        ))}
                        {/* Totals Row */}
                        <TableRow className="border-t-2 border-gray-300 bg-gray-50 font-bold">
                          <TableCell colSpan={3} className="text-right">
                            <strong>TOTALS:</strong>
                          </TableCell>
                          <TableCell className="text-right">
                            <span className="font-bold text-blue-600">
                              {formatCurrency(trialBalanceTotals.totalDebits)}
                            </span>
                          </TableCell>
                          <TableCell className="text-right">
                            <span className="font-bold text-green-600">
                              {formatCurrency(trialBalanceTotals.totalCredits)}
                            </span>
                          </TableCell>
                        </TableRow>
                      </>
                    )}
                  </TableBody>
                </Table>
              </div>

              {/* Balance Check */}
              {filteredTrialBalance.length > 0 && (
                <div className="mt-4 p-4 rounded-lg border">
                  <div className="flex items-center justify-between">
                    <span className="font-medium">Balance Check:</span>
                    <div className="flex items-center space-x-2">
                      {Math.abs(trialBalanceTotals.totalDebits - trialBalanceTotals.totalCredits) < 0.01 ? (
                        <>
                          <CheckCircle className="h-5 w-5 text-green-500" />
                          <span className="text-green-600 font-medium">Balanced</span>
                        </>
                      ) : (
                        <>
                          <AlertCircle className="h-5 w-5 text-red-500" />
                          <span className="text-red-600 font-medium">
                            Out of Balance by {formatCurrency(Math.abs(trialBalanceTotals.totalDebits - trialBalanceTotals.totalCredits))}
                          </span>
                        </>
                      )}
                    </div>
                  </div>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        {/* Account Balances Tab */}
        <TabsContent value="balances" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center justify-between">
                <span className="flex items-center">
                  <DollarSign className="h-5 w-5 mr-2" />
                  Account Balances
                </span>
                <Badge variant="secondary">
                  {filteredAccountBalances.length} accounts
                </Badge>
              </CardTitle>
              <CardDescription>
                Current balance for each account as of {endDate || 'today'}
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="rounded-md border">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Account Code</TableHead>
                      <TableHead>Account Name</TableHead>
                      <TableHead>Type</TableHead>
                      <TableHead className="text-right">Balance</TableHead>
                      <TableHead className="text-center">Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {filteredAccountBalances.length === 0 ? (
                      <TableRow>
                        <TableCell colSpan={5} className="text-center py-8">
                          <div className="flex flex-col items-center space-y-2">
                            <DollarSign className="h-8 w-8 text-muted-foreground" />
                            <p className="text-muted-foreground">No account balances found</p>
                            <p className="text-sm text-muted-foreground">
                              Try refreshing the data or check your filters
                            </p>
                          </div>
                        </TableCell>
                      </TableRow>
                    ) : (
                      filteredAccountBalances.map((account) => (
                        <TableRow key={account.account_id}>
                          <TableCell className="font-medium">
                            {account.account_code}
                          </TableCell>
                          <TableCell>{account.account_name}</TableCell>
                          <TableCell>
                            <Badge className={getAccountTypeColor(account.account_type)}>
                              {account.account_type}
                            </Badge>
                          </TableCell>
                          <TableCell className="text-right">
                            <span className={`font-medium ${
                              account.balance >= 0 ? 'text-green-600' : 'text-red-600'
                            }`}>
                              {formatCurrency(Math.abs(account.balance))}
                            </span>
                          </TableCell>
                          <TableCell className="text-center">
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => {
                                setSelectedAccount(account.account_id);
                                setActiveTab('entries');
                              }}
                            >
                              <Eye className="h-4 w-4" />
                            </Button>
                          </TableCell>
                        </TableRow>
                      ))
                    )}
                  </TableBody>
                </Table>
              </div>

              {/* Summary by Account Type */}
              {filteredAccountBalances.length > 0 && (
                <div className="mt-6 grid gap-4 md:grid-cols-2 lg:grid-cols-5">
                  {['Asset', 'Liability', 'Equity', 'Revenue', 'Expense'].map((type) => {
                    const typeAccounts = filteredAccountBalances.filter(acc => acc.account_type === type);
                    const totalBalance = typeAccounts.reduce((sum, acc) => sum + acc.balance, 0);

                    return (
                      <Card key={type}>
                        <CardContent className="pt-4">
                          <div className="text-center">
                            <Badge className={getAccountTypeColor(type)} variant="secondary">
                              {type}
                            </Badge>
                            <div className="mt-2">
                              <div className="text-2xl font-bold">
                                {formatCurrency(Math.abs(totalBalance))}
                              </div>
                              <p className="text-xs text-muted-foreground">
                                {typeAccounts.length} accounts
                              </p>
                            </div>
                          </div>
                        </CardContent>
                      </Card>
                    );
                  })}
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default GeneralLedger;
